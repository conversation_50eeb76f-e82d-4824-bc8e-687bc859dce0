#!/usr/bin/env python3
"""
简化的性能测试脚本
"""
import time
import os
import sys

print("开始简化性能测试...")

# 测试模块导入
print("\n=== 测试模块导入 ===")

modules_to_test = [
    'utils.screen_loader',
    'utils.memory_utils', 
    'utils.performance_monitor'
]

for module_name in modules_to_test:
    start_time = time.time()
    try:
        __import__(module_name)
        import_time = (time.time() - start_time) * 1000
        print(f"✓ {module_name}: {import_time:.2f}ms")
    except ImportError as e:
        print(f"✗ {module_name}: 导入失败 - {e}")
    except Exception as e:
        print(f"✗ {module_name}: 错误 - {e}")

# 测试屏幕加载器
print("\n=== 测试屏幕加载器 ===")
try:
    from utils.screen_loader import ScreenLoader
    loader = ScreenLoader()
    print(f"✓ 屏幕加载器创建成功")
    print(f"✓ 配置的屏幕数量: {len(loader._screen_configs)}")
except Exception as e:
    print(f"✗ 屏幕加载器测试失败: {e}")

# 测试内存工具
print("\n=== 测试内存工具 ===")
try:
    from utils.memory_utils import check_for_circular_references
    start_time = time.time()
    cleaned = check_for_circular_references()
    check_time = (time.time() - start_time) * 1000
    print(f"✓ 循环引用检查: 清理 {cleaned} 个对象，耗时 {check_time:.2f}ms")
except Exception as e:
    print(f"✗ 内存工具测试失败: {e}")

# 测试性能监控器
print("\n=== 测试性能监控器 ===")
try:
    from utils.performance_monitor import get_performance_monitor
    monitor = get_performance_monitor()
    monitor.record_metric("test_metric", 100, "ms")
    avg = monitor.get_average("test_metric")
    print(f"✓ 性能监控器: 记录指标成功，平均值 {avg:.2f}ms")
except Exception as e:
    print(f"✗ 性能监控器测试失败: {e}")

# 测试字体文件
print("\n=== 测试字体文件 ===")
fonts_dir = os.path.join(os.path.dirname(__file__), 'assets', 'fonts')
if os.path.exists(fonts_dir):
    essential_fonts = ["NotoSansSC-Regular.ttf", "NotoSansSC-Medium.ttf"]
    font_count = 0
    for font_file in essential_fonts:
        font_path = os.path.join(fonts_dir, font_file)
        if os.path.exists(font_path) and os.path.getsize(font_path) > 0:
            font_count += 1
            print(f"✓ {font_file}")
        else:
            print(f"✗ {font_file} (不存在或为空)")
    print(f"可用必要字体: {font_count}/{len(essential_fonts)}")
else:
    print("✗ 字体目录不存在")

print("\n测试完成!")
