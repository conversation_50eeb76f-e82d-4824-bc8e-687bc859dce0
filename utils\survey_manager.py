"""
问卷管理模块 - 处理问卷和评估量表的下载、存储和提交
"""
import os
import json
import logging
import threading
import time
import uuid
from datetime import datetime
import traceback

from .cloud_api import CloudAPI

# 设置日志
logger = logging.getLogger(__name__)

class SurveyManager:
    """
    问卷管理器 - 负责问卷和量表的本地存储和云端同步
    """
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls):
        """单例模式获取实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
            return cls._instance
    
    def __init__(self):
        """初始化问卷管理器"""
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 数据存储路径
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.questionnaires_dir = os.path.join(self.data_dir, 'questionnaires')
        self.responses_dir = os.path.join(self.data_dir, 'responses')
        self.sync_queue_dir = os.path.join(self.data_dir, 'sync_queue', 'responses')
        
        # 确保目录存在
        os.makedirs(self.questionnaires_dir, exist_ok=True)
        os.makedirs(self.responses_dir, exist_ok=True)
        os.makedirs(self.sync_queue_dir, exist_ok=True)
        
        # 本地数据库文件
        self.questionnaires_db_file = os.path.join(self.data_dir, 'questionnaires_db.json')
        self.responses_db_file = os.path.join(self.data_dir, 'responses_db.json')
        
        # 初始化本地数据库
        self._init_db()
        
        # 初始化云端API
        self.cloud_api = CloudAPI()
        
        # 启动同步队列处理线程
        threading.Thread(target=self._process_sync_queue_background, daemon=True).start()
    
    def _init_db(self):
        """初始化本地数据库"""
        # 问卷数据库
        if not os.path.exists(self.questionnaires_db_file):
            with open(self.questionnaires_db_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'questionnaires': [],
                    'last_updated': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
        
        # 回答数据库
        if not os.path.exists(self.responses_db_file):
            with open(self.responses_db_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'responses': [],
                    'last_updated': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
    
    def save_questionnaire_metadata(self, questionnaire_metadata):
        """保存问卷元数据
        
        Args:
            questionnaire_metadata (dict): 问卷元数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保有问卷ID
            if 'questionnaire_id' not in questionnaire_metadata:
                questionnaire_metadata['questionnaire_id'] = str(uuid.uuid4())
            
            questionnaire_id = questionnaire_metadata['questionnaire_id']
            
            # 添加更新时间
            questionnaire_metadata['updated_at'] = datetime.now().isoformat()
            
            # 创建问卷目录
            questionnaire_dir = os.path.join(self.questionnaires_dir, questionnaire_id)
            os.makedirs(questionnaire_dir, exist_ok=True)
            
            # 保存元数据
            metadata_file = os.path.join(questionnaire_dir, 'metadata.json')
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(questionnaire_metadata, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_questionnaires_db(questionnaire_id, questionnaire_metadata)
            
            logger.info(f"问卷元数据已保存: {questionnaire_id} - {questionnaire_metadata.get('title', '未知标题')}")
            return True
            
        except Exception as e:
            logger.error(f"保存问卷元数据失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def save_questionnaire_content(self, questionnaire_id, questionnaire_content):
        """保存问卷内容
        
        Args:
            questionnaire_id (str): 问卷ID
            questionnaire_content (dict): 问卷内容
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建问卷目录
            questionnaire_dir = os.path.join(self.questionnaires_dir, questionnaire_id)
            os.makedirs(questionnaire_dir, exist_ok=True)
            
            # 保存内容文件
            content_file = os.path.join(questionnaire_dir, 'content.json')
            with open(content_file, 'w', encoding='utf-8') as f:
                json.dump(questionnaire_content, f, ensure_ascii=False, indent=2)
            
            # 更新元数据的下载状态
            metadata_file = os.path.join(questionnaire_dir, 'metadata.json')
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                metadata['has_content'] = True
                metadata['downloaded_at'] = datetime.now().isoformat()
                
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                
                # 更新数据库
                self._update_questionnaires_db(questionnaire_id, metadata)
            
            logger.info(f"问卷内容已保存: {questionnaire_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存问卷内容失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _update_questionnaires_db(self, questionnaire_id, questionnaire_data):
        """更新问卷数据库
        
        Args:
            questionnaire_id (str): 问卷ID
            questionnaire_data (dict): 问卷数据
        """
        with self._lock:
            try:
                # 读取当前数据库
                with open(self.questionnaires_db_file, 'r', encoding='utf-8') as f:
                    db = json.load(f)
                
                # 检查是否已存在
                existing_index = None
                for i, item in enumerate(db['questionnaires']):
                    if item['questionnaire_id'] == questionnaire_id:
                        existing_index = i
                        break
                
                # 准备基本记录信息
                record_info = {
                    'questionnaire_id': questionnaire_id,
                    'title': questionnaire_data.get('title', ''),
                    'description': questionnaire_data.get('description', ''),
                    'category': questionnaire_data.get('category', ''),
                    'type': questionnaire_data.get('type', ''),
                    'version': questionnaire_data.get('version', ''),
                    'author': questionnaire_data.get('author', ''),
                    'has_content': questionnaire_data.get('has_content', False),
                    'created_at': questionnaire_data.get('created_at', ''),
                    'updated_at': questionnaire_data.get('updated_at', ''),
                    'downloaded_at': questionnaire_data.get('downloaded_at', '')
                }
                
                # 更新或添加记录
                if existing_index is not None:
                    db['questionnaires'][existing_index] = record_info
                else:
                    db['questionnaires'].append(record_info)
                
                # 更新最后更新时间
                db['last_updated'] = datetime.now().isoformat()
                
                # 保存数据库
                with open(self.questionnaires_db_file, 'w', encoding='utf-8') as f:
                    json.dump(db, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                logger.error(f"更新问卷数据库失败: {str(e)}")
                logger.error(traceback.format_exc())
    
    def get_questionnaire_list(self, category=None):
        """获取问卷列表
        
        Args:
            category (str, optional): 问卷类别
            
        Returns:
            list: 问卷列表
        """
        try:
            # 读取数据库
            with open(self.questionnaires_db_file, 'r', encoding='utf-8') as f:
                db = json.load(f)
            
            # 筛选问卷
            questionnaires = db['questionnaires']
            
            if category:
                questionnaires = [q for q in questionnaires if q['category'] == category]
            
            # 按更新时间倒序排序
            questionnaires.sort(key=lambda x: x.get('updated_at', ''), reverse=True)
            
            return questionnaires
            
        except Exception as e:
            logger.error(f"获取问卷列表失败: {str(e)}")
            return []
    
    def get_questionnaire_content(self, questionnaire_id):
        """获取问卷内容
        
        Args:
            questionnaire_id (str): 问卷ID
            
        Returns:
            dict: 问卷内容，不存在返回None
        """
        try:
            # 检查内容文件是否存在
            content_file = os.path.join(self.questionnaires_dir, questionnaire_id, 'content.json')
            if not os.path.exists(content_file):
                logger.warning(f"问卷内容不存在: {questionnaire_id}")
                return None
            
            # 读取内容
            with open(content_file, 'r', encoding='utf-8') as f:
                content = json.load(f)
            
            return content
            
        except Exception as e:
            logger.error(f"获取问卷内容失败: {str(e)}")
            return None
    
    def save_questionnaire_response(self, response_data):
        """保存问卷回答
        
        Args:
            response_data (dict): 问卷回答数据
            
        Returns:
            str: 回答ID，失败返回None
        """
        try:
            # 生成回答ID
            response_id = str(uuid.uuid4())
            
            # 添加回答ID和时间戳
            response_data['response_id'] = response_id
            
            if 'created_at' not in response_data:
                response_data['created_at'] = datetime.now().isoformat()
            response_data['updated_at'] = datetime.now().isoformat()
            
            # 添加同步状态
            response_data['sync_status'] = 'pending'
            
            # 保存到文件
            file_path = os.path.join(self.responses_dir, f"{response_id}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_responses_db(response_id, response_data)
            
            logger.info(f"问卷回答已保存，ID: {response_id}")
            return response_id
            
        except Exception as e:
            logger.error(f"保存问卷回答失败: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def _update_responses_db(self, response_id, response_data):
        """更新回答数据库
        
        Args:
            response_id (str): 回答ID
            response_data (dict): 回答数据
        """
        with self._lock:
            try:
                # 读取当前数据库
                with open(self.responses_db_file, 'r', encoding='utf-8') as f:
                    db = json.load(f)
                
                # 检查是否已存在
                existing_index = None
                for i, item in enumerate(db['responses']):
                    if item['response_id'] == response_id:
                        existing_index = i
                        break
                
                # 准备基本记录信息
                record_info = {
                    'response_id': response_id,
                    'questionnaire_id': response_data.get('questionnaire_id', ''),
                    'user_id': response_data.get('user_id', ''),
                    'created_at': response_data.get('created_at', ''),
                    'updated_at': response_data.get('updated_at', ''),
                    'submitted_at': response_data.get('submitted_at', ''),
                    'sync_status': response_data.get('sync_status', 'pending'),
                    'cloud_response_id': response_data.get('cloud_response_id', '')
                }
                
                # 更新或添加记录
                if existing_index is not None:
                    db['responses'][existing_index] = record_info
                else:
                    db['responses'].append(record_info)
                
                # 更新最后更新时间
                db['last_updated'] = datetime.now().isoformat()
                
                # 保存数据库
                with open(self.responses_db_file, 'w', encoding='utf-8') as f:
                    json.dump(db, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                logger.error(f"更新回答数据库失败: {str(e)}")
                logger.error(traceback.format_exc())
    
    def get_response(self, response_id):
        """获取问卷回答
        
        Args:
            response_id (str): 回答ID
            
        Returns:
            dict: 回答数据，不存在返回None
        """
        try:
            # 检查文件是否存在
            file_path = os.path.join(self.responses_dir, f"{response_id}.json")
            if not os.path.exists(file_path):
                logger.warning(f"问卷回答不存在: {response_id}")
                return None
            
            # 读取数据
            with open(file_path, 'r', encoding='utf-8') as f:
                response_data = json.load(f)
            
            return response_data
            
        except Exception as e:
            logger.error(f"获取问卷回答失败: {str(e)}")
            return None
    
    def get_user_responses(self, user_id, questionnaire_id=None):
        """获取用户的问卷回答列表
        
        Args:
            user_id (str): 用户ID
            questionnaire_id (str, optional): 问卷ID
            
        Returns:
            list: 回答列表
        """
        try:
            # 读取数据库
            with open(self.responses_db_file, 'r', encoding='utf-8') as f:
                db = json.load(f)
            
            # 筛选回答
            responses = db['responses']
            
            # 根据用户ID筛选
            responses = [r for r in responses if r['user_id'] == user_id]
            
            # 根据问卷ID筛选
            if questionnaire_id:
                responses = [r for r in responses if r['questionnaire_id'] == questionnaire_id]
            
            # 按提交时间倒序排序
            responses.sort(key=lambda x: x.get('submitted_at', ''), reverse=True)
            
            return responses
            
        except Exception as e:
            logger.error(f"获取用户问卷回答列表失败: {str(e)}")
            return []
    
    def update_response_sync_status(self, response_id, cloud_response_id, score=None):
        """更新回答同步状态
        
        Args:
            response_id (str): 本地回答ID
            cloud_response_id (str): 云端回答ID
            score (dict, optional): 评分结果
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取回答数据
            response_data = self.get_response(response_id)
            if not response_data:
                logger.warning(f"更新同步状态失败，回答不存在: {response_id}")
                return False
            
            # 更新同步状态
            response_data['sync_status'] = 'synced'
            response_data['cloud_response_id'] = cloud_response_id
            response_data['updated_at'] = datetime.now().isoformat()
            
            # 添加评分结果
            if score:
                response_data['score'] = score
            
            # 保存回文件
            file_path = os.path.join(self.responses_dir, f"{response_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_responses_db(response_id, response_data)
            
            logger.info(f"问卷回答已标记为已同步，本地ID: {response_id}，云端ID: {cloud_response_id}")
            return True
            
        except Exception as e:
            logger.error(f"标记问卷回答同步状态失败: {str(e)}")
            return False
    
    def add_response_to_sync_queue(self, response_data):
        """添加回答到同步队列
        
        Args:
            response_data (dict): 回答数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保有回答ID
            if 'response_id' not in response_data:
                response_data['response_id'] = str(uuid.uuid4())
            
            response_id = response_data['response_id']
            
            # 保存到队列
            queue_file = os.path.join(self.sync_queue_dir, f"{response_id}.json")
            
            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'response_data': response_data,
                    'created_at': datetime.now().isoformat(),
                    'retries': 0
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"问卷回答已添加到同步队列，ID: {response_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加问卷回答到同步队列失败: {str(e)}")
            return False
    
    def _process_sync_queue_background(self):
        """在后台处理同步队列"""
        # 等待应用初始化
        time.sleep(10)
        
        logger.info("开始同步队列处理线程")
        
        while True:
            try:
                # 检查是否已认证
                if not self.cloud_api.is_authenticated():
                    logger.debug("云API未认证，等待下一次同步周期")
                    time.sleep(600)  # 10分钟后重试
                    continue
                    
                # 处理队列
                success_count, failed_count = self._process_sync_queue()
                
                if success_count > 0 or failed_count > 0:
                    logger.info(f"同步队列处理完成: {success_count}个成功, {failed_count}个失败")
                    
                # 等待下一次处理
                time.sleep(300)  # 5分钟后重试
                
            except Exception as e:
                logger.error(f"同步队列处理线程异常: {str(e)}")
                logger.error(traceback.format_exc())
                time.sleep(600)  # 发生错误后等待10分钟再次尝试
    
    def _process_sync_queue(self):
        """处理同步队列
        
        Returns:
            tuple: (成功数, 失败数)
        """
        success_count = 0
        failed_count = 0
        
        try:
            # 获取队列文件
            queue_files = []
            for file in os.listdir(self.sync_queue_dir):
                if file.endswith('.json'):
                    queue_files.append(file)
            
            if not queue_files:
                return 0, 0
            
            logger.info(f"找到{len(queue_files)}个待同步的问卷回答")
            
            # 逐个处理
            for file in queue_files:
                file_path = os.path.join(self.sync_queue_dir, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        queue_item = json.load(f)
                    
                    response_data = queue_item.get('response_data', {})
                    retries = queue_item.get('retries', 0)
                    response_id = response_data.get('response_id')
                    
                    if not response_id:
                        logger.warning(f"队列项缺少response_id，跳过: {file}")
                        os.remove(file_path)
                        continue
                    
                    logger.info(f"正在同步问卷回答: {response_id}")
                    
                    # 提交到云端
                    result = self.cloud_api.submit_questionnaire_response(response_data)
                    
                    if result and result.get('success'):
                        # 同步成功
                        cloud_response_id = result.get('response_id')
                        score = result.get('score')
                        
                        if cloud_response_id:
                            # 更新同步状态
                            self.update_response_sync_status(response_id, cloud_response_id, score)
                            
                            # 删除队列项
                            os.remove(file_path)
                            
                            success_count += 1
                            logger.info(f"问卷回答同步成功: {response_id} -> {cloud_response_id}")
                        else:
                            # 缺少云端ID
                            failed_count += 1
                            logger.warning(f"问卷回答同步异常，缺少云端ID: {response_id}")
                            
                            # 更新重试次数并保存
                            queue_item['retries'] = retries + 1
                            queue_item['last_attempt'] = datetime.now().isoformat()
                            queue_item['last_error'] = "缺少云端回答ID"
                            
                            with open(file_path, 'w', encoding='utf-8') as f:
                                json.dump(queue_item, f, ensure_ascii=False, indent=2)
                    else:
                        # 同步失败
                        failed_count += 1
                        error = result.get('error', '未知错误') if result else '未知错误'
                        logger.warning(f"问卷回答同步失败: {response_id}, 错误: {error}")
                        
                        # 检查重试次数
                        if retries >= 5:
                            # 超过最大重试次数，放弃
                            logger.error(f"问卷回答同步失败次数过多，放弃: {response_id}")
                            
                            # 将记录标记为同步失败
                            response_data = self.get_response(response_id)
                            if response_data:
                                response_data['sync_status'] = 'failed'
                                response_data['sync_error'] = error
                                response_data['updated_at'] = datetime.now().isoformat()
                                
                                # 保存回文件
                                response_file = os.path.join(self.responses_dir, f"{response_id}.json")
                                with open(response_file, 'w', encoding='utf-8') as f:
                                    json.dump(response_data, f, ensure_ascii=False, indent=2)
                                
                                # 更新数据库
                                self._update_responses_db(response_id, response_data)
                            
                            # 删除队列项
                            os.remove(file_path)
                        else:
                            # 更新重试次数并保存
                            queue_item['retries'] = retries + 1
                            queue_item['last_attempt'] = datetime.now().isoformat()
                            queue_item['last_error'] = error
                            
                            with open(file_path, 'w', encoding='utf-8') as f:
                                json.dump(queue_item, f, ensure_ascii=False, indent=2)
                        
                    # 每次同步后适当延迟，避免过度请求
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"处理同步队列项时出错: {file}, {str(e)}")
                    logger.error(traceback.format_exc())
                    failed_count += 1
                    
                    try:
                        # 更新重试信息
                        with open(file_path, 'r', encoding='utf-8') as f:
                            queue_item = json.load(f)
                        
                        queue_item['retries'] = queue_item.get('retries', 0) + 1
                        queue_item['last_attempt'] = datetime.now().isoformat()
                        queue_item['last_error'] = str(e)
                        
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(queue_item, f, ensure_ascii=False, indent=2)
                    except:
                        pass
        
        except Exception as e:
            logger.error(f"处理同步队列时出错: {str(e)}")
            logger.error(traceback.format_exc())
        
        return success_count, failed_count

    def list_available_questionnaires(self, user_id=None):
        """列出可用问卷/量表，支持按用户过滤"""
        questionnaires = []
        for file in os.listdir(self.questionnaires_dir):
            if file.endswith('.json'):
                with open(os.path.join(self.questionnaires_dir, file), 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    questionnaires.append(data)
        return questionnaires

    def list_user_responses(self, user_id=None):
        """列出当前用户的历史答卷"""
        responses = []
        for file in os.listdir(self.responses_dir):
            if file.endswith('.json'):
                with open(os.path.join(self.responses_dir, file), 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if not user_id or data.get('user_id') == user_id:
                        responses.append(data)
        return responses

    def submit_response(self, response_data, user_id=None):
        """提交问卷答卷，自动带上user_id"""
        try:
            if user_id:
                response_data['user_id'] = user_id
            response_id = response_data.get('response_id') or str(uuid.uuid4())
            response_data['response_id'] = response_id
            response_data['submitted_at'] = datetime.now().isoformat()
            file_path = os.path.join(self.responses_dir, f"{response_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
            # 加入同步队列
            # ...可扩展云端同步逻辑...
            return True
        except Exception as e:
            logger.error(f"提交问卷答卷失败: {e}")
            return False

# 全局获取函数
def get_survey_manager():
    """获取问卷管理器实例"""
    return SurveyManager.get_instance()