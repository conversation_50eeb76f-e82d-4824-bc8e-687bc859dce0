#!/usr/bin/env python3
"""
移动端与云端API通信测试脚本

使用说明：
1. 修改BASE_URL为您的后端服务地址
2. 确保有可用的测试账号
3. 运行脚本测试各API端点

此脚本测试所有移动端API兼容的端点
"""

import os
import json
import time
import requests
from pprint import pprint

# 配置参数
BASE_URL = "http://localhost:3000"  # 前端服务地址，会通过代理转发到后端
TEST_USERNAME = "admin"  # 修改为您的测试用户名
TEST_PASSWORD = "admin123"  # 修改为您的测试密码

# 可选参数，保存响应到文件
SAVE_RESPONSES = True
RESPONSES_DIR = "api_test_responses"

# 创建保存响应的目录
if SAVE_RESPONSES and not os.path.exists(RESPONSES_DIR):
    os.makedirs(RESPONSES_DIR)

def save_response(name, data):
    """保存API响应到文件"""
    if not SAVE_RESPONSES:
        return
    
    filename = os.path.join(RESPONSES_DIR, f"{name}_{int(time.time())}.json")
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"响应已保存到 {filename}")

def print_separator(title):
    """打印分隔线和标题"""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def test_login():
    """测试登录API"""
    print_separator("测试登录API")
    
    url = f"{BASE_URL}/api/login"
    data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    try:
        print(f"POST {url}")
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("登录成功!")
            pprint(result)
            save_response("login", result)
            
            # 返回访问令牌
            return result.get("access_token")
        else:
            print("登录失败!")
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return None

def test_get_health_records(token):
    """测试获取健康记录API"""
    print_separator("测试获取健康记录API")
    
    url = f"{BASE_URL}/api/health-records/health-records"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"GET {url}")
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("获取健康记录成功!")
            pprint(result)
            save_response("health_records", result)
            
            # 返回记录ID (如果有)
            record_id = None
            if result.get("items") and len(result["items"]) > 0:
                record_id = result["items"][0]["id"]
            return record_id
        else:
            print("获取健康记录失败!")
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return None

def test_create_health_record(token):
    """测试创建健康记录API"""
    print_separator("测试创建健康记录API")
    
    url = f"{BASE_URL}/api/health-records/health-records"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "user_id": 1,  # 通常服务器会忽略这个，使用token中的用户ID
        "record_type": "basic_info",
        "record_value": "血压: 120/80",
        "record_unit": "mmHg",
        "notes": "晨起测量，状态良好"
    }
    
    try:
        print(f"POST {url}")
        response = requests.post(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("创建健康记录成功!")
            pprint(result)
            save_response("create_health_record", result)
            return result.get("id")
        else:
            print("创建健康记录失败!")
            print(f"错误: {response.text}")
            return None
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return None

def test_get_record_detail(token, record_id):
    """测试获取健康记录详情API"""
    print_separator(f"测试获取健康记录详情API (ID: {record_id})")
    
    url = f"{BASE_URL}/api/health-records/health-records/{record_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"GET {url}")
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("获取健康记录详情成功!")
            pprint(result)
            save_response(f"health_record_{record_id}", result)
            return True
        else:
            print("获取健康记录详情失败!")
            print(f"错误: {response.text}")
            return False
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return False

def test_update_health_record(token, record_id):
    """测试更新健康记录API"""
    print_separator(f"测试更新健康记录API (ID: {record_id})")
    
    url = f"{BASE_URL}/api/health-records/health-records/{record_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "record_value": "血压: 122/78",
        "notes": "测试更新API，时间：" + time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        print(f"PUT {url}")
        response = requests.put(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("更新健康记录成功!")
            pprint(result)
            save_response(f"update_health_record_{record_id}", result)
            return True
        else:
            print("更新健康记录失败!")
            print(f"错误: {response.text}")
            return False
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return False

def test_delete_health_record(token, record_id):
    """测试删除健康记录API"""
    print_separator(f"测试删除健康记录API (ID: {record_id})")
    
    url = f"{BASE_URL}/api/health-records/health-records/{record_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"DELETE {url}")
        response = requests.delete(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            print("删除健康记录成功!")
            try:
                result = response.json()
                pprint(result)
                save_response(f"delete_health_record_{record_id}", result)
            except:
                print("删除成功，服务器返回空响应")
            return True
        else:
            print("删除健康记录失败!")
            print(f"错误: {response.text}")
            return False
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return False

def test_get_user_profile(token):
    """测试获取用户资料API"""
    print_separator("测试获取用户资料API")
    
    url = f"{BASE_URL}/api/users/mobile-profile"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        print(f"GET {url}")
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("获取用户资料成功!")
            pprint(result)
            save_response("user_profile", result)
            return True
        else:
            print("获取用户资料失败!")
            print(f"错误: {response.text}")
            return False
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return False

def test_update_user_profile(token):
    """测试更新用户资料API"""
    print_separator("测试更新用户资料API")
    
    url = f"{BASE_URL}/api/users/mobile-profile"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "full_name": f"测试用户 {time.strftime('%Y-%m-%d')}"
    }
    
    try:
        print(f"PUT {url}")
        response = requests.put(url, headers=headers, json=data)
        print(f"状态码: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("更新用户资料成功!")
            pprint(result)
            save_response("update_profile", result)
            return True
        else:
            print("更新用户资料失败!")
            print(f"错误: {response.text}")
            return False
    except Exception as e:
        print(f"请求出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("\n" + "*" * 100)
    print(f" 移动端API兼容性测试 - 目标: {BASE_URL} ".center(100, "*"))
    print("*" * 100 + "\n")
    
    # 测试登录
    token = test_login()
    if not token:
        print("\n登录失败，无法继续测试其他API")
        return
    
    # 测试用户资料API
    test_get_user_profile(token)
    test_update_user_profile(token)
    
    # 测试健康记录API
    existing_record_id = test_get_health_records(token)
    
    # 创建新记录
    new_record_id = test_create_health_record(token)
    if new_record_id:
        # 测试记录详情
        test_get_record_detail(token, new_record_id)
        
        # 测试更新记录
        test_update_health_record(token, new_record_id)
        
        # 测试删除记录
        test_delete_health_record(token, new_record_id)
    
    print("\n" + "*" * 100)
    print(" 测试完成 ".center(100, "*"))
    print("*" * 100 + "\n")

if __name__ == "__main__":
    main()
