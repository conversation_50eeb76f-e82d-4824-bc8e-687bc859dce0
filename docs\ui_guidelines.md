# UI界面开发指南

## Logo使用规范

为了保持应用界面的一致性和避免重复Logo的问题，请遵循以下规范：

### 1. 统一使用KV语言定义Logo

- 在所有界面的KV语言定义中添加`HealthLogo`组件
- **不要**在Python代码中通过`add_logo_to_layout`函数添加Logo

### 2. 正确的Logo使用方式

```kv
# 在KV语言中正确使用Logo的示例
BoxLayout:
    size_hint_y: None
    height: dp(150)
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    # 使用统一的HealthLogo组件
    HealthLogo:
        id: health_logo
```

### 3. 避免的做法

```python
# 不要在Python代码中这样做
def init_ui(self, dt=0):
    logo_container = self.ids.logo_container
    add_logo_to_layout(logo_container)  # 不要这样使用！
```

### 4. Logo位置和样式

- Logo应该放置在界面的顶部
- 每个界面只应该显示一个Logo
- 保持Logo的大小和样式一致

### 5. 修改现有代码

如果你发现在Python代码中使用了`add_logo_to_layout`函数，请将其注释掉，确保只使用KV语言中定义的Logo。

## 其他UI规范

### 1. 颜色和主题

- 使用`theme.py`中定义的颜色和样式
- 不要硬编码颜色值

### 2. 字体和文本

- 使用`theme.py`中定义的字体样式
- 保持文本大小和样式的一致性

### 3. 布局和间距

- 使用一致的间距和边距
- 遵循设计规范中的布局指南
