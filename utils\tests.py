#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试脚本 - 用于测试云API连接
"""

import os
import sys
import logging
import time

# 设置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 添加上级目录到模块搜索路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入云API模块
from utils.cloud_api import get_cloud_api

def test_server_connectivity():
    """测试服务器连通性"""
    logger = logging.getLogger("连通性测试")
    logger.info("开始服务器连通性测试")
    
    # 获取CloudAPI实例
    cloud_api = get_cloud_api()
    
    # 检查服务器健康状态
    health = cloud_api.check_server_health()
    
    logger.info(f"服务器在线状态: {health['online']}")
    logger.info(f"状态消息: {health['message']}")
    
    # 打印每个服务器的状态
    for server in health['servers']:
        logger.info(f"服务器: {server['name']} ({server['url']})")
        logger.info(f"  - 在线状态: {server['online']}")
        logger.info(f"  - 状态消息: {server['message']}")
    
    # 返回当前使用的服务器
    logger.info(f"当前使用的服务器: {cloud_api.current_server_name} ({cloud_api.base_url})")
    
    return health['online']

def test_api_endpoints():
    """测试API端点访问"""
    logger = logging.getLogger("API端点测试")
    logger.info("开始API端点测试")
    
    # 获取CloudAPI实例
    cloud_api = get_cloud_api()
    
    # 测试各个端点
    endpoints = [
        "/auth/login",
        "/users/me",
        "/users",
        "/templates/assessment-templates",
        "/templates/questionnaire-templates",
        "/users/mobile-users"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        logger.info(f"测试端点: {endpoint}")
        try:
            # 只进行GET请求测试，不传递认证信息
            response = cloud_api._make_request(
                method="GET",
                endpoint=endpoint,
                bypass_auth=True,  # 不使用认证，仅测试连通性
                max_retries=1
            )
            
            # 记录结果
            status = "成功" if response and isinstance(response, dict) else "失败"
            status_code = response.get("status_code", "未知") if isinstance(response, dict) else "未知"
            message = response.get("message", "无消息") if isinstance(response, dict) else str(response)
            
            results[endpoint] = {
                "status": status,
                "status_code": status_code,
                "message": message
            }
            
            logger.info(f"端点 {endpoint} 测试结果: {status}")
            if status == "失败":
                logger.warning(f"失败原因: {message}")
        except Exception as e:
            logger.error(f"端点 {endpoint} 测试出错: {str(e)}")
            results[endpoint] = {
                "status": "错误",
                "message": str(e)
            }
    
    return results

if __name__ == "__main__":
    # 测试服务器连通性
    server_online = test_server_connectivity()
    
    # 如果服务器在线，测试API端点
    if server_online:
        print("\n===============================")
        print("服务器在线，测试API端点")
        print("===============================\n")
        
        api_results = test_api_endpoints()
        
        # 打印测试结果摘要
        print("\n===============================")
        print("API端点测试结果摘要")
        print("===============================")
        
        for endpoint, result in api_results.items():
            print(f"端点: {endpoint}")
            print(f"状态: {result['status']}")
            if 'status_code' in result:
                print(f"状态码: {result['status_code']}")
            print(f"消息: {result['message'][:100]}...")
            print("-------------------------------")
    else:
        print("\n所有服务器都离线，跳过API端点测试\n")
    
    print("测试完成") 