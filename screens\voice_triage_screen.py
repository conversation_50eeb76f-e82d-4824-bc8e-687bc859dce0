import os
import threading
import time
from datetime import datetime
import wave
import pyaudio
from kivy.app import App
from kivy.clock import Clock
from kivy.core.window import Window
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.image import Image
from kivy.uix.label import Label
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.screenmanager import Screen
from kivy.animation import Animation
from kivy.garden.iconfonts import icon
from kivy.lang import Builder
from kivymd.uix.button import MDIconButton
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel

from api.api_client import APIClient
from utils.triage_manager import get_triage_manager
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件
from theme import AppTheme, AppMetrics
from utils.user_manager import get_user_manager

# 使用KV语言定义UI
Builder.load_string('''
<MessageBubble>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    padding: dp(8)
    spacing: dp(4)
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_COLOR if self.is_user else app.theme.ACCENT_COLOR
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [dp(12)]

    MDLabel:
        id: message_text
        text: root.message_text
        size_hint_y: None
        height: self.texture_size[1]
        padding: dp(10), dp(5)
        color: [1, 1, 1, 1]
        font_size: '14sp'

    MDLabel:
        id: timestamp
        text: root.timestamp
        size_hint_y: None
        height: self.texture_size[1]
        font_size: '10sp'
        color: [0.9, 0.9, 0.9, 0.8]
        halign: 'right'

<VoiceWaveIndicator>:
    size_hint: None, None
    size: dp(120), dp(60)
    canvas:
        Color:
            rgba: app.theme.PRIMARY_COLOR
        Line:
            points: root.wave_points
            width: dp(1.5)
            cap: 'round'
            joint: 'round'

<VoiceTriageScreen>:
    BoxLayout:
        orientation: 'vertical'

        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                Rectangle:
                    pos: self.pos
                    size: self.size

            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo

        # 功能标题栏
        BoxLayout:
            size_hint_y: None
            height: dp(56)
            padding: dp(8)
            spacing: dp(8)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_DARK_COLOR
                Rectangle:
                    pos: self.pos
                    size: self.size

            MDIconButton:
                icon: "arrow-left"
                pos_hint: {"center_y": .5}
                on_release: root.go_back()

            MDLabel:
                text: "语音智能分诊"
                font_size: '18sp'
                font_name: "NotoSansBold"
                halign: "center"
                valign: "middle"
                size_hint_x: 1

            MDIconButton:
                icon: "information-outline"
                pos_hint: {"center_y": .5}
                on_release: root.show_info()

        # 消息区域
        ScrollView:
            id: chat_scroll
            do_scroll_x: False
            bar_width: dp(4)

            GridLayout:
                id: message_area
                cols: 1
                size_hint_y: None
                height: self.minimum_height
                padding: dp(10)
                spacing: dp(15)

        # 底部控制区域
        BoxLayout:
            size_hint_y: None
            height: dp(130)
            orientation: 'vertical'
            padding: dp(8)
            spacing: dp(5)
            canvas.before:
                Color:
                    rgba: [0.95, 0.95, 0.95, 1]
                Rectangle:
                    pos: self.pos
                    size: self.size

            # 语音波形显示
            BoxLayout:
                id: wave_container
                size_hint_y: None
                height: dp(60)
                orientation: 'horizontal'
                padding: dp(10)

                VoiceWaveIndicator:
                    id: wave_indicator
                    opacity: 1 if root.is_recording else 0

            # 控制按钮
            BoxLayout:
                size_hint_y: None
                height: dp(60)
                spacing: dp(10)
                padding: dp(10)

                MDIconButton:
                    icon: "text"
                    theme_icon_color: "Custom"
                    icon_color: app.theme.PRIMARY_COLOR
                    md_bg_color: [0.9, 0.9, 0.9, 1]
                    size_hint: None, None
                    size: dp(48), dp(48)
                    on_release: root.toggle_text_input()

                Button:
                    id: record_button
                    text: "按住说话" if not root.is_recording else "松开发送"
                    size_hint: 1, None
                    height: dp(48)
                    background_color: app.theme.PRIMARY_COLOR if not root.is_recording else [1, 0.3, 0.3, 1]
                    on_press: root.start_recording()
                    on_release: root.stop_recording()

                MDIconButton:
                    icon: "file-document-outline"
                    theme_icon_color: "Custom"
                    icon_color: app.theme.PRIMARY_COLOR
                    md_bg_color: [0.9, 0.9, 0.9, 1]
                    size_hint: None, None
                    size: dp(48), dp(48)
                    on_release: root.show_summary()
''')

class MessageBubble(BoxLayout):
    """聊天消息气泡组件"""
    message_text = StringProperty('')
    timestamp = StringProperty('')
    is_user = BooleanProperty(False)

class VoiceWaveIndicator(BoxLayout):
    """语音波形指示器"""
    wave_points = ObjectProperty([])
    amplitude = NumericProperty(0)

    def __init__(self, **kwargs):
        super(VoiceWaveIndicator, self).__init__(**kwargs)
        self._generate_wave_points()
        Clock.schedule_interval(self._update_wave, 0.1)

    def _generate_wave_points(self):
        """生成初始波形点"""
        width, height = self.size
        center_y = height / 2
        num_points = 30
        spacing = width / num_points

        points = []
        for i in range(num_points + 1):
            x = i * spacing
            y = center_y
            points.extend([x, y])

        self.wave_points = points

    def _update_wave(self, dt):
        """更新波形"""
        if not self.parent:
            return

        width, height = self.size
        center_y = height / 2
        num_points = len(self.wave_points) // 2

        new_points = []
        for i in range(num_points):
            x = self.wave_points[i*2]
            # 生成随机波形，振幅随麦克风音量变化
            import random
            amplitude = min(1.0, self.amplitude * (1 + random.uniform(-0.2, 0.2)))
            y_offset = amplitude * height * 0.4 * random.uniform(-1, 1)
            y = center_y + y_offset
            new_points.extend([x, y])

        self.wave_points = new_points

class VoiceTriageScreen(Screen):
    """语音分诊屏幕"""
    current_session_id = StringProperty("")
    is_recording = BooleanProperty(False)
    recording_time = NumericProperty(0)

    def __init__(self, **kwargs):
        super(VoiceTriageScreen, self).__init__(**kwargs)
        self.api_client = APIClient()
        self.triage_manager = get_triage_manager()
        self.chat_history = []
        self.recording_thread = None
        self.stream = None
        self.audio = None
        self.frames = []

        # 配置PyAudio参数
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.chunk = 1024

        # 在屏幕初始化时创建新会话
        Clock.schedule_once(self._initialize_session, 0.5)

    def on_pre_enter(self, *args):
        self.current_user = get_user_manager().get_current_user()
        if not self.current_user:
            self.show_login_required()
            return

    def show_login_required(self):
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        dialog = MDDialog(title="请先登录", text="请登录后再进行语音分诊。", buttons=[MDFlatButton(text="确定", on_release=lambda x: dialog.dismiss())])
        dialog.open()

    def _initialize_session(self, dt):
        """初始化分诊会话"""
        result = self.api_client.start_voice_triage()
        if result.get('success'):
            self.current_session_id = result.get('session_id')
            self._add_system_message("您好，我是智能分诊助手，请问您有什么不适需要咨询？")
        else:
            self._add_system_message("系统初始化失败，请稍后重试")

    def on_enter(self):
        """进入屏幕时加载历史消息"""
        if self.current_session_id:
            self._load_chat_history()

    def on_leave(self):
        """离开屏幕时停止录音"""
        if self.is_recording:
            self.stop_recording()

    def _load_chat_history(self):
        """加载历史聊天消息"""
        if not self.current_session_id:
            return

        messages = self.triage_manager.get_session_messages(self.current_session_id)

        # 清空UI消息区域
        self.ids.message_area.clear_widgets()

        # 添加消息到UI
        for msg in messages:
            if msg.get('direction') == 'outgoing':
                self._add_user_message(
                    self._get_message_content(msg),
                    msg.get('timestamp', '')
                )
            else:
                self._add_system_message(
                    self._get_message_content(msg),
                    msg.get('timestamp', '')
                )

    def _get_message_content(self, message):
        """从消息对象中提取内容文本"""
        msg_type = message.get('type', '')
        content = message.get('content', {})

        if msg_type == 'text':
            return content.get('text', '')
        elif msg_type == 'audio':
            return "[语音消息]"
        else:
            return f"[{msg_type}消息]"

    def _add_user_message(self, text, timestamp=None):
        """添加用户消息到聊天窗口"""
        if not timestamp:
            timestamp = datetime.now().strftime("%H:%M:%S")

        bubble = MessageBubble(
            message_text=text,
            timestamp=timestamp,
            is_user=True
        )
        self.ids.message_area.add_widget(bubble)
        self._scroll_to_bottom()

    def _add_system_message(self, text, timestamp=None):
        """添加系统消息到聊天窗口"""
        if not timestamp:
            timestamp = datetime.now().strftime("%H:%M:%S")

        bubble = MessageBubble(
            message_text=text,
            timestamp=timestamp,
            is_user=False
        )
        self.ids.message_area.add_widget(bubble)
        self._scroll_to_bottom()

    def _scroll_to_bottom(self):
        """滚动到底部"""
        Clock.schedule_once(lambda dt: setattr(self.ids.chat_scroll, 'scroll_y', 0), 0.1)

    def start_recording(self):
        """开始录音"""
        if self.is_recording:
            return

        self.is_recording = True
        self.frames = []
        self.recording_time = 0

        # 开始录音计时
        Clock.schedule_interval(self._update_recording_time, 1)

        # 启动录音线程
        self.recording_thread = threading.Thread(target=self._recording_thread_func)
        self.recording_thread.daemon = True
        self.recording_thread.start()

    def _recording_thread_func(self):
        """录音线程函数"""
        try:
            self.audio = pyaudio.PyAudio()

            # 打开音频流
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                frames_per_buffer=self.chunk
            )

            # 录音
            while self.is_recording:
                data = self.stream.read(self.chunk)
                self.frames.append(data)

                # 计算音量并更新波形
                amplitude = max(0.1, min(1.0, self._calculate_volume(data) * 2))
                Clock.schedule_once(lambda dt: setattr(self.ids.wave_indicator, 'amplitude', amplitude))

        except Exception as e:
            print(f"录音出错: {str(e)}")
            Clock.schedule_once(lambda dt: self._add_system_message(f"录音失败: {str(e)}"))

    def _calculate_volume(self, data):
        """计算音频块的音量水平"""
        try:
            import numpy as np
            audio_data = np.frombuffer(data, dtype=np.int16)
            # 计算均方根值并归一化
            rms = np.sqrt(np.mean(np.square(audio_data)))
            normalized = min(1.0, rms / 32767)
            return normalized
        except:
            return 0.5  # 默认值

    def _update_recording_time(self, dt):
        """更新录音计时"""
        if self.is_recording:
            self.recording_time += 1

            # 如果录音时间超过60秒，自动停止
            if self.recording_time >= 60:
                Clock.schedule_once(lambda dt: self.stop_recording())

    def stop_recording(self):
        """停止录音并发送"""
        if not self.is_recording:
            return

        self.is_recording = False
        Clock.unschedule(self._update_recording_time)

        # 停止录音线程
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()

        if self.audio:
            self.audio.terminate()

        # 保存录音
        if self.frames:
            # 在主线程外处理录音保存和发送
            threading.Thread(target=self._save_and_send_recording).start()

    def _save_and_send_recording(self):
        """保存录音并发送到分诊系统"""
        try:
            # 创建临时录音目录
            temp_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 保存WAV文件
            filename = os.path.join(temp_dir, f"recording_{datetime.now().strftime('%Y%m%d%H%M%S')}.wav")

            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(self.audio.get_sample_size(self.format))
                wf.setframerate(self.rate)
                wf.writeframes(b''.join(self.frames))

            # 在UI线程中更新界面
            Clock.schedule_once(lambda dt: self._add_user_message("[语音消息]"))

            # 发送到分诊系统
            if self.current_session_id:
                result = self.api_client.send_voice_message(self.current_session_id, filename)

                if result.get('success'):
                    response = result.get('response', {})
                    response_text = response.get('content', {}).get('text', '')

                    if response_text:
                        Clock.schedule_once(lambda dt: self._add_system_message(response_text))
                    else:
                        Clock.schedule_once(lambda dt: self._add_system_message("对不起，我没能理解您的语音内容"))
                else:
                    error_msg = result.get('message', '发送语音消息失败')
                    Clock.schedule_once(lambda dt: self._add_system_message(f"错误: {error_msg}"))
            else:
                Clock.schedule_once(lambda dt: self._add_system_message("错误: 会话未初始化"))

        except Exception as e:
            print(f"保存和发送录音出错: {str(e)}")
            Clock.schedule_once(lambda dt: self._add_system_message(f"发送语音消息失败: {str(e)}"))

    def toggle_text_input(self):
        """切换文本输入模式"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.textfield import MDTextField
        from kivymd.uix.button import MDFlatButton

        # 创建文本输入对话框
        self.text_field = MDTextField(
            hint_text="请输入您想说的内容...",
            multiline=True,
            max_height=dp(100)
        )

        self.text_dialog = MDDialog(
            title="文本输入",
            type="custom",
            content_cls=self.text_field,
            buttons=[
                MDFlatButton(
                    text="取消",
                    on_release=lambda x: self.text_dialog.dismiss()
                ),
                MDFlatButton(
                    text="发送",
                    on_release=self._send_text_message
                )
            ]
        )

        self.text_dialog.open()

    def _send_text_message(self, *args):
        """发送文本消息"""
        text = self.text_field.text.strip()
        self.text_dialog.dismiss()

        if not text:
            return

        # 添加用户消息到聊天窗口
        self._add_user_message(text)

        # 发送到分诊系统
        if self.current_session_id:
            # 使用线程发送消息，避免阻塞UI
            threading.Thread(target=self._send_text_to_server, args=(text,)).start()
        else:
            self._add_system_message("错误: 会话未初始化")

    def _send_text_to_server(self, text):
        """发送文本消息到服务器"""
        try:
            result = self.api_client.send_text_message(self.current_session_id, text)

            if result.get('success'):
                response = result.get('response', {})
                response_text = response.get('content', {}).get('text', '')

                if response_text:
                    Clock.schedule_once(lambda dt: self._add_system_message(response_text))
                else:
                    Clock.schedule_once(lambda dt: self._add_system_message("对不起，我暂时无法回答您的问题"))
            else:
                error_msg = result.get('message', '发送文本消息失败')
                Clock.schedule_once(lambda dt: self._add_system_message(f"错误: {error_msg}"))

        except Exception as e:
            print(f"发送文本消息出错: {str(e)}")
            Clock.schedule_once(lambda dt: self._add_system_message(f"发送文本消息失败: {str(e)}"))

    def show_summary(self):
        """显示会话总结"""
        if not self.current_session_id:
            self._add_system_message("错误: 会话未初始化")
            return

        # 使用线程获取总结，避免阻塞UI
        threading.Thread(target=self._get_and_show_summary).start()

    def _get_and_show_summary(self):
        """获取并显示会话总结"""
        try:
            result = self.api_client.get_triage_summary(self.current_session_id)

            if result.get('success'):
                summary = result.get('summary', {})

                # 构建总结文本
                summary_text = "【分诊总结】\n"
                if 'main_complaint' in summary:
                    summary_text += f"主诉: {summary.get('main_complaint', '')}\n"
                if 'possible_conditions' in summary:
                    conditions = summary.get('possible_conditions', [])
                    if conditions:
                        summary_text += "可能的情况:\n"
                        for i, condition in enumerate(conditions, 1):
                            name = condition.get('name', '')
                            probability = condition.get('probability', '')
                            summary_text += f"{i}. {name} ({probability})\n"
                if 'recommendations' in summary:
                    recommendations = summary.get('recommendations', [])
                    if recommendations:
                        summary_text += "建议:\n"
                        for i, rec in enumerate(recommendations, 1):
                            summary_text += f"{i}. {rec}\n"
                if 'urgency_level' in summary:
                    summary_text += f"紧急程度: {summary.get('urgency_level', '')}\n"
                if 'department_recommendation' in summary:
                    summary_text += f"推荐科室: {summary.get('department_recommendation', '')}"

                # 在UI线程中显示总结
                Clock.schedule_once(lambda dt: self._add_system_message(summary_text))
            else:
                error_msg = result.get('message', '获取会话总结失败')
                Clock.schedule_once(lambda dt: self._add_system_message(f"错误: {error_msg}"))

        except Exception as e:
            print(f"获取会话总结出错: {str(e)}")
            Clock.schedule_once(lambda dt: self._add_system_message(f"获取会话总结失败: {str(e)}"))

    def show_info(self):
        """显示帮助信息"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton

        help_text = """
语音智能分诊系统使用指南:

1. 按住录音按钮开始说话，松开按钮发送语音
2. 点击文本按钮可以切换到文本输入模式
3. 点击文档按钮可以查看分诊总结
4. 请清晰描述您的症状，包括:
   - 不适感开始的时间
   - 症状的位置和特点
   - 是否有加重或缓解因素
   - 相关的病史信息
5. 分诊结果仅供参考，严重情况请立即就医
"""

        dialog = MDDialog(
            title="使用指南",
            text=help_text,
            buttons=[
                MDFlatButton(
                    text="了解",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )

        dialog.open()

    def go_back(self):
        """返回上一屏幕"""
        app = App.get_running_app()
        app.root.current = "homepage_screen"