# 用户ID格式规范

## 标准规范

为了确保系统中用户ID格式的一致性，我们制定了以下标准规范：

1. 所有用户ID必须使用带前缀的格式化ID
2. 用户ID由后端统一生成和管理，移动端不再生成用户ID
3. 前缀根据用户角色确定：
   - 个人用户：P前缀
   - 健康顾问：D前缀
   - 单位管理员：U前缀
   - 超级管理员：SM前缀
4. 数字部分长度根据用户角色确定：
   - 个人用户(P)：8位数字，如P_00000001
   - 健康顾问(D)：5位数字，如D_00001
   - 单位管理员(U)：4位数字，如U_0001
   - 超级管理员(SM)：3位数字，如SM_001
5. 多角色用户使用最高级别角色的前缀（P < D < U < SM）

## 实现方式

1. 后端API返回用户信息时，始终包含`custom_id`字段
2. 移动端登录时优先使用后端返回的`custom_id`字段作为用户ID
3. 移动端不再生成用户ID，所有用户ID由后端统一生成
4. 移动端个人页面和主页显示用户信息时，直接使用后端返回的数据

## 技术实现

1. 后端API已修改，确保所有用户相关API都返回`custom_id`字段
2. 移动端登录流程已修改，优先使用后端返回的`custom_id`字段
3. 移动端的用户ID生成规则已简化，只生成临时ID用于本地测试
4. 移动端个人页面和主页已修改，正确显示用户真实姓名和用户ID

## 注意事项

1. 如果后端返回的`custom_id`为空，移动端会生成一个临时ID（格式为`TEMP_时间戳`）
2. 临时ID仅用于本地测试，实际使用时应该使用后端生成的ID
3. 移动端不再维护ID计数器，所有ID计数由后端统一管理
4. 用户真实姓名优先使用后端返回的`full_name`字段，如果为空则使用`username`
5. 主页欢迎信息和个人页面用户信息都会正确显示用户真实姓名
