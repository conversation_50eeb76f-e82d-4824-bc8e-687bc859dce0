# 工具类模块说明文档

本模块包含应用程序使用的各种工具类和辅助功能。这些工具类主要用于管理数据库、数据同步、云端API通信、数据迁移等功能。

## 模块列表

### 数据库管理

- `database.py` - 数据库管理器，负责管理SQLite数据库连接和基本操作。
- `db_models.py` - 数据库模型定义，定义了各个数据表的结构。
- `db_service.py` - 数据库服务类，提供通用的数据CRUD操作和事务处理。
- `health_service.py` - 健康数据服务类，处理健康相关数据的存储和查询。

### 数据同步与云端通信

- `cloud_api.py` - 云端API客户端，负责与远程服务器的通信。
- `sync_service.py` - 数据同步服务，管理本地数据与云端数据的同步。

### 数据迁移与备份

- `data_migration.py` - 数据迁移工具，用于备份和恢复数据库。

## 使用说明

### 数据库管理

#### 数据库管理器 (DatabaseManager)

数据库管理器负责创建和管理数据库连接，以及执行基本的数据库操作。

```python
from mobile.utils.database import DatabaseManager, get_database_manager

# 获取数据库管理器实例（单例模式）
db_manager = get_database_manager()

# 获取用户数据库路径
user_db_path = db_manager.get_user_db_path("user123")

# 执行SQL查询
results = db_manager.execute_query("SELECT * FROM user_info WHERE id = ?", (1,), "user123")
```

#### 数据库服务 (BaseDBService)

数据库服务提供了更高级别的数据操作API，支持CRUD操作和事务处理。

```python
from mobile.utils.db_service import BaseDBService

# 创建数据库服务实例
db_service = BaseDBService("user123")

# 插入记录
record = {
    "username": "张三",
    "email": "<EMAIL>",
    "age": 30
}
record_id = db_service.insert_record("user_info", record)

# 更新记录
updated_data = {"age": 31}
db_service.update_record("user_info", record_id, updated_data)

# 获取记录
user = db_service.get_record_by_id("user_info", record_id)

# 删除记录
db_service.delete_record("user_info", record_id)

# 事务处理
with db_service.transaction():
    db_service.insert_record("table1", data1)
    db_service.insert_record("table2", data2)
```

#### 健康数据服务 (HealthDBService)

健康数据服务专门用于处理健康相关数据，提供了更专业的健康数据存储和查询功能。

```python
from mobile.utils.health_service import HealthDBService

# 创建健康数据服务实例
health_service = HealthDBService("user123")

# 保存用户基本信息
user_data = {
    "username": "张三",
    "real_name": "张三",
    "gender": "男",
    "birth_date": "1990-01-01"
}
health_service.save_user_info(user_data)

# 保存健康基本信息
health_data = {
    "height": 175,
    "weight": 70,
    "blood_type": "A"
}
health_service.save_health_info(health_data)

# 添加疾病史
disease_data = {
    "disease_name": "高血压",
    "onset_time": "2020-01-01",
    "treatment": "服用降压药"
}
health_service.add_disease(disease_data)

# 添加药物过敏史
allergy_data = {
    "drug_name": "青霉素",
    "reaction": "皮疹",
    "severity": "中度"
}
health_service.add_drug_allergy(allergy_data)

# 获取用户健康信息
health_info = health_service.get_health_info()
```

### 数据同步与云端通信

#### 云端API客户端 (CloudAPI)

云端API客户端负责与远程服务器通信，提供了用户认证、数据同步等功能。

```python
from mobile.utils.cloud_api import get_cloud_api

# 获取云端API实例（单例模式）
cloud_api = get_cloud_api()

# 设置API凭证
cloud_api.set_credentials(api_key="your_api_key", app_id="your_app_id")

# 用户认证
cloud_api.authenticate(username="user123", password="password123")

# 获取用户信息
user_info = cloud_api.get_user_info()

# 上传文件
file_info = cloud_api.upload_file(
    file_path="/path/to/file.jpg", 
    file_type="image"
)

# 同步记录到云端
cloud_api.sync_record("health_info", record_data)
```

#### 数据同步服务 (SyncManager)

数据同步服务负责管理本地数据与云端数据的同步，支持自动同步和手动同步。

```python
from mobile.utils.sync_service import get_sync_manager
from mobile.utils.cloud_api import get_cloud_api

# 获取同步管理器实例（单例模式）
sync_manager = get_sync_manager()

# 设置云端API
cloud_api = get_cloud_api()
sync_manager.set_cloud_api(cloud_api)

# 设置当前用户
sync_manager.set_user_id("user123")

# 设置同步间隔（秒）
sync_manager.set_sync_interval(300)  # 5分钟

# 启动同步服务
sync_manager.start_sync()

# 手动触发同步
sync_manager.sync_now()

# 从云端下载数据
sync_manager.download_from_cloud("user123")

# 停止同步服务
sync_manager.stop_sync()
```

### 数据迁移与备份

#### 数据迁移工具 (DataMigrationTool)

数据迁移工具用于备份和恢复数据库，支持导出数据为JSON格式。

```python
from mobile.utils.data_migration import get_migration_tool

# 获取数据迁移工具实例（单例模式）
migration_tool = get_migration_tool()

# 备份用户数据
backup_file = migration_tool.backup_user_data("user123")

# 恢复用户数据
migration_tool.restore_user_data(backup_file, "user123")

# 导出数据为JSON格式
json_file = migration_tool.export_data_as_json("user123")

# 从JSON导入数据
migration_tool.import_data_from_json(json_file, "user123")

# 列出备份文件
backups = migration_tool.list_backups("user123")

# 获取备份文件信息
backup_info = migration_tool.get_backup_info(backup_file)

# 删除备份文件
migration_tool.delete_backup(backup_file)
```

## 注意事项

1. 所有的服务类都采用单例模式，可以通过对应的工厂函数获取实例，如 `get_database_manager()`, `get_cloud_api()` 等。

2. 在使用数据同步功能前，必须先设置云端API和用户ID。

3. 数据库操作应尽量使用事务来保证数据一致性。

4. 所有的API都有适当的错误处理，使用时应检查返回值以确保操作成功。

5. 数据迁移和备份功能应在应用程序空闲时进行，以避免数据不一致。 