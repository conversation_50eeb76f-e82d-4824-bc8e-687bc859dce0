#!/usr/bin/env python3
"""
验证修复效果
"""

def test_response_processing():
    """测试响应处理逻辑"""
    print("=== 测试响应处理逻辑 ===")
    
    # 模拟不同格式的响应
    test_cases = [
        {
            "name": "列表格式响应（服务器直接返回列表）",
            "data": [
                {"id": 1, "name": "量表1", "notes": "描述1"},
                {"id": 2, "name": "量表2", "notes": "描述2"}
            ]
        },
        {
            "name": "标准成功响应",
            "data": {
                "status": "success",
                "data": [
                    {"id": 1, "name": "量表1", "notes": "描述1"}
                ]
            }
        },
        {
            "name": "错误响应",
            "data": {
                "status": "error",
                "message": "认证失败"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        result = test_case['data']
        
        # 模拟修复后的处理逻辑
        try:
            if result:
                if isinstance(result, list):
                    # 如果直接返回列表，直接使用
                    assessment_list = result
                    print(f"✓ 列表格式处理成功: 获取到 {len(assessment_list)} 个评估量表")
                elif isinstance(result, dict):
                    if result.get('status') == 'success':
                        # 标准成功响应格式
                        assessment_list = result.get('data', [])
                        print(f"✓ 标准响应处理成功: 获取到 {len(assessment_list)} 个评估量表")
                    else:
                        # 错误响应
                        error_msg = result.get('message', '未知错误')
                        print(f"✓ 错误响应处理成功: {error_msg}")
                        assessment_list = []
                else:
                    # 其他格式，尝试直接使用
                    assessment_list = [result] if result else []
                    print(f"✓ 其他格式处理成功: 获取到 {len(assessment_list)} 个评估量表")
            else:
                print("✗ 空响应")
                assessment_list = []
                
        except Exception as e:
            print(f"✗ 处理失败: {str(e)}")

def main():
    """主函数"""
    print("验证列表响应格式修复...")
    
    test_response_processing()
    
    print("\n=== 修复总结 ===")
    print("✅ 已修复 'list' object has no attribute 'get' 错误")
    print("✅ 现在可以正确处理以下响应格式:")
    print("   1. 直接返回的列表格式 (list)")
    print("   2. 标准的成功响应格式 (dict with status/data)")
    print("   3. 错误响应格式 (dict with status/message)")
    print("   4. 其他格式的响应")
    
    print("\n=== 修复的关键点 ===")
    print("1. 在处理服务器响应时，先检查数据类型")
    print("2. 如果是列表，直接使用作为评估量表数据")
    print("3. 如果是字典，根据status字段判断处理方式")
    print("4. 增加了详细的日志记录，便于调试")
    
    print("\n=== 建议测试步骤 ===")
    print("1. 重启移动应用")
    print("2. 重新登录获取有效token")
    print("3. 进入评估量表页面")
    print("4. 检查是否能正常显示评估量表列表")
    print("5. 查看日志中的数据类型信息")

if __name__ == "__main__":
    main()
