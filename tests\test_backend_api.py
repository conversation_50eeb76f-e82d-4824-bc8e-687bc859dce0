#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
移动端API单元测试
使用FastAPI的TestClient测试移动端API接口
"""

import os
import sys
import json
import pytest
import hashlib
import time
import logging
from datetime import datetime

# 添加项目根目录到路径中，以便导入HTTP日志功能
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.http_logger import setup_http_logging, enable_http_logging, test_api_endpoint

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('mobile_api_tests')

# 确保HTTP日志已启用
setup_http_logging()
enable_http_logging()

# API基础URL
API_BASE_URL = "http://8.138.188.26/api"

# 测试数据
test_mobile_user = {
    "username": f"mobile_testuser_{int(time.time())}",  # 确保每次测试都是唯一用户名
    "fullName": "移动测试用户",
    "password": "MobilePass@123",
    "email": f"mobile_test_{int(time.time())}@example.com",
    "role": "personal_user"
}

# 存储认证信息
auth_token = None
user_id = None

def setup_module(module):
    """模块初始化函数"""
    logger.info("=== 开始移动端API测试 ===")

def teardown_module(module):
    """模块清理函数"""
    logger.info("=== 移动端API测试结束 ===")

def test_01_server_health():
    """测试服务器健康状态"""
    # 测试主服务器健康状态
    result = test_api_endpoint(f"{API_BASE_URL}/health")
    assert result['success'] is True, "服务器健康检查失败"
    logger.info(f"服务器健康状态检查成功: {result['status_code']}")

def test_02_mobile_register():
    """测试移动端用户注册API"""
    url = f"{API_BASE_URL}/auth/register"
    
    # 准备注册数据
    register_data = {
        "username": test_mobile_user["username"],
        "name": test_mobile_user["fullName"],  # 后端API使用name而不是fullName
        "password_hash": hashlib.sha256(test_mobile_user["password"].encode()).hexdigest(),  # 使用哈希密码
        "email": test_mobile_user["email"],
        "role": test_mobile_user["role"]
    }
    
    # 发送注册请求
    result = test_api_endpoint(
        url=url,
        method='post',
        data=register_data,
        expected_status=[200, 204, 400]  # 接受400 - 如果用户已存在
    )
    
    # 断言响应状态码和内容
    if result['status_code'] == 400:
        response_data = result.get('response', {})
        message = ''
        if isinstance(response_data, dict):
            message = response_data.get('message', '')
        
        if isinstance(message, str) and ('已存在' in message or 'exists' in message.lower()):
            logger.warning(f"用户已存在，将跳过注册测试: {message}")
            pytest.skip("用户已存在，跳过此测试")
        else:
            assert False, f"注册失败，原因: {message}"
    else:
        assert result['success'] is True, f"注册失败: {result.get('response')}"
        logger.info(f"用户注册成功: {test_mobile_user['username']}")
        
        # 检查返回的用户信息
        response_data = result.get('response', {})
        if response_data and response_data != '(Empty Response)' and isinstance(response_data, dict):
            data = response_data.get('data', {})
            if isinstance(data, dict):
                # 保存用户ID供后续测试使用
                global user_id
                user_id = data.get('user_id')
                logger.info(f"提取的用户ID: {user_id}")

def test_03_mobile_login():
    """测试移动端登录API"""
    global auth_token, user_id
    
    url = f"{API_BASE_URL}/auth/login"
    
    # 创建登录数据
    login_data = {
        "username": test_mobile_user["username"],
        "password_hash": hashlib.sha256(test_mobile_user["password"].encode()).hexdigest(),
        "timestamp": int(time.time())
    }
    
    # 发送登录请求
    result = test_api_endpoint(
        url=url,
        method='post',
        data=login_data,
        expected_status=[200, 204]
    )
    
    # 断言响应状态码和内容
    assert result['success'] is True, f"登录失败: {result.get('response')}"
    logger.info("用户登录成功")
    
    # 保存认证信息供后续测试使用
    response_data = result.get('response', {})
    if response_data and response_data != '(Empty Response)' and isinstance(response_data, dict):
        data = response_data.get('data', {})
        if isinstance(data, dict):
            auth_token = data.get('token')
            user_id = data.get('user_id') or user_id  # 如果已经有ID，则不覆盖
            
            assert auth_token is not None, "登录响应中缺少token"
            logger.info(f"成功获取认证Token，用户ID: {user_id}")
    else:
        # 如果响应为空但状态码是成功的，则可能是服务器特性
        logger.warning("登录成功，但无法从响应中获取Token")
        pytest.skip("无法获取Token，跳过依赖Token的后续测试")

def test_04_mobile_user_info():
    """测试移动端获取用户信息API"""
    # 确保已登录
    if not auth_token:
        pytest.skip("需要先登录才能测试此接口")
    
    url = f"{API_BASE_URL}/user/info"
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 发送请求
    result = test_api_endpoint(
        url=url,
        method='get',
        headers=headers,
        expected_status=[200, 204]
    )
    
    # 断言响应状态码和内容
    assert result['success'] is True, f"获取用户信息失败: {result.get('response')}"
    logger.info("成功获取用户信息")
    
    # 验证用户信息
    response_data = result.get('response', {})
    if response_data and response_data != '(Empty Response)' and isinstance(response_data, dict):
        data = response_data.get('data', {})
        if isinstance(data, dict) and 'username' in data:
            assert data['username'] == test_mobile_user["username"], "返回的用户名不匹配"
            logger.info(f"验证用户信息成功: {data.get('username')}")

def test_05_mobile_health_data():
    """测试上传健康数据API"""
    # 确保已登录
    if not auth_token:
        pytest.skip("需要先登录才能测试此接口")
    
    url = f"{API_BASE_URL}/health/upload"
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 准备健康数据
    health_data = {
        "user_id": user_id,
        "data_type": "blood_pressure",
        "measurement": {
            "systolic": 120,
            "diastolic": 80,
            "pulse": 72
        },
        "measured_at": datetime.now().isoformat(),
        "device_id": "test_device_001"
    }
    
    # 发送请求
    result = test_api_endpoint(
        url=url,
        method='post',
        headers=headers,
        data=health_data,
        expected_status=[200, 201, 204, 404]  # 404表示端点不存在也接受
    )
    
    # 如果端点不存在，跳过测试
    if result['status_code'] == 404:
        logger.warning(f"健康数据API端点不存在: {url}")
        pytest.skip("健康数据API端点不存在")
    else:
        assert result['success'] is True, f"上传健康数据失败: {result.get('response')}"
        logger.info("成功上传健康数据")

def test_06_mobile_logout():
    """测试移动端退出登录API"""
    # 确保已登录
    if not auth_token:
        pytest.skip("需要先登录才能测试此接口")
    
    url = f"{API_BASE_URL}/auth/logout"
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # 发送请求
    result = test_api_endpoint(
        url=url,
        method='post',
        headers=headers,
        expected_status=[200, 204]
    )
    
    # 断言响应状态码和内容
    assert result['success'] is True, f"退出登录失败: {result.get('response')}"
    logger.info("用户成功退出登录")

def test_07_invalid_login():
    """测试无效登录凭据"""
    url = f"{API_BASE_URL}/auth/login"
    
    # 创建无效的登录数据
    invalid_login_data = {
        "username": "nonexistent_user",
        "password_hash": hashlib.sha256("invalid_password".encode()).hexdigest(),
        "timestamp": int(time.time())
    }
    
    # 发送登录请求，期望失败，但服务器可能返回200或400
    result = test_api_endpoint(
        url=url,
        method='post',
        data=invalid_login_data,
        expected_status=[200, 400, 401, 403]
    )
    
    # 检查成功标志是否为False或状态码是否表示错误
    if result['status_code'] in [400, 401, 403]:
        assert True, "无效登录测试成功：服务器返回了适当的错误状态码"
    else:
        response_data = result.get('response', {})
        if isinstance(response_data, dict):
            # 即使状态码是200，success字段应该是False
            assert response_data.get('success') is False, "无效登录应该返回success=False"
    
    logger.info("无效登录测试通过")

def test_08_access_protected_api_without_auth():
    """测试无认证访问受保护的API"""
    url = f"{API_BASE_URL}/user/info"
    
    # 不提供认证头
    result = test_api_endpoint(
        url=url,
        method='get',
        expected_status=[401, 403, 200]  # 接受401/403表示未授权，或200但success=False
    )
    
    if result['status_code'] in [401, 403]:
        assert True, "未认证测试成功：服务器返回了适当的错误状态码"
    else:
        response_data = result.get('response', {})
        if isinstance(response_data, dict):
            assert response_data.get('success') is False, "未认证访问应该返回success=False"
    
    logger.info("未认证访问测试通过")

if __name__ == "__main__":
    # 使用pytest运行测试
    pytest.main(["-v", __file__]) 