# screens/public_screen.py
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import StringProperty, NumericProperty
from kivy.factory import Factory
from theme import AppTheme, AppMetrics, FontStyles, FontManager
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.graphics import RoundedRectangle, Color
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件
import json
import os

# 确保图形元素在kv字符串中可用
Factory.register('RoundedRectangle', cls=RoundedRectangle)
Factory.register('Color', cls=Color)

# 定义公务人员界面
class PublicScreen(Screen):
    # 属性定义
    user_name = StringProperty("李明")  # 用户真实姓名
    gender = StringProperty("男")  # 性别
    age = NumericProperty(45)  # 年龄
    blood_type = StringProperty("A型")  # 血型
    id_card = StringProperty("**********")  # 身份证号（部分隐藏）
    address = StringProperty("**********")  # 住址（部分隐藏）
    
    def __init__(self, **kwargs):
        super(PublicScreen, self).__init__(**kwargs)
        # 加载用户数据
        self.load_user_data()
    
    def load_user_data(self):
        """从本地存储加载用户数据"""
        try:
            # 尝试从data目录读取用户数据
            data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../data")
            user_data_file = os.path.join(data_dir, "user_data.json")
            
            if os.path.exists(user_data_file):
                with open(user_data_file, "r") as f:
                    data = json.load(f)
                    user_info = data.get("user_info", {})
                    
                    # 设置用户名称
                    if "name" in user_info:
                        self.user_name = user_info["name"]
                    
                    # 在实际应用中，这里应该从API获取最新的健康数据
                    # 目前使用模拟数据
        except Exception as e:
            print(f"加载用户数据失败: {str(e)}")
    
    def on_enter(self):
        """屏幕进入时调用"""
        # 在实际应用中，这里应该从API获取最新的健康数据
        pass
    
    def navigate_to_health_history(self):
        """导航到健康历史记录页面"""
        # 在实际应用中，这里应该跳转到健康历史记录页面
        pass

Builder.load_string("""
<PublicScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    BoxLayout:
        orientation: "vertical"
        padding: dp(5)
        spacing: dp(5)
        
        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                Rectangle:
                    pos: self.pos
                    size: self.size
            
            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo
        
        # 紧急提醒区域
        BoxLayout:
            size_hint_y: None
            height: dp(50)
            padding: dp(5)
            canvas.before:
                Color:
                    rgba: 1, 0.8, 0.8, 1  # 浅红色背景
                Rectangle:
                    pos: self.pos
                    size: self.size
            
            # 警告图标
            Label:
                size_hint_x: None
                width: dp(30)
                text: "⚠"
                font_size: app.font_styles.TITLE_MEDIUM['font_size']
                color: app.theme.ERROR_COLOR
            
            # 提醒文本
            Label:
                text: "公务人员: 如有问题，正在调阅: 李二重 同志健康资料"
                font_size: app.font_styles.BODY_MEDIUM['font_size']
                font_name: "NotoSans"
                color: app.theme.ERROR_COLOR
                halign: "left"
                text_size: self.size
            
            # 状态图标
            BoxLayout:
                size_hint_x: None
                width: dp(100)
                padding: dp(5)
                
                Label:
                    size_hint_x: None
                    width: dp(20)
                    text: "⭕"
                    font_size: app.font_styles.BODY_LARGE['font_size']
                    font_name: "NotoSans"
                    color: app.theme.ERROR_COLOR
                
                Label:
                    text: "资料状态使用"
                    font_size: app.font_styles.BODY_SMALL['font_size']
                    font_name: "NotoSans"
                    color: app.theme.ERROR_COLOR
                    halign: "left"
                    text_size: self.size
        
        # 信息安全提示
        BoxLayout:
            size_hint_y: None
            height: dp(60)
            padding: dp(10)
            canvas.before:
                Color:
                    rgba: 1, 0.95, 0.8, 1  # 浅黄色背景
                Rectangle:
                    pos: self.pos
                    size: self.size
            
            # 提示图标
            Label:
                size_hint_x: None
                width: dp(30)
                text: "ℹ"
                font_size: app.font_styles.TITLE_MEDIUM['font_size']
                font_name: "NotoSans"
                color: 1, 0.6, 0, 1  # 橙色
            
            # 提示文本
            Label:
                text: "信息安全提示：此次访问将被记录，请注意保密，防止信息外泄，违规使用将承担法律责任。"
                font_size: app.font_styles.BODY_SMALL['font_size']
                font_name: "NotoSans"
                color: 1, 0.6, 0, 1  # 橙色
                halign: "left"
                text_size: self.size
        
        # 健康状况一览表标题
        BoxLayout:
            size_hint_y: None
            height: dp(40)
            padding: [dp(10), dp(5), dp(10), dp(5)]
            
            Label:
                text: "健康状况一览表"
                font_size: app.font_styles.BODY_LARGE['font_size']
                font_name: "NotoSansMedium"
                color: app.theme.TEXT_PRIMARY
                bold: True
                halign: "left"
                text_size: self.size
        
        # 基本信息区域
        BoxLayout:
            size_hint_y: None
            height: dp(120)
            padding: dp(10)
            canvas.before:
                Color:
                    rgba: app.theme.CARD_BACKGROUND
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [dp(app.metrics.CORNER_RADIUS)]
            
            # 基本信息图标和标题
            BoxLayout:
                orientation: "vertical"
                size_hint_x: None
                width: dp(80)
                
                # 用户图标
                Label:
                    text: "👤"
                    font_size: dp(24)
                    color: app.theme.TEXT_PRIMARY
                
                # 基本信息标题
                Label:
                    text: "基本信息"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
            
            # 基本信息内容
            BoxLayout:
                orientation: "vertical"
                spacing: dp(5)
                
                # 姓名行
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(80)
                        text: "姓名："
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                    
                    Label:
                        text: "李二重"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 性别行
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(80)
                        text: "性别："
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                    
                    Label:
                        text: "男"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 年龄行
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(80)
                        text: "年龄："
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                    
                    Label:
                        text: "45岁"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 血型行
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(80)
                        text: "血型："
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                    
                    Label:
                        text: "A型"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
        
        # 身份证和住址信息
        BoxLayout:
            size_hint_y: None
            height: dp(60)
            padding: [dp(10), dp(0), dp(10), dp(0)]
            
            # 身份证号
            BoxLayout:
                orientation: "vertical"
                size_hint_y: None
                height: dp(60)
                
                Label:
                    size_hint_y: None
                    height: dp(25)
                    text: "身份证号："
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    halign: "left"
                    text_size: self.size
                
                Label:
                    size_hint_y: None
                    height: dp(25)
                    text: "**********X"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    halign: "left"
                    text_size: self.size
            
            # 住址
            BoxLayout:
                orientation: "vertical"
                size_hint_y: None
                height: dp(60)
                
                Label:
                    size_hint_y: None
                    height: dp(25)
                    text: "住址："
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    halign: "left"
                    text_size: self.size
                
                Label:
                    size_hint_y: None
                    height: dp(25)
                    text: "***********市"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    halign: "left"
                    text_size: self.size
        
        # 健康历史记录
        BoxLayout:
            size_hint_y: None
            height: dp(120)
            orientation: "vertical"
            padding: [dp(10), dp(5), dp(10), dp(5)]
            
            # 健康历史标题和图标
            BoxLayout:
                size_hint_y: None
                height: dp(30)
                
                # 图标
                Label:
                    size_hint_x: None
                    width: dp(30)
                    text: "🔍"
                    font_size: dp(18)
                    color: app.theme.TEXT_PRIMARY
                
                # 标题
                Label:
                    text: "更新健康历史"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    halign: "left"
                    text_size: self.size
            
            # 历史记录列表
            BoxLayout:
                orientation: "vertical"
                spacing: dp(5)
                padding: [dp(20), dp(0), dp(0), dp(0)]
                
                # 2023年记录
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "⚪"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "2023年：高血压 II 级"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 2022年记录
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "⚪"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "2022年：糖尿病 II 型"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 2020年记录
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "⚪"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "2020年：冠心病"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
        
        # 药物过敏史
        BoxLayout:
            size_hint_y: None
            height: dp(80)
            orientation: "vertical"
            padding: [dp(10), dp(5), dp(10), dp(5)]
            
            # 药物过敏史标题和图标
            BoxLayout:
                size_hint_y: None
                height: dp(30)
                
                # 图标
                Label:
                    size_hint_x: None
                    width: dp(30)
                    text: "⚕"
                    font_size: dp(18)
                    color: app.theme.TEXT_PRIMARY
                
                # 标题
                Label:
                    text: "药物过敏史"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    halign: "left"
                    text_size: self.size
            
            # 过敏药物列表
            BoxLayout:
                orientation: "vertical"
                spacing: dp(5)
                padding: [dp(20), dp(0), dp(0), dp(0)]
                
                # 青霉素过敏
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "⚪"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "青霉素类药物（严重）"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 磺胺过敏
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "⚪"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "磺胺类药物（中度）"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
        
        # 目前药物使用情况
        BoxLayout:
            size_hint_y: None
            height: dp(120)
            orientation: "vertical"
            padding: [dp(10), dp(5), dp(10), dp(5)]
            
            # 目前药物使用情况标题和图标
            BoxLayout:
                size_hint_y: None
                height: dp(30)
                
                # 图标
                Label:
                    size_hint_x: None
                    width: dp(30)
                    text: "💊"
                    font_size: dp(18)
                    color: app.theme.TEXT_PRIMARY
                
                # 标题
                Label:
                    text: "目前药物使用情况"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    halign: "left"
                    text_size: self.size
            
            # 药物使用列表
            BoxLayout:
                orientation: "vertical"
                spacing: dp(5)
                padding: [dp(20), dp(0), dp(0), dp(0)]
                
                # 药物1
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "1."
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "硝苯地平 80mg 每日一次"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 药物2
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "2."
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "二甲双胍 0.5g 每日两次"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 药物3
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        size_hint_x: None
                        width: dp(20)
                        text: "3."
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                    
                    Label:
                        text: "阿司匹林 100mg 每日一次"
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
        
        # 紧急联系人
        BoxLayout:
            size_hint_y: None
            height: dp(80)
            orientation: "vertical"
            padding: [dp(10), dp(5), dp(10), dp(5)]
            
            # 紧急联系人标题和图标
            BoxLayout:
                size_hint_y: None
                height: dp(30)
                
                # 图标
                Label:
                    size_hint_x: None
                    width: dp(30)
                    text: "📞"
                    font_size: dp(18)
                    color: app.theme.TEXT_PRIMARY
                
                # 标题
                Label:
                    text: "紧急联系人"
                    font_size: dp(14)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    halign: "left"
                    text_size: self.size
            
            # 联系人列表
            BoxLayout:
                orientation: "vertical"
                spacing: dp(5)
                padding: [dp(20), dp(0), dp(0), dp(0)]
                
                # 联系人1
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        text: "李小明（子）"
                        size_hint_x: 0.4
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                    
                    Label:
                        text: "138****5678"
                        size_hint_x: 0.6
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                
                # 联系人2
                BoxLayout:
                    size_hint_y: None
                    height: dp(25)
                    
                    Label:
                        text: "王芳（妻）"
                        size_hint_x: 0.4
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
                    
                    Label:
                        text: "139****1234"
                        size_hint_x: 0.6
                        font_size: dp(14)
                        color: app.theme.TEXT_PRIMARY
                        halign: "left"
                        text_size: self.size
        
        # 底部提示信息
        BoxLayout:
            size_hint_y: None
            height: dp(40)
            padding: [dp(10), dp(5), dp(10), dp(5)]
            canvas.before:
                Color:
                    rgba: 1, 0.9, 0.9, 1  # 浅红色背景
                Rectangle:
                    pos: self.pos
                    size: self.size
            
            Label:
                text: "信息仅供医疗救治使用，请勿用于非法律规定用途"
                font_size: dp(12)
                color: app.theme.ERROR_COLOR
                halign: "center"
                text_size: self.size
""")