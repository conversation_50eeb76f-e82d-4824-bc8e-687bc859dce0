from typing import Dict, Optional
import re
from datetime import datetime

class IDCardUtils:
    @staticmethod
    def parse_id_card(id_number: str) -> Optional[Dict[str, str]]:
        """
        解析身份证号码，提取出生日期和性别信息
        
        Args:
            id_number: 18位身份证号码
            
        Returns:
            包含出生日期和性别信息的字典，如果解析失败则返回None
        """
        if not IDCardUtils.validate_id_card(id_number):
            return None
            
        try:
            # 提取出生日期
            birth_date = id_number[6:14]
            year = birth_date[:4]
            month = birth_date[4:6]
            day = birth_date[6:8]
            
            # 格式化出生日期为YYYY-MM-DD
            formatted_birth_date = f"{year}-{month}-{day}"
            
            # 提取性别信息（第17位数字，奇数为男性，偶数为女性）
            gender_code = int(id_number[16])
            gender = "男" if gender_code % 2 == 1 else "女"
            
            return {
                "birth_date": formatted_birth_date,
                "gender": gender
            }
        except Exception:
            return None
    
    @staticmethod
    def validate_id_card(id_number: str) -> bool:
        """
        验证身份证号码是否有效
        
        Args:
            id_number: 18位身份证号码
            
        Returns:
            如果身份证号码有效则返回True，否则返回False
        """
        # 18位身份证号码的正则表达式
        pattern = r'^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$'
        
        if not re.match(pattern, id_number):
            return False
            
        # 验证校验码
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        try:
            # 计算校验码
            sum = 0
            for i in range(17):
                sum += int(id_number[i]) * weights[i]
            check_code = check_codes[sum % 11]
            
            # 验证校验码
            return check_code.upper() == id_number[-1].upper()
        except:
            return False
    
    @staticmethod
    def format_birth_date(date_str: str) -> str:
        """
        格式化出生日期为YYYY-MM-DD格式
        
        Args:
            date_str: 原始日期字符串
            
        Returns:
            格式化后的日期字符串
        """
        try:
            # 尝试解析不同格式的日期
            formats = [
                "%Y-%m-%d",
                "%Y/%m/%d",
                "%Y%m%d",
                "%Y.%m.%d"
            ]
            
            for fmt in formats:
                try:
                    date = datetime.strptime(date_str, fmt)
                    return date.strftime("%Y-%m-%d")
                except ValueError:
                    continue
                    
            return date_str
        except:
            return date_str 