#!/usr/bin/env python3
"""
清理上传队列中的未上传文件
"""

import os
import json
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_upload_queue():
    """清理上传队列中的文件"""
    upload_queue_dir = "data/upload_queue"
    
    if not os.path.exists(upload_queue_dir):
        logger.info("上传队列目录不存在")
        return
    
    # 获取所有队列文件
    queue_files = [f for f in os.listdir(upload_queue_dir) if f.endswith('.json')]
    
    if not queue_files:
        logger.info("上传队列为空")
        return
    
    logger.info(f"发现 {len(queue_files)} 个队列文件")
    
    cleaned_count = 0
    for queue_file in queue_files:
        queue_path = os.path.join(upload_queue_dir, queue_file)
        
        try:
            # 读取队列文件信息
            with open(queue_path, 'r', encoding='utf-8') as f:
                queue_data = json.load(f)
            
            # 获取文件信息
            file_name = queue_data.get('file_name', 'Unknown')
            created_time = queue_data.get('created_time', '')
            
            logger.info(f"清理队列文件: {file_name} (创建时间: {created_time})")
            
            # 删除队列文件
            os.remove(queue_path)
            cleaned_count += 1
            
        except Exception as e:
            logger.error(f"清理队列文件 {queue_file} 时出错: {str(e)}")
    
    logger.info(f"清理完成，共清理了 {cleaned_count} 个队列文件")

def cleanup_ocr_queue():
    """清理OCR队列中的文件"""
    ocr_queue_dir = "data/ocr_queue"
    
    if not os.path.exists(ocr_queue_dir):
        logger.info("OCR队列目录不存在")
        return
    
    # 获取所有队列文件
    queue_files = [f for f in os.listdir(ocr_queue_dir) if f.endswith('.json')]
    
    if not queue_files:
        logger.info("OCR队列为空")
        return
    
    logger.info(f"发现 {len(queue_files)} 个OCR队列文件")
    
    cleaned_count = 0
    for queue_file in queue_files:
        queue_path = os.path.join(ocr_queue_dir, queue_file)
        
        try:
            logger.info(f"清理OCR队列文件: {queue_file}")
            os.remove(queue_path)
            cleaned_count += 1
            
        except Exception as e:
            logger.error(f"清理OCR队列文件 {queue_file} 时出错: {str(e)}")
    
    logger.info(f"OCR队列清理完成，共清理了 {cleaned_count} 个文件")

def cleanup_sync_queue():
    """清理同步队列中的文件"""
    sync_queue_dir = "data/sync_queue"
    
    if not os.path.exists(sync_queue_dir):
        logger.info("同步队列目录不存在")
        return
    
    # 清理健康数据同步队列
    health_data_dir = os.path.join(sync_queue_dir, "health_data")
    if os.path.exists(health_data_dir):
        health_files = os.listdir(health_data_dir)
        for file in health_files:
            try:
                os.remove(os.path.join(health_data_dir, file))
                logger.info(f"清理健康数据同步文件: {file}")
            except Exception as e:
                logger.error(f"清理健康数据同步文件 {file} 时出错: {str(e)}")
    
    # 清理响应同步队列
    responses_dir = os.path.join(sync_queue_dir, "responses")
    if os.path.exists(responses_dir):
        response_files = os.listdir(responses_dir)
        for file in response_files:
            try:
                os.remove(os.path.join(responses_dir, file))
                logger.info(f"清理响应同步文件: {file}")
            except Exception as e:
                logger.error(f"清理响应同步文件 {file} 时出错: {str(e)}")

def cleanup_register_queue():
    """清理注册队列"""
    register_queue_file = "data/register_queue.json"
    
    if os.path.exists(register_queue_file):
        try:
            with open(register_queue_file, 'r', encoding='utf-8') as f:
                queue_data = json.load(f)
            
            if queue_data:
                logger.info(f"发现 {len(queue_data)} 个注册队列项")
                
                # 清空注册队列
                with open(register_queue_file, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                
                logger.info("注册队列已清空")
            else:
                logger.info("注册队列为空")
                
        except Exception as e:
            logger.error(f"清理注册队列时出错: {str(e)}")
    else:
        logger.info("注册队列文件不存在")

def main():
    """主清理函数"""
    logger.info("开始清理未上传的文件相关信息...")
    
    # 清理各种队列
    cleanup_upload_queue()
    cleanup_ocr_queue()
    cleanup_sync_queue()
    cleanup_register_queue()
    
    logger.info("清理完成！")

if __name__ == "__main__":
    main()
