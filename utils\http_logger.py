"""
HTTP请求日志模块

提供对HTTP请求和响应的详细日志记录功能，包括：
- 请求URL、方法、参数和头信息
- 响应状态码和内容
- 请求执行时间
- 异常捕获和记录
"""

import os
import json
import time
import uuid
import logging
import traceback
import requests
from datetime import datetime
import functools
from functools import wraps
from typing import Callable, Dict, Any, List, Optional

# 创建HTTP请求专用logger
http_logger = logging.getLogger('http_requests')

# 日志设置
LOGGING_ENABLED = True
MAX_CONTENT_LENGTH = 5000

# 敏感信息字段列表
SENSITIVE_FIELDS = [
    'password', 'token', 'auth', 'key', 'secret', 'credential', 
    'access_token', 'refresh_token', 'api_key', 'private_key',
    'session_id', 'cookie', 'csrf'
]

# 配置日志
logger = logging.getLogger("http_requests")
error_logger = logger  # 添加error_logger作为logger的别名

def setup_http_logging(log_level=logging.DEBUG):
    """
    设置HTTP请求日志
    
    Args:
        log_level: 日志级别，默认为DEBUG
    """
    # 已经设置了处理器则不重复添加
    if http_logger.handlers:
        return
        
    http_logger.setLevel(log_level)
    
    try:
        # 获取应用数据目录
        app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        logs_dir = os.path.join(app_dir, 'logs')
        
        # 确保日志目录存在
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
            
        # 创建HTTP请求日志文件（使用日期命名）
        today = datetime.now().strftime('%Y%m%d')
        http_log_file = os.path.join(logs_dir, f'http_requests_{today}.log')
        
        # 文件处理器
        file_handler = logging.FileHandler(http_log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)  # 控制台只显示INFO级别以上的日志
        
        # 设置格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        http_logger.addHandler(file_handler)
        http_logger.addHandler(console_handler)
        
        http_logger.info(f"HTTP请求日志初始化完成，日志文件: {http_log_file}")
    except Exception as e:
        print(f"初始化HTTP日志失败: {str(e)}")

def sanitize_data(data):
    """
    清理敏感数据，用于日志记录
    
    Args:
        data: 要清理的数据（字典、列表或其他类型）
        
    Returns:
        清理后的数据副本
    """
    if data is None:
        return None
        
    # 处理字典类型
    if isinstance(data, dict):
        sanitized = {}
        for key, value in data.items():
            # 检查是否为敏感字段
            is_sensitive = any(sensitive in key.lower() for sensitive in SENSITIVE_FIELDS)
            
            if is_sensitive and value:
                # 根据字段类型进行不同的掩码处理
                if isinstance(value, str):
                    sanitized[key] = f"{'*' * min(len(value), 8)}"
                else:
                    sanitized[key] = "[REDACTED]"
            else:
                # 递归处理非敏感字段
                sanitized[key] = sanitize_data(value)
        return sanitized
        
    # 处理列表类型
    elif isinstance(data, list):
        return [sanitize_data(item) for item in data]
        
    # 处理其他类型
    return data

def truncate_response(content, max_length=2000):
    """
    截断响应内容，防止日志过大
    
    Args:
        content: 响应内容
        max_length: 最大长度
        
    Returns:
        截断后的内容
    """
    if content is None:
        return None
        
    # 字符串类型处理
    if isinstance(content, str):
        if len(content) > max_length:
            return content[:max_length] + f"... [截断，完整长度: {len(content)}]"
        return content
        
    # 字节类型处理
    elif isinstance(content, bytes):
        try:
            # 尝试解码为字符串
            decoded = content.decode('utf-8')
            if len(decoded) > max_length:
                return decoded[:max_length] + f"... [截断，完整长度: {len(decoded)}]"
            return decoded
        except:
            # 解码失败，显示字节长度
            return f"[二进制数据，长度: {len(content)}]"
    
    # 其他类型处理
    return str(content)

def parse_response_content(response):
    """
    解析响应内容
    
    Args:
        response: 请求响应对象
        
    Returns:
        解析后的内容
    """
    # 如果没有响应，直接返回
    if response is None:
        return None
    
    # 处理响应内容
    try:
        # 尝试获取内容
        if hasattr(response, 'content'):
            content = response.content
        elif hasattr(response, 'text'):
            content = response.text
        else:
            content = str(response)
        
        # 检查是否为空
        if not content:
            return None
            
        # 尝试解析为JSON
        if isinstance(content, bytes):
            try:
                content = content.decode('utf-8')
            except UnicodeDecodeError:
                return f"[二进制数据，长度: {len(content)}]"
                
        try:
            if content.strip().startswith('{') or content.strip().startswith('['):
                json_content = json.loads(content)
                # 清理敏感数据
                sanitized_content = sanitize_data(json_content)
                # 转换回字符串并截断
                return truncate_response(json.dumps(sanitized_content, ensure_ascii=False, indent=2))
        except:
            pass
            
        # 如果不是JSON或解析失败，则按文本处理
        return truncate_response(content)
            
    except Exception as e:
        # 解析异常处理
        error_logger.error(f"解析响应内容时出错: {str(e)}")
        return f"[无法解析的响应: {str(e)}]"

def log_http_request(func):
    """
    装饰器：记录HTTP请求的详细信息
    
    装饰HTTP请求方法（GET, POST, PUT, DELETE等），记录：
    - 请求URL、方法、参数
    - 请求头信息（敏感信息已掩码）
    - 响应状态码和内容
    - 执行时间
    - 异常信息（如有）
    
    Args:
        func: 被装饰的HTTP请求函数
        
    Returns:
        包装后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 生成请求ID
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()
        
        # 尝试确定HTTP方法
        method = kwargs.get('method', '').upper() if 'method' in kwargs else None
        if not method:
            # 尝试从函数名推断方法
            for http_method in ['get', 'post', 'put', 'delete', 'patch', 'head', 'options']:
                if http_method in func.__name__.lower():
                    method = http_method.upper()
                    break
            # 如果还是没找到，使用第一个参数(如果有)
            if not method and len(args) > 1:
                method = str(args[1]).upper()
            # 如果还是没有，标记为未知
            if not method:
                method = "UNKNOWN"
        
        # 确定URL
        url = None
        if 'url' in kwargs:
            url = kwargs['url']
        elif len(args) > 1 and isinstance(args[1], str) and ('http://' in args[1] or 'https://' in args[1]):
            url = args[1]
        
        # 记录请求开始
        log_data = {
            'request_id': request_id,
            'timestamp': datetime.now().isoformat(),
            'method': method,
            'url': url,
        }
        
        # 记录参数（清理敏感信息）
        if 'params' in kwargs:
            log_data['params'] = sanitize_data(kwargs['params'])
        
        # 记录数据（清理敏感信息）
        if 'data' in kwargs:
            log_data['data'] = sanitize_data(kwargs['data'])
        elif 'json' in kwargs:
            log_data['json'] = sanitize_data(kwargs['json'])
        
        # 记录头信息（清理敏感信息）
        if 'headers' in kwargs:
            log_data['headers'] = sanitize_data(kwargs['headers'])
        
        # 记录请求开始
        logger.debug(f"[{request_id}] HTTP请求开始 - {method} {url}")
        
        response = None
        try:
            # 执行请求
            response = func(*args, **kwargs)
            
            # 计算执行时间
            elapsed_time = time.time() - start_time
            
            # 记录响应状态码
            status_code = getattr(response, 'status_code', None)
            log_data['status_code'] = status_code
            log_data['elapsed_time'] = f"{elapsed_time:.3f}s"
            
            # 解析并记录响应内容
            content = parse_response_content(response)
            if content:
                log_data['response'] = content
            
            # 记录成功请求
            log_level = logging.INFO if status_code and 200 <= status_code < 400 else logging.WARNING
            logger.log(log_level, 
                f"[{request_id}] HTTP响应 - {method} {url} - "
                f"状态码: {status_code} - "
                f"耗时: {elapsed_time:.3f}s")
            
            # 如果状态码表示错误，记录更详细的信息
            if status_code and (status_code < 200 or status_code >= 400):
                error_logger.warning(
                    f"HTTP请求返回错误状态码 - {method} {url} - "
                    f"状态码: {status_code} - "
                    f"响应: {content}"
                )
            
            return response
            
        except Exception as e:
            # 计算执行时间
            elapsed_time = time.time() - start_time
            
            # 记录异常信息
            log_data['exception'] = str(e)
            log_data['elapsed_time'] = f"{elapsed_time:.3f}s"
            
            # 获取堆栈跟踪
            stack_trace = traceback.format_exc()
            
            # 记录失败请求
            error_logger.error(
                f"[{request_id}] HTTP请求异常 - {method} {url} - "
                f"异常: {str(e)} - "
                f"耗时: {elapsed_time:.3f}s\n{stack_trace}"
            )
            
            # 重新抛出异常
            raise
            
        finally:
            # 记录完整的请求/响应日志（仅调试级别）
            if logger.isEnabledFor(logging.DEBUG):
                try:
                    logger.debug(f"HTTP请求详情: {json.dumps(log_data, ensure_ascii=False)}")
                except:
                    logger.debug(f"HTTP请求详情: {str(log_data)}")
    
    return wrapper

def enable_http_logging():
    """启用HTTP请求日志（猴子补丁requests库）"""
    # 检查是否已经应用了补丁
    if hasattr(requests, '_http_logging_enabled') and requests._http_logging_enabled:
        return
    
    # 保存原始方法，以便后续可以恢复
    if not hasattr(requests, '_original_methods'):
        requests._original_methods = {}
    
    # 保存原始request方法
    requests._original_methods['request'] = requests.request
    
    # 应用装饰器
    requests.request = log_http_request(requests._original_methods['request'])
    
    # 为其他方法应用装饰器并保存原始方法
    for method_name in ('get', 'post', 'put', 'delete', 'patch', 'head', 'options'):
        requests._original_methods[method_name] = getattr(requests, method_name)
        setattr(requests, method_name, log_http_request(getattr(requests, method_name)))
    
    # 标记已启用
    requests._http_logging_enabled = True
    http_logger.info("已启用HTTP请求日志记录")

def disable_http_logging():
    """禁用HTTP请求日志（恢复requests库原始方法）"""
    # 检查是否已经禁用
    if not hasattr(requests, '_http_logging_enabled') or not requests._http_logging_enabled:
        return
    
    # 检查是否有保存的原始方法
    if not hasattr(requests, '_original_methods'):
        http_logger.warning("无法恢复原始方法，HTTP日志功能将保持启用状态")
        return
        
    # 恢复原始方法
    requests.request = requests._original_methods['request']
    
    # 恢复各HTTP方法的原始函数
    for method_name in ('get', 'post', 'put', 'delete', 'patch', 'head', 'options'):
        if method_name in requests._original_methods:
            setattr(requests, method_name, requests._original_methods[method_name])
    
    # 标记为已禁用
    requests._http_logging_enabled = False
    http_logger.info("已禁用HTTP请求日志记录")

def test_api_endpoint(url, method='get', data=None, headers=None, params=None, expected_status=None):
    """
    测试API端点
    
    Args:
        url: 请求URL
        method: 请求方法，默认为'get'
        data: 请求数据，默认为None
        headers: 请求头，默认为None
        params: 请求参数，默认为None
        expected_status: 预期的状态码，默认为None (自动接受200和204)
        
    Returns:
        测试结果字典，包含成功标志、状态码、响应数据等
    """
    # 确保HTTP日志已启用
    setup_http_logging()
    enable_http_logging()
    
    method = method.lower()
    request_func = getattr(requests, method)
    
    # 重试逻辑
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            start_time = time.time()
            response = request_func(url, json=data, headers=headers, params=params)
            elapsed_time = time.time() - start_time
            
            # 如果未指定预期状态码，则接受200和204为成功
            if expected_status is None:
                expected_status = [200, 204]
            elif not isinstance(expected_status, list):
                expected_status = [expected_status]
            
            # 确定测试是否成功
            success = response.status_code in expected_status
            
            # 解析响应
            try:
                if response.text.strip():  # 只有当响应非空时才尝试解析JSON
                    response_data = response.json()
                else:
                    response_data = "(Empty Response)"
            except:
                response_data = response.text
            
            # 构建测试结果
            result = {
                "success": success,
                "status_code": response.status_code,
                "expected_status": expected_status,
                "elapsed_time": elapsed_time,
                "response": response_data
            }
            
            # 如果是502错误，尝试重试
            if response.status_code == 502 and retry_count < max_retries - 1:
                retry_count += 1
                wait_time = 2 ** retry_count  # 指数退避
                logger.warning(f"收到502网关错误，将在{wait_time}秒后重试 ({retry_count}/{max_retries-1})")
                time.sleep(wait_time)
                continue
            
            # 记录测试结果
            status = "成功" if result["success"] else "失败"
            logger.info(f"API测试 [{status}] {method.upper()} {url} - 状态码: {response.status_code} (预期: {expected_status}), 耗时: {elapsed_time:.3f}秒")
            
            return result
            
        except Exception as e:
            # 记录异常并重试
            if retry_count < max_retries - 1:
                retry_count += 1
                wait_time = 2 ** retry_count
                logger.error(f"API测试异常: {method.upper()} {url} - {str(e)}, 将在{wait_time}秒后重试 ({retry_count}/{max_retries-1})")
                time.sleep(wait_time)
                continue
            else:
                logger.error(f"API测试异常: {method.upper()} {url} - {str(e)}，已达到最大重试次数")
                return {
                    "success": False,
                    "error": str(e),
                    "url": url,
                    "method": method
                }

# 初始化HTTP日志
setup_http_logging()

# 默认启用HTTP日志记录
enable_http_logging() 