# icon_font_setup.py
"""
图标字体设置模块 - 配置KivyMD内置图标字体

该模块负责注册和配置应用程序中使用的图标字体，确保所有图标正确显示。
使用KivyMD内置的Material Design图标，避免依赖外部资源。
"""

import os
from kivy.core.text import LabelBase
from kivymd.icon_definitions import md_icons

# 应用程序路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))

def register_icon_font():
    """
    注册KivyMD内置的Material Design图标字体
    """
    # 检查KivyMD图标字体是否已注册
    if 'Icons' not in LabelBase._fonts:
        try:
            # 使用KivyMD内置的图标字体
            from kivymd.font_definitions import fonts
            
            # 注册所有KivyMD字体
            for font_name, font_data in fonts.items():
                LabelBase.register(
                    name=font_name, 
                    fn_regular=font_data[0],
                    fn_bold=font_data[1] if len(font_data) > 1 else font_data[0],
                    fn_italic=font_data[2] if len(font_data) > 2 else font_data[0],
                    fn_bolditalic=font_data[3] if len(font_data) > 3 else font_data[0]
                )
            print("已注册KivyMD图标字体")
            
            # 验证图标字体是否可用
            if 'Icons' in LabelBase._fonts:
                print(f"图标字体注册成功，可用图标数量: {len(md_icons)}")
            else:
                print("警告: 图标字体注册失败")
                
        except Exception as e:
            print(f"注册图标字体时出错: {e}")
    else:
        print("图标字体已注册")

def get_icon_code(icon_name):
    """
    获取图标的Unicode代码
    
    Args:
        icon_name: 图标名称，如'account', 'home'等
        
    Returns:
        图标的Unicode字符
    """
    if icon_name in md_icons:
        return md_icons[icon_name]
    else:
        print(f"警告: 未找到图标 '{icon_name}'")
        return "\uE000"  # 返回一个占位符图标

def list_available_icons(category=None):
    """
    列出可用的图标名称
    
    Args:
        category: 可选，图标类别前缀，如'account', 'home'等
        
    Returns:
        匹配类别的图标名称列表
    """
    if category:
        return [name for name in md_icons.keys() if name.startswith(category)]
    else:
        return list(md_icons.keys())

# 当模块被直接运行时，注册图标字体
if __name__ == "__main__":
    register_icon_font()