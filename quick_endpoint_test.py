#!/usr/bin/env python3
"""
快速测试API端点
"""

import requests

def quick_test():
    """快速测试"""
    print("=== 快速API端点测试 ===")
    
    base_url = "http://8.138.188.26:80/api"
    custom_id = "U_0002"
    
    # 测试端点
    endpoints = [
        ("GET assessments", f"{base_url}/assessments"),
        ("GET user-health-records", f"{base_url}/user-health-records/{custom_id}"),
        ("POST user-health-records", f"{base_url}/user-health-records/{custom_id}"),
        ("POST assessment-responses", f"{base_url}/assessment-responses"),
    ]
    
    for name, url in endpoints:
        print(f"\n测试: {name}")
        print(f"URL: {url}")
        
        try:
            if "POST" in name:
                response = requests.post(
                    url,
                    json={"test": "data"},
                    headers={"Content-Type": "application/json", "X-User-ID": custom_id},
                    timeout=5,
                    proxies={'http': None, 'https': None}
                )
            else:
                response = requests.get(
                    url,
                    headers={"X-User-ID": custom_id},
                    timeout=5,
                    proxies={'http': None, 'https': None}
                )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 404:
                print("❌ 端点不存在")
            elif response.status_code in [200, 201]:
                print("✅ 端点可用")
            elif response.status_code == 401:
                print("🔐 需要认证（端点存在）")
            elif response.status_code == 422:
                print("📝 数据格式错误（端点存在）")
            else:
                print(f"ℹ️ 状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print("🔌 连接错误")
        except Exception as e:
            print(f"❌ 错误: {str(e)}")

if __name__ == "__main__":
    quick_test()
