"""
字段名称标准化脚本

此脚本用于将移动端和后端的字段名称标准化，使用统一的字段名称：
- 用户ID：custom_id
- 真实姓名：full_name
- 用户名：username
- 用户角色：role

此脚本会更新用户数据文件，确保使用标准字段名称。
"""

import os
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "field_update.log"))
    ]
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("开始字段名称标准化...")
    
    # 导入并运行用户数据更新脚本
    try:
        from utils.update_user_data import update_user_data
        success = update_user_data()
        if success:
            logger.info("用户数据字段名称更新成功")
        else:
            logger.error("用户数据字段名称更新失败")
    except Exception as e:
        logger.error(f"运行用户数据更新脚本失败: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("字段名称标准化完成")

if __name__ == "__main__":
    main()
