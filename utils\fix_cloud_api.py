#!/usr/bin/env python3
"""
修复cloud_api.py文件中的缩进错误
"""
import os
import re

def fix_cloud_api():
    """
    修复cloud_api.py文件中的authenticate方法和register_user方法中的缩进问题
    """
    file_path = os.path.join("utils", "cloud_api.py")
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return False
    
    print(f"开始修复 {file_path}...")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 第一处问题：authenticate方法中的缩进错误
    auth_problem_code = """            if response.status_code == 200:
                try:
                result = response.json()
                if result.get("success"):"""
                
    auth_fixed_code = """            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("success"):"""
                
    content = content.replace(auth_problem_code, auth_fixed_code)
    
    # 第二处问题：在else下面的缩进错误
    else_problem_code = """            else:
                        error_msg = str(error_detail)
                
                    logger.error(f"验证失败(422): {error_msg}")
                    self.last_error = f"请求格式错误: {error_msg}"
        except Exception as e:
                    logger.error(f"验证失败: 请求格式错误(422): {str(e)}")
                    self.last_error = "请求数据验证失败"
                return None"""
    
    else_fixed_code = """                    else:
                        error_msg = str(error_detail)
                
                    logger.error(f"验证失败(422): {error_msg}")
                    self.last_error = f"请求格式错误: {error_msg}"
                except Exception as e:
                    logger.error(f"验证失败: 请求格式错误(422): {str(e)}")
                    self.last_error = "请求数据验证失败"
                return None"""
    
    content = content.replace(else_problem_code, else_fixed_code)
    
    # 第三处问题：在register_user方法中的缩进问题
    register_problem_code = """        # 实现指数退避重试机制
        while retry_count <= max_retries:
            try:
                # 准备请求数据"""
                
    register_fixed_code = """        # 实现指数退避重试机制
        while retry_count <= max_retries:
            try:
                # 准备请求数据"""

    content = content.replace(register_problem_code, register_fixed_code)
    
    # 第四处问题
    if_response_problem = """            if response.status_code == 200:
                result = response.json()
                if result.get("success"):"""
                
    if_response_fixed = """                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):"""
    
    content = content.replace(if_response_problem, if_response_fixed)
    
    # 保存修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"修复完成. 已替换 {file_path} 中的缩进问题代码")
    return True

if __name__ == "__main__":
    fix_cloud_api() 