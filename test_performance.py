#!/usr/bin/env python3
"""
性能测试脚本
用于测试移动端应用的性能优化效果
"""
import time
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_import_performance():
    """测试模块导入性能"""
    logger.info("=== 测试模块导入性能 ===")

    # 测试主要模块导入时间
    modules_to_test = [
        'utils.screen_loader',
        'utils.memory_utils',
        'utils.performance_monitor',
        'theme',
        'utils.app_config'
    ]

    total_import_time = 0

    for module_name in modules_to_test:
        start_time = time.time()
        try:
            __import__(module_name)
            import_time = (time.time() - start_time) * 1000
            total_import_time += import_time
            logger.info(f"  {module_name}: {import_time:.2f}ms")
        except ImportError as e:
            logger.error(f"  {module_name}: 导入失败 - {e}")

    logger.info(f"总导入时间: {total_import_time:.2f}ms")
    return total_import_time

def test_screen_loader():
    """测试屏幕懒加载功能"""
    logger.info("=== 测试屏幕懒加载功能 ===")

    try:
        from utils.screen_loader import ScreenLoader

        # 创建屏幕加载器
        loader = ScreenLoader()

        # 测试屏幕配置
        configs = loader._screen_configs
        logger.info(f"配置的屏幕数量: {len(configs)}")

        for screen_name in configs.keys():
            logger.info(f"  - {screen_name}")

        # 测试屏幕加载状态检查
        test_screen = 'homepage_screen'
        is_loaded_before = loader.is_screen_loaded(test_screen)
        logger.info(f"{test_screen} 加载状态: {is_loaded_before}")

        return True

    except Exception as e:
        logger.error(f"屏幕加载器测试失败: {e}")
        return False

def test_memory_utils():
    """测试内存管理工具"""
    logger.info("=== 测试内存管理工具 ===")

    try:
        from utils.memory_utils import check_for_circular_references, get_memory_stats

        # 测试循环引用检查
        start_time = time.time()
        cleaned = check_for_circular_references()
        check_time = (time.time() - start_time) * 1000

        logger.info(f"循环引用检查: 清理 {cleaned} 个对象，耗时 {check_time:.2f}ms")

        # 测试内存统计
        try:
            stats = get_memory_stats()
            if 'error' not in stats:
                logger.info(f"进程内存使用: {stats.get('process_rss_mb', 0):.2f}MB")
                logger.info(f"系统内存使用率: {stats.get('system_used_percent', 0):.1f}%")
            else:
                logger.warning(f"内存统计获取失败: {stats['error']}")
        except Exception as e:
            logger.warning(f"内存统计测试失败: {e}")

        return True

    except Exception as e:
        logger.error(f"内存工具测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控器"""
    logger.info("=== 测试性能监控器 ===")

    try:
        from utils.performance_monitor import (
            get_performance_monitor,
            get_startup_timer,
            timing_decorator
        )

        # 测试性能监控器
        monitor = get_performance_monitor()

        # 记录测试指标
        monitor.record_metric("test_metric", 100, "ms")
        monitor.record_metric("test_metric", 150, "ms")

        # 获取平均值
        avg = monitor.get_average("test_metric")
        logger.info(f"测试指标平均值: {avg:.2f}ms")

        # 测试启动计时器
        timer = get_startup_timer()
        timer.start_phase("测试阶段")
        time.sleep(0.1)  # 模拟耗时操作
        timer.end_phase("测试阶段")

        summary = timer.get_phase_summary()
        logger.info(f"测试阶段耗时: {summary['phases'].get('测试阶段', {}).get('duration', 0):.2f}ms")

        # 测试装饰器
        @timing_decorator("test_function")
        def test_function():
            time.sleep(0.05)
            return "test"

        result = test_function()
        logger.info(f"装饰器测试结果: {result}")

        return True

    except Exception as e:
        logger.error(f"性能监控器测试失败: {e}")
        return False

def test_font_optimization():
    """测试字体优化"""
    logger.info("=== 测试字体优化 ===")

    try:
        # 检查字体文件是否存在
        fonts_dir = os.path.join(os.path.dirname(__file__), 'assets', 'fonts')

        if not os.path.exists(fonts_dir):
            logger.warning("字体目录不存在，跳过字体测试")
            return True

        essential_fonts = [
            "NotoSansSC-Regular.ttf",
            "NotoSansSC-Medium.ttf"
        ]

        font_count = 0
        for font_file in essential_fonts:
            font_path = os.path.join(fonts_dir, font_file)
            if os.path.exists(font_path) and os.path.getsize(font_path) > 0:
                font_count += 1
                logger.info(f"  ✓ {font_file}")
            else:
                logger.warning(f"  ✗ {font_file} (不存在或为空)")

        logger.info(f"可用必要字体: {font_count}/{len(essential_fonts)}")
        return font_count > 0

    except Exception as e:
        logger.error(f"字体优化测试失败: {e}")
        return False

def run_all_tests():
    """运行所有性能测试"""
    logger.info("开始性能测试...")

    test_results = {}

    # 运行各项测试
    test_results['import_time'] = test_import_performance()
    test_results['screen_loader'] = test_screen_loader()
    test_results['memory_utils'] = test_memory_utils()
    test_results['performance_monitor'] = test_performance_monitor()
    test_results['font_optimization'] = test_font_optimization()

    # 汇总结果
    logger.info("=== 测试结果汇总 ===")

    passed_tests = sum(1 for result in test_results.values() if result is True)
    total_tests = len([k for k in test_results.keys() if k != 'import_time'])

    logger.info(f"通过测试: {passed_tests}/{total_tests}")

    if isinstance(test_results['import_time'], (int, float)):
        logger.info(f"总导入时间: {test_results['import_time']:.2f}ms")

    for test_name, result in test_results.items():
        if test_name != 'import_time':
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"  {test_name}: {status}")

    # 性能评估
    if isinstance(test_results['import_time'], (int, float)):
        if test_results['import_time'] < 100:
            logger.info("导入性能: 优秀")
        elif test_results['import_time'] < 200:
            logger.info("导入性能: 良好")
        else:
            logger.info("导入性能: 需要优化")

    return test_results

if __name__ == "__main__":
    try:
        results = run_all_tests()

        # 如果所有测试都通过，退出码为0
        failed_tests = sum(1 for result in results.values() if result is False)
        sys.exit(failed_tests)

    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
