# utils/storage.py
import os
import json
import time
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

class UserStorage:
    """用户数据存储类，用于管理用户信息的持久化存储和与后端数据库的同步"""

    # 用户数据文件路径
    USER_DATA_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "user_data.json")

    # 同步锁，防止多线程同时操作数据
    _sync_lock = threading.Lock()

    # 上次同步时间
    _last_sync_time = 0

    # 同步间隔（秒）
    SYNC_INTERVAL = 300  # 5分钟

    @staticmethod
    def save_user_data(user_data: Dict[str, Any], sync_to_backend: bool = True) -> bool:
        """保存用户数据到本地存储，并可选择同步到后端数据库

        Args:
            user_data: 要保存的用户数据
            sync_to_backend: 是否同步到后端数据库，默认为True

        Returns:
            bool: 保存是否成功
        """
        try:
            with UserStorage._sync_lock:
                # 检查文件是否存在，如果存在则保留其他数据
                existing_data: Dict[str, Any] = {}
                if os.path.exists(UserStorage.USER_DATA_FILE):
                    try:
                        with open(UserStorage.USER_DATA_FILE, "r", encoding='utf-8') as f:
                            file_content = f.read().strip()
                            if file_content:  # 确保文件不为空
                                existing_data = json.loads(file_content)
                            else:
                                # 文件存在但为空，初始化为空的账户列表
                                existing_data = {"accounts": []}
                    except json.JSONDecodeError as e:
                        # 文件格式不正确，初始化为空的账户列表
                        logging.error(f"读取用户数据文件失败，文件可能为空或格式不正确: {str(e)}")
                        existing_data = {"accounts": []}
                    except Exception as e:
                        logging.error(f"读取用户数据文件失败: {str(e)}")
                        existing_data = {"accounts": []}
                else:
                    # 文件不存在，初始化为空的账户列表
                    existing_data = {"accounts": []}

                # 确保accounts字段存在
                if "accounts" not in existing_data:
                    existing_data["accounts"] = []

                # 创建用户数据的副本
                user_data_copy = user_data.copy()

                # 处理accounts数组，确保每个用户的所有信息都被正确保存
                if "accounts" in user_data_copy:
                    new_accounts = user_data_copy["accounts"]
                    existing_accounts = existing_data["accounts"]

                    # 创建现有账户ID的映射，用于快速查找
                    existing_account_map = {acc.get("user_id"): i for i, acc in enumerate(existing_accounts) if "user_id" in acc}

                    # 合并账户数据
                    for new_account in new_accounts:
                        user_id = new_account.get("user_id")
                        if user_id and user_id in existing_account_map:
                            # 更新现有账户
                            index = existing_account_map[user_id]
                            existing_account = existing_accounts[index]

                            # 保留原有账户的所有字段
                            updated_account = existing_account.copy()

                            # 更新账户信息，保留密码等敏感信息
                            for key, value in new_account.items():
                                # 如果新账户没有提供密码，保留原有密码
                                if key == "password" and (not value or value == ""):
                                    continue
                                # 更新其他字段
                                updated_account[key] = value

                            # 更新到现有账户列表
                            existing_accounts[index] = updated_account
                        elif user_id:  # 只添加有用户ID的新账户
                            # 添加新账户
                            existing_accounts.append(new_account)

                    # 更新accounts数组
                    existing_data["accounts"] = existing_accounts

                    # 从副本中移除已处理的accounts字段，避免重复处理
                    del user_data_copy["accounts"]

                # 更新顶层数据（当前登录用户信息）
                if "user_id" in user_data_copy:
                    # 更新当前用户的所有信息到顶层
                    current_user_fields = [
                        "username", "user_id", "real_name", "identity",
                        "last_login", "remember_password", "password"
                    ]
                    for key in current_user_fields:
                        if key in user_data_copy:
                            existing_data[key] = user_data_copy[key]

                # 添加最后更新时间
                existing_data["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 创建目录（如果需要）
                os.makedirs(os.path.dirname(UserStorage.USER_DATA_FILE), exist_ok=True)

                # 保存完整数据到本地
                with open(UserStorage.USER_DATA_FILE, "w", encoding='utf-8') as f:
                    json.dump(existing_data, f, ensure_ascii=False, indent=2)

                return True
        except Exception as e:
            logging.error(f"保存用户数据失败: {str(e)}")
            print(f"保存用户数据失败: {str(e)}")
            return False

    @staticmethod
    def get_user_data(sync_from_backend: bool = False) -> Dict[str, Any]:
        """从本地存储获取用户数据，并可选择从后端同步

        Args:
            sync_from_backend: 是否从后端同步数据，默认为False

        Returns:
            用户数据字典，如果不存在则返回空字典
        """
        try:
            with UserStorage._sync_lock:
                local_data: Dict[str, Any] = {}
                if os.path.exists(UserStorage.USER_DATA_FILE) and os.path.getsize(UserStorage.USER_DATA_FILE) > 0:
                    try:
                        with open(UserStorage.USER_DATA_FILE, "r", encoding='utf-8') as f:
                            local_data = json.load(f)
                    except Exception as e:
                        logging.error(f"读取用户数据文件失败: {str(e)}")
                        # 文件存在但格式不正确，创建一个空的但有效的JSON结构
                        with open(UserStorage.USER_DATA_FILE, "w", encoding='utf-8') as f:
                            json.dump({"accounts": []}, f)
                else:
                    # 文件不存在或为空，创建一个空的但有效的JSON结构
                    os.makedirs(os.path.dirname(UserStorage.USER_DATA_FILE), exist_ok=True)
                    with open(UserStorage.USER_DATA_FILE, "w", encoding='utf-8') as f:
                        json.dump({"accounts": []}, f)

                # 如果需要从后端同步数据
                if sync_from_backend and local_data and "user_id" in local_data:
                    # 检查是否需要同步（根据同步间隔）
                    current_time = time.time()
                    if current_time - UserStorage._last_sync_time >= UserStorage.SYNC_INTERVAL:
                        UserStorage._last_sync_time = current_time
                        # 从后端同步数据
                        backend_data = UserStorage.sync_from_backend(local_data["user_id"])
                        if backend_data and backend_data.get("success"):
                            # 合并后端数据和本地数据，优先使用后端数据
                            if "user_data" in backend_data:
                                merged_data = local_data.copy()
                                merged_data.update(backend_data["user_data"])
                                # 保存合并后的数据到本地，但不再同步到后端
                                UserStorage.save_user_data(merged_data, sync_to_backend=False)
                                return merged_data

                return local_data
        except Exception as e:
            logging.error(f"获取用户数据失败: {str(e)}")
            print(f"获取用户数据失败: {str(e)}")
            return {"accounts": []}

    @staticmethod
    def clear_user_data(sync_to_backend=True):
        """清除用户数据

        Args:
            sync_to_backend: 是否同步删除后端数据，默认为True

        Returns:
            bool: 清除是否成功
        """
        try:
            with UserStorage._sync_lock:
                # 如果需要同步到后端，先获取用户ID
                user_id = None
                if sync_to_backend and os.path.exists(UserStorage.USER_DATA_FILE):
                    try:
                        with open(UserStorage.USER_DATA_FILE, "r", encoding='utf-8') as f:
                            data = json.load(f)
                            user_id = data.get("user_id")
                    except Exception:
                        pass

                # 删除本地文件
                if os.path.exists(UserStorage.USER_DATA_FILE):
                    os.remove(UserStorage.USER_DATA_FILE)

                # 如果需要同步到后端且有用户ID，则删除后端数据
                if sync_to_backend and user_id:
                    # 使用线程异步删除，避免阻塞UI
                    delete_thread = threading.Thread(target=UserStorage.delete_from_backend, args=(user_id,))
                    delete_thread.daemon = True
                    delete_thread.start()

                return True
        except Exception as e:
            logging.error(f"清除用户数据失败: {str(e)}")
            print(f"清除用户数据失败: {str(e)}")
            return False

    @staticmethod
    def update_user_setting(key, value, sync_to_backend=True):
        """更新用户设置

        Args:
            key: 设置键名
            value: 设置值
            sync_to_backend: 是否同步到后端，默认为True

        Returns:
            bool: 更新是否成功
        """
        try:
            user_data = UserStorage.get_user_data() or {}
            user_data[key] = value
            return UserStorage.save_user_data(user_data, sync_to_backend=sync_to_backend)
        except Exception as e:
            logging.error(f"更新用户设置失败: {str(e)}")
            print(f"更新用户设置失败: {str(e)}")
            return False

    @staticmethod
    def get_user_setting(key, default=None):
        """获取用户设置

        Args:
            key: 设置键名
            default: 默认值，如果设置不存在则返回该值

        Returns:
            设置值
        """
        try:
            user_data = UserStorage.get_user_data() or {}
            return user_data.get(key, default)
        except Exception as e:
            logging.error(f"获取用户设置失败: {str(e)}")
            print(f"获取用户设置失败: {str(e)}")
            return default

    @staticmethod
    def sync_to_backend(user_data):
        """同步用户数据到后端

        Args:
            user_data: 用户数据字典

        Returns:
            dict: 同步结果
        """
        try:
            from api.user_api import UserAPI
            api = UserAPI()
            result = api.sync_user_data(user_data)
            if result.get("success"):
                logging.info(f"用户数据同步到后端成功: {user_data.get('user_id')}")
            else:
                logging.warning(f"用户数据同步到后端失败: {result.get('message')}")
            return result
        except Exception as e:
            logging.error(f"同步用户数据到后端时出错: {str(e)}")
            return {"success": False, "message": str(e)}

    @staticmethod
    def sync_from_backend(user_id):
        """从后端同步用户数据

        Args:
            user_id: 用户ID

        Returns:
            dict: 包含用户数据的字典
        """
        try:
            from api.user_api import UserAPI
            api = UserAPI()
            result = api.get_user_data(user_id)
            if result.get("success"):
                logging.info(f"从后端同步用户数据成功: {user_id}")
            else:
                logging.warning(f"从后端同步用户数据失败: {result.get('message')}")
            return result
        except Exception as e:
            logging.error(f"从后端同步用户数据时出错: {str(e)}")
            return {"success": False, "message": str(e)}

    @staticmethod
    def delete_from_backend(user_id):
        """从后端服务器删除用户数据"""
        try:
            # 模拟后端API调用
            time.sleep(0.5)  # 模拟网络延迟
            # 这里应该实现真正的API调用
            return {"success": True, "message": "用户数据已从服务器删除"}
        except Exception as e:
            logging.error(f"从后端删除用户数据失败: {str(e)}")
            return {"success": False, "message": f"从后端删除用户数据失败: {str(e)}"}

    @staticmethod
    def save_token(token):
        """保存访问令牌

        Args:
            token: 访问令牌

        Returns:
            bool: 保存是否成功
        """
        try:
            user_data = UserStorage.get_user_data() or {}
            user_data["access_token"] = token
            user_data["token_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return UserStorage.save_user_data(user_data, sync_to_backend=False)
        except Exception as e:
            logging.error(f"保存访问令牌失败: {str(e)}")
            print(f"保存访问令牌失败: {str(e)}")
            return False

    @staticmethod
    def save_user_info(user_info):
        """保存用户信息

        Args:
            user_info: 用户信息字典

        Returns:
            bool: 保存是否成功
        """
        try:
            user_data = UserStorage.get_user_data() or {}

            # 更新用户信息
            if isinstance(user_info, dict):
                # 将用户信息合并到用户数据中
                for key, value in user_info.items():
                    user_data[key] = value

                # 确保有user_id字段
                if "id" in user_info and "user_id" not in user_data:
                    user_data["user_id"] = str(user_info["id"])

                # 确保有username字段
                if "username" in user_info and "username" not in user_data:
                    user_data["username"] = user_info["username"]

                # 添加最后更新时间
                user_data["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                return UserStorage.save_user_data(user_data, sync_to_backend=False)
            else:
                logging.error(f"用户信息格式不正确: {type(user_info)}")
                return False
        except Exception as e:
            logging.error(f"保存用户信息失败: {str(e)}")
            print(f"保存用户信息失败: {str(e)}")
            return False

    @staticmethod
    def save_health_info(health_info: Dict[str, Any]) -> Dict[str, Any]:
        """保存用户健康信息

        Args:
            health_info: 要保存的健康信息

        Returns:
            Dict: 包含保存结果的字典
        """
        try:
            with UserStorage._sync_lock:
                # 检查文件是否存在
                health_data_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "health_data.json")

                existing_data: List[Dict[str, Any]] = []
                if os.path.exists(health_data_file):
                    try:
                        with open(health_data_file, "r", encoding='utf-8') as f:
                            file_content = f.read().strip()
                            if file_content:  # 确保文件不为空
                                existing_data = json.loads(file_content)
                            else:
                                # 文件存在但为空，初始化为空列表
                                existing_data = []
                    except Exception as e:
                        logging.error(f"读取健康数据文件失败: {str(e)}")
                        existing_data = []

                # 确保 existing_data 是一个列表
                if not isinstance(existing_data, list):
                    existing_data = []

                # 添加或更新健康信息
                user_id = health_info.get("user_id", None)
                if not user_id:
                    return {"success": False, "message": "未提供用户ID"}

                # 为健康信息添加唯一ID
                if "id" not in health_info:
                    health_info["id"] = f"health_{int(time.time())}_{user_id}"

                # 查找用户现有的健康信息
                found = False
                for i, item in enumerate(existing_data):
                    if item.get("user_id") == user_id:
                        # 更新现有健康信息
                        existing_data[i] = health_info
                        found = True
                        break

                # 如果不存在，则添加新的健康信息
                if not found:
                    existing_data.append(health_info)

                # 创建目录（如果需要）
                os.makedirs(os.path.dirname(health_data_file), exist_ok=True)

                # 保存健康信息到本地
                with open(health_data_file, "w", encoding='utf-8') as f:
                    json.dump(existing_data, f, ensure_ascii=False, indent=2)

                # 同步到后端服务器
                # 这里可以添加与后端同步的代码

                return {"success": True, "message": "健康信息保存成功"}
        except Exception as e:
            logging.error(f"保存健康信息失败: {str(e)}")
            return {"success": False, "message": f"保存健康信息失败: {str(e)}"}

    @staticmethod
    def get_health_info(user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户的健康信息

        Args:
            user_id: 用户ID

        Returns:
            Optional[Dict[str, Any]]: 用户的健康信息，如不存在则返回None
        """
        try:
            health_data_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "health_data.json")

            if not os.path.exists(health_data_file):
                return None

            with open(health_data_file, "r", encoding='utf-8') as f:
                health_data = json.load(f)

                # 查找对应用户的健康信息
                for item in health_data:
                    if item.get("user_id") == user_id:
                        return item

                return None
        except Exception as e:
            logging.error(f"获取健康信息失败: {str(e)}")
            return None

# 单例模式，确保只有一个Storage实例
_storage_instance = None

def get_storage() -> UserStorage:
    """获取Storage实例"""
    global _storage_instance
    if _storage_instance is None:
        _storage_instance = UserStorage()
    return _storage_instance