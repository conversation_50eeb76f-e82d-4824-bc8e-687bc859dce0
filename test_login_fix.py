#!/usr/bin/env python3
"""
测试登录修复是否有效
"""

import os
import sys
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_proxy_fix():
    """测试代理修复"""
    print("=== 测试代理修复 ===")
    
    # 1. 导入代理配置模块（这会自动禁用代理）
    try:
        from utils.proxy_config import initialize_proxy_config, check_proxy_status
        
        # 手动初始化代理配置
        initialize_proxy_config()
        
        # 检查代理状态
        status = check_proxy_status()
        print(f"代理状态: {status}")
        
        if not status['has_proxy_vars']:
            print("✅ 代理环境变量已成功清除")
            return True
        else:
            print("❌ 仍有代理环境变量存在")
            return False
            
    except Exception as e:
        print(f"❌ 代理配置测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n=== 测试API连接 ===")
    
    try:
        import requests
        
        # 测试直接连接到服务器
        test_url = "http://8.138.188.26:80/api/health"
        
        response = requests.get(
            test_url,
            timeout=10,
            proxies={
                'http': None,
                'https': None
            }
        )
        
        print(f"✅ API连接成功: {response.status_code}")
        return True
        
    except requests.exceptions.ProxyError as e:
        print(f"❌ 代理错误: {e}")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_local_api_client():
    """测试本地API客户端"""
    print("\n=== 测试本地API客户端 ===")
    
    try:
        from utils.local_api_client import LocalApiClient
        
        client = LocalApiClient()
        
        # 测试健康检查
        health_result = client.check_server_health()
        print(f"健康检查结果: {health_result}")
        
        if health_result and health_result.get('any_server_online', False):
            print("✅ 本地API客户端连接成功")
            return True
        else:
            print("❌ 本地API客户端连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 本地API客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cloud_api():
    """测试云API客户端"""
    print("\n=== 测试云API客户端 ===")
    
    try:
        from utils.cloud_api import get_cloud_api
        
        cloud_api = get_cloud_api()
        
        # 测试健康检查
        health_result = cloud_api.check_server_health()
        print(f"云API健康检查结果: {health_result}")
        
        if health_result and health_result.get('online', False):
            print("✅ 云API客户端连接成功")
            return True
        else:
            print("❌ 云API客户端连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 云API客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始登录修复测试...")
    
    # 测试结果
    results = []
    
    # 1. 测试代理修复
    results.append(("代理修复", test_proxy_fix()))
    
    # 2. 测试API连接
    results.append(("API连接", test_api_connection()))
    
    # 3. 测试本地API客户端
    results.append(("本地API客户端", test_local_api_client()))
    
    # 4. 测试云API客户端
    results.append(("云API客户端", test_cloud_api()))
    
    # 总结结果
    print("\n=== 测试总结 ===")
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 个测试通过")
    
    if success_count >= 2:  # 至少有2个测试通过就认为修复成功
        print("🎉 登录修复成功！")
        return True
    else:
        print("💥 登录修复失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
