#!/usr/bin/env python3
"""
简单的API端点测试
"""

import requests

def test_simple():
    """简单测试"""
    base_url = "http://8.138.188.26:80/api"
    
    endpoints = [
        "assessments",
        "questionnaires", 
        "documents",
        "login"
    ]
    
    print(f"测试服务器: {base_url}")
    
    for endpoint in endpoints:
        url = f"{base_url}/{endpoint}"
        print(f"\n测试: {url}")
        
        try:
            response = requests.get(url, timeout=5, proxies={'http': None, 'https': None})
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✓ 端点可访问")
            elif response.status_code == 401:
                print("✓ 端点存在（需要认证）")
            elif response.status_code == 404:
                print("✗ 端点不存在 (404)")
            else:
                print(f"? 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"✗ 请求失败: {str(e)}")

if __name__ == "__main__":
    test_simple()
