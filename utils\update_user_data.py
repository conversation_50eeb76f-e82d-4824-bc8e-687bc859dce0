"""
更新用户数据字段名称脚本

此脚本用于将移动端用户数据中的字段名称标准化，使用后端API的字段名称。
- 用户ID：custom_id（替代user_id）
- 真实姓名：full_name（替代real_name）
- 用户名：username（保持不变）
- 用户角色：role（替代identity）

此脚本会读取user_data.json文件，更新字段名称，并保存回原文件。
"""

import os
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 用户数据文件路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)
USER_DATA_FILE = os.path.join(APP_DIR, "user_data.json")

def update_user_data():
    """更新用户数据字段名称"""
    try:
        # 检查文件是否存在
        if not os.path.exists(USER_DATA_FILE):
            logger.error(f"用户数据文件不存在: {USER_DATA_FILE}")
            return False

        # 读取用户数据
        with open(USER_DATA_FILE, "r", encoding="utf-8") as f:
            try:
                user_data = json.load(f)
            except json.JSONDecodeError:
                logger.error("用户数据文件格式错误")
                return False

        # 创建备份
        backup_file = f"{USER_DATA_FILE}.bak.{int(datetime.now().timestamp())}"
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(user_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已创建备份: {backup_file}")

        # 更新顶层字段
        updated_data = update_fields(user_data)

        # 更新账户列表中的字段
        if "accounts" in updated_data and isinstance(updated_data["accounts"], list):
            for i, account in enumerate(updated_data["accounts"]):
                if isinstance(account, dict):
                    updated_data["accounts"][i] = update_fields(account)

        # 保存更新后的数据
        with open(USER_DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(updated_data, f, ensure_ascii=False, indent=2)

        logger.info("用户数据字段名称已更新")
        return True

    except Exception as e:
        logger.error(f"更新用户数据字段名称失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_fields(data):
    """更新字段名称

    Args:
        data (dict): 用户数据字典

    Returns:
        dict: 更新后的用户数据字典
    """
    if not isinstance(data, dict):
        return data

    updated_data = data.copy()

    # 添加标准字段名
    # 1. 如果有real_name，添加full_name
    if "real_name" in data:
        updated_data["full_name"] = data["real_name"]
    
    # 2. 如果有identity，添加role
    if "identity" in data:
        updated_data["role"] = data["identity"]
    
    # 3. 如果有user_id但没有custom_id，添加custom_id
    if "user_id" in data and "custom_id" not in data:
        # 如果user_id看起来像是格式化的ID（包含前缀），则使用它作为custom_id
        user_id = data["user_id"]
        if isinstance(user_id, str) and ("_" in user_id or user_id.startswith("P") or user_id.startswith("D") or user_id.startswith("U") or user_id.startswith("SM")):
            updated_data["custom_id"] = user_id

    return updated_data

if __name__ == "__main__":
    logger.info("开始更新用户数据字段名称...")
    success = update_user_data()
    if success:
        logger.info("用户数据字段名称更新成功")
    else:
        logger.error("用户数据字段名称更新失败")
