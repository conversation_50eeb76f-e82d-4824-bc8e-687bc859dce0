# -*- coding: utf-8 -*-
"""
用户认证管理器
统一管理用户认证相关功能，简化认证流程
"""

import os
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class AuthManager:
    """用户认证管理器，统一处理用户认证相关操作"""

    _instance = None
    _initialized = False

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(AuthManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化认证管理器"""
        if self._initialized:
            return

        self.current_user = None
        self.api_client = None
        self._user_data_cache = None
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 缓存5分钟

        # 数据文件路径
        self.app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.user_data_file = os.path.join(self.app_dir, "user_data.json")
        self.auth_info_file = os.path.join(self.app_dir, "data", "auth_info.json")

        self._initialized = True
        logger.info("认证管理器初始化完成")

    def get_current_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息

        Returns:
            Optional[Dict]: 用户信息字典，包含custom_id, username, token等
        """
        try:
            # 检查缓存是否有效
            current_time = datetime.now().timestamp()
            if (self._user_data_cache and
                current_time - self._cache_timestamp < self._cache_ttl):
                return self._user_data_cache

            # 1. 优先从current_user获取
            if self.current_user and hasattr(self.current_user, 'custom_id'):
                user_info = {
                    'custom_id': self.current_user.custom_id,
                    'username': getattr(self.current_user, 'username', ''),
                    'user_id': getattr(self.current_user, 'user_id', ''),
                    'full_name': getattr(self.current_user, 'full_name', ''),
                    'phone': getattr(self.current_user, 'phone', ''),
                    'role': getattr(self.current_user, 'role', '个人用户')
                }

                # 获取token
                token = self._get_user_token()
                if token:
                    user_info['access_token'] = token

                # 更新缓存
                self._user_data_cache = user_info
                self._cache_timestamp = current_time

                logger.info(f"从current_user获取用户信息: {user_info.get('username', 'Unknown')}")
                return user_info

            # 2. 从UserStorage获取
            user_info = self._get_user_from_storage()
            if user_info:
                # 更新缓存
                self._user_data_cache = user_info
                self._cache_timestamp = current_time
                return user_info

            # 3. 从认证信息文件获取
            user_info = self._get_user_from_auth_file()
            if user_info:
                # 更新缓存
                self._user_data_cache = user_info
                self._cache_timestamp = current_time
                return user_info

            logger.warning("未找到有效的用户信息")
            return None

        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return None

    def _get_user_token(self) -> Optional[str]:
        """获取用户token"""
        try:
            # 1. 从api_client获取
            if (self.api_client and
                hasattr(self.api_client, 'cloud_api') and
                self.api_client.cloud_api and
                hasattr(self.api_client.cloud_api, 'token') and
                self.api_client.cloud_api.token):
                return self.api_client.cloud_api.token

            # 2. 从UserStorage获取
            try:
                from utils.storage import UserStorage
                user_data = UserStorage.get_user_data(sync_from_backend=False)
                if user_data and 'access_token' in user_data:
                    return user_data['access_token']
            except ImportError:
                pass

            # 3. 从认证信息文件获取
            # 尝试多个可能的认证文件路径
            auth_files = [
                self.auth_info_file,
                os.path.join(self.app_dir, "data", "cloud_auth.json"),
                os.path.join(self.app_dir, "cloud_auth.json")
            ]

            for auth_file in auth_files:
                if os.path.exists(auth_file):
                    try:
                        with open(auth_file, 'r', encoding='utf-8') as f:
                            auth_data = json.load(f)
                            token = auth_data.get('token')
                            if token:
                                logger.info(f"从认证文件获取到token: {auth_file}")
                                return token
                    except Exception as e:
                        logger.warning(f"读取认证文件失败 {auth_file}: {str(e)}")
                        continue

            return None

        except Exception as e:
            logger.error(f"获取token失败: {str(e)}")
            return None

    def _get_user_from_storage(self) -> Optional[Dict[str, Any]]:
        """从UserStorage获取用户信息"""
        try:
            from utils.storage import UserStorage
            user_data = UserStorage.get_user_data(sync_from_backend=False)

            if user_data and 'custom_id' in user_data:
                logger.info(f"从UserStorage获取用户信息: {user_data.get('username', 'Unknown')}")
                return user_data

            return None

        except ImportError:
            logger.warning("无法导入UserStorage")
            return None
        except Exception as e:
            logger.error(f"从UserStorage获取用户信息失败: {str(e)}")
            return None

    def _get_user_from_auth_file(self) -> Optional[Dict[str, Any]]:
        """从认证信息文件获取用户信息"""
        try:
            # 尝试多个可能的认证文件路径
            auth_files = [
                self.auth_info_file,
                os.path.join(self.app_dir, "data", "cloud_auth.json"),
                os.path.join(self.app_dir, "cloud_auth.json")
            ]

            for auth_file in auth_files:
                if os.path.exists(auth_file):
                    try:
                        with open(auth_file, 'r', encoding='utf-8') as f:
                            auth_data = json.load(f)

                        if auth_data.get('custom_id'):
                            user_info = {
                                'custom_id': auth_data['custom_id'],
                                'username': auth_data.get('username', ''),
                                'access_token': auth_data.get('token', '')
                            }
                            logger.info(f"从认证文件获取用户信息: {auth_file}, username: {user_info.get('username', 'Unknown')}")
                            return user_info
                    except Exception as e:
                        logger.warning(f"读取认证文件失败 {auth_file}: {str(e)}")
                        continue

            return None

        except Exception as e:
            logger.error(f"从认证文件获取用户信息失败: {str(e)}")
            return None

    def setup_api_client(self, api_client):
        """设置API客户端

        Args:
            api_client: API客户端实例
        """
        self.api_client = api_client

        # 获取用户信息并配置API客户端
        user_info = self.get_current_user_info()
        if user_info and api_client:
            self._configure_api_client(api_client, user_info)

    def _configure_api_client(self, api_client, user_info: Dict[str, Any]):
        """配置API客户端的认证信息

        Args:
            api_client: API客户端实例
            user_info: 用户信息字典
        """
        try:
            if hasattr(api_client, 'cloud_api') and api_client.cloud_api:
                cloud_api = api_client.cloud_api

                # 设置custom_id
                if user_info.get('custom_id'):
                    cloud_api.custom_id = user_info['custom_id']
                    logger.info(f"设置API客户端custom_id: {user_info['custom_id']}")

                # 设置token
                if user_info.get('access_token'):
                    cloud_api.token = user_info['access_token']
                    logger.info("设置API客户端token")

                # 保存认证信息
                if hasattr(cloud_api, 'save_auth_info'):
                    cloud_api.save_auth_info()

        except Exception as e:
            logger.error(f"配置API客户端失败: {str(e)}")

    def set_current_user(self, user):
        """设置当前用户

        Args:
            user: 用户对象
        """
        self.current_user = user
        # 清除缓存，强制重新获取用户信息
        self._user_data_cache = None
        self._cache_timestamp = 0

        logger.info(f"设置当前用户: {getattr(user, 'username', 'Unknown')}")

    def clear_cache(self):
        """清除用户信息缓存"""
        self._user_data_cache = None
        self._cache_timestamp = 0
        logger.info("清除用户信息缓存")

    def is_user_authenticated(self) -> bool:
        """检查用户是否已认证

        Returns:
            bool: 用户是否已认证
        """
        user_info = self.get_current_user_info()
        return bool(user_info and user_info.get('custom_id'))

    def get_auth_headers(self) -> Dict[str, str]:
        """获取认证请求头

        Returns:
            Dict[str, str]: 包含认证信息的请求头
        """
        headers = {}

        user_info = self.get_current_user_info()
        if user_info:
            # 添加custom_id
            if user_info.get('custom_id'):
                headers['X-User-ID'] = user_info['custom_id']

            # 添加token
            if user_info.get('access_token'):
                headers['Authorization'] = f"Bearer {user_info['access_token']}"

        return headers

    def reload_user_auth(self):
        """重新加载用户认证信息"""
        try:
            # 清除缓存
            self.clear_cache()

            # 尝试从存储中获取用户信息
            from utils.storage import UserStorage
            user_data = UserStorage.get_user_data(sync_from_backend=False)

            if not user_data or 'custom_id' not in user_data or 'username' not in user_data:
                logger.warning("存储中没有有效的用户数据")
                return False

            # 用户数据存在，尝试重新加载用户
            custom_id = user_data['custom_id']
            username = user_data['username']

            logger.info(f"从存储中找到用户数据: {username}, custom_id: {custom_id}")

            # 获取用户管理器
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()

            if not user_manager:
                logger.error("无法获取用户管理器")
                return False

            # 先尝试重新加载用户账户列表
            user_manager.load_accounts()

            # 尝试通过custom_id查找用户
            user = user_manager.get_user_by_custom_id(custom_id)

            # 如果找不到，尝试创建新用户
            if not user:
                logger.info(f"未找到用户 {username}，尝试创建新用户")
                # 从user_data中获取必要信息
                full_name = user_data.get('full_name', username)
                role = user_data.get('role', 'personal')

                # 创建新用户
                user = user_manager.add_account(
                    username=username,
                    password='',  # 空密码，因为我们使用token认证
                    full_name=full_name,
                    role=role,
                    custom_id=custom_id
                )

            # 如果有用户，切换到该用户
            if user:
                user_manager.current_user = user
                user_manager.save_current_user(force_save=True)
                self.set_current_user(user)

                # 确保API客户端配置正确
                if self.api_client:
                    self._configure_api_client(self.api_client, user_data)

                logger.info(f"成功设置当前用户: {username}")
                return True
            else:
                logger.error("无法创建或获取用户")
                return False

        except Exception as e:
            logger.error(f"重新加载用户认证信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def refresh_user_info(self):
        """刷新用户信息"""
        self.clear_cache()
        user_info = self.get_current_user_info()

        # 重新配置API客户端
        if self.api_client and user_info:
            self._configure_api_client(self.api_client, user_info)

        logger.info("用户信息已刷新")


# 全局认证管理器实例
_auth_manager = None

def get_auth_manager() -> AuthManager:
    """获取认证管理器实例（单例模式）

    Returns:
        AuthManager: 认证管理器实例
    """
    global _auth_manager
    if _auth_manager is None:
        _auth_manager = AuthManager()
    return _auth_manager