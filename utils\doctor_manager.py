"""医生管理模块
该模块提供医生信息的管理功能，包括添加、删除、查询医生等。
"""

import os
import json
from datetime import datetime

# 应用程序路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)

# 医生数据文件路径
DOCTOR_DATA_FILE = os.path.join(APP_DIR, "user_doctors.json")

# 医生科室类型（示例）
DEPARTMENT_TYPES = [
    "内科", "外科", "妇产科", "儿科", "眼科", "耳鼻喉科", 
    "口腔科", "皮肤科", "神经内科", "神经外科", "心脏科", 
    "呼吸科", "消化科", "泌尿科", "骨科", "中医科", "其他"
]

class Doctor:
    """医生类"""
    def __init__(self, doctor_id, name, department, hospital, phone, added_time=None):
        self.doctor_id = doctor_id    # 医生ID
        self.name = name              # 医生姓名
        self.department = department  # 科室
        self.hospital = hospital      # 医院
        self.phone = phone            # 联系电话
        self.added_time = added_time or datetime.now().isoformat()  # 添加时间
    
    def to_dict(self):
        """转换为字典"""
        return {
            "doctor_id": self.doctor_id,
            "name": self.name,
            "department": self.department,
            "hospital": self.hospital,
            "phone": self.phone,
            "added_time": self.added_time
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建医生对象"""
        return cls(
            doctor_id=data.get("doctor_id"),
            name=data.get("name"),
            department=data.get("department"),
            hospital=data.get("hospital"),
            phone=data.get("phone"),
            added_time=data.get("added_time")
        )

class DoctorManager:
    """医生管理器"""
    def __init__(self, user_id):
        self.user_id = user_id  # 当前用户ID
        self.doctors = []       # 医生列表
        self.load_doctors()     # 加载医生数据
    
    def load_doctors(self):
        """加载医生数据"""
        try:
            if os.path.exists(DOCTOR_DATA_FILE):
                with open(DOCTOR_DATA_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 获取当前用户的医生
                user_data = data.get(self.user_id, {})
                doctors_data = user_data.get("doctors", [])
                
                # 转换为医生对象
                self.doctors = [Doctor.from_dict(d) for d in doctors_data]
            else:
                # 如果文件不存在，创建一些示例数据以便测试
                self._create_sample_doctors()
        except Exception as e:
            print(f"加载医生数据失败: {e}")
            # 创建一些示例数据以便测试
            self._create_sample_doctors()
    
    def _create_sample_doctors(self):
        """创建示例医生数据（仅用于测试）"""
        sample_doctors = [
            {"name": "王医生", "department": "内科", "hospital": "北京协和医院", "phone": "13800138000"},
            {"name": "李医生", "department": "心脏科", "hospital": "上海瑞金医院", "phone": "13900139000"},
            {"name": "张医生", "department": "神经内科", "hospital": "广州南方医院", "phone": "13700137000"}
        ]
        
        self.doctors = []
        for i, doctor in enumerate(sample_doctors):
            doctor_id = f"DR{i+1:03d}"
            self.doctors.append(Doctor(
                doctor_id=doctor_id,
                name=doctor["name"],
                department=doctor["department"],
                hospital=doctor["hospital"],
                phone=doctor["phone"]
            ))
        
        # 保存示例数据
        self.save_doctors()
    
    def save_doctors(self):
        """保存医生数据"""
        try:
            # 读取现有数据
            all_data = {}
            if os.path.exists(DOCTOR_DATA_FILE):
                try:
                    with open(DOCTOR_DATA_FILE, 'r', encoding='utf-8') as f:
                        all_data = json.load(f)
                except:
                    all_data = {}
            
            # 更新当前用户的数据
            doctors_data = [d.to_dict() for d in self.doctors]
            all_data[self.user_id] = {"doctors": doctors_data}
            
            # 保存数据
            with open(DOCTOR_DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存医生数据失败: {e}")
            return False
    
    def add_doctor(self, name, department, hospital, phone):
        """添加医生"""
        # 生成医生ID
        doctor_id = f"DR{len(self.doctors) + 1:03d}"
        
        # 创建医生对象
        doctor = Doctor(
            doctor_id=doctor_id,
            name=name,
            department=department,
            hospital=hospital,
            phone=phone
        )
        
        # 添加到列表
        self.doctors.append(doctor)
        
        # 保存数据
        self.save_doctors()
        
        return doctor
    
    def remove_doctor(self, doctor_id):
        """删除医生"""
        for i, doctor in enumerate(self.doctors):
            if doctor.doctor_id == doctor_id:
                del self.doctors[i]
                self.save_doctors()
                return True
        
        return False
    
    def get_doctor(self, doctor_id):
        """获取指定医生"""
        for doctor in self.doctors:
            if doctor.doctor_id == doctor_id:
                return doctor
        
        return None
    
    def get_all_doctors(self):
        """获取所有医生"""
        return [doctor.to_dict() for doctor in self.doctors]
    
    def update_doctor(self, doctor_id, name=None, department=None, hospital=None, phone=None):
        """更新医生信息"""
        doctor = self.get_doctor(doctor_id)
        if not doctor:
            return False
        
        if name:
            doctor.name = name
        if department:
            doctor.department = department
        if hospital:
            doctor.hospital = hospital
        if phone:
            doctor.phone = phone
        
        self.save_doctors()
        return True

# 测试代码
if __name__ == "__main__":
    # 创建医生管理器
    manager = DoctorManager("user123")
    
    # 添加医生
    manager.add_doctor("赵医生", "骨科", "北京第三医院", "13600136000")
    
    # 获取所有医生
    doctors = manager.get_all_doctors()
    for doctor in doctors:
        print(f"医生ID: {doctor['doctor_id']}, 姓名: {doctor['name']}, 科室: {doctor['department']}, 医院: {doctor['hospital']}") 