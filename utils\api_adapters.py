"""
API适配器模块

此模块提供了将移动端数据格式转换为云端API期望格式的适配器函数。
"""

import logging
import time

logger = logging.getLogger(__name__)

def adapt_register_data(mobile_data):
    """
    将移动端注册数据适配为后端API期望的格式

    Args:
        mobile_data (dict): 移动端注册数据

    Returns:
        dict: 适配后的后端API数据
    """
    # 创建一个新字典，避免修改原始数据
    api_data = {}

    # 字段映射 - 统一使用后端字段名
    field_mapping = {
        "username": "username",
        "password": "password",
        "email": "email",
        "phone": "phone",
        "full_name": "full_name",  # 统一使用full_name
        "id_number": "id_number",
        "gender": "gender",
        "address": "address",
        "emergency_contact": "emergency_contact",
        "emergency_phone": "emergency_phone",
        "registration_type": "registration_type",
        "relationship": "relationship_type",
        "role": "role"  # 统一使用role
    }

    # 兼容旧字段名 - 将旧字段名映射到新字段名
    legacy_field_mapping = {
        "real_name": "full_name",  # 旧字段real_name映射到full_name
        "identity": "role"  # 旧字段identity映射到role
    }

    # 根据映射复制字段
    for mobile_field, api_field in field_mapping.items():
        if mobile_field in mobile_data and mobile_data[mobile_field]:
            api_data[api_field] = mobile_data[mobile_field]

    # 处理旧字段名映射
    for legacy_field, new_field in legacy_field_mapping.items():
        if legacy_field in mobile_data and mobile_data[legacy_field] and new_field not in api_data:
            api_data[new_field] = mobile_data[legacy_field]
            logger.info(f"从旧字段 {legacy_field} 映射到新字段 {new_field}: {mobile_data[legacy_field]}")

    # 特殊处理日期字段 - 转换为ISO格式 (YYYY-MM-DD)
    if "birth_date" in mobile_data and mobile_data["birth_date"]:
        try:
            # 尝试解析日期字符串
            from datetime import datetime

            # 处理不同的日期格式
            date_str = mobile_data["birth_date"]
            if "/" in date_str:  # 格式如 1975/03/08
                parts = date_str.split("/")
                if len(parts) == 3:
                    year, month, day = parts
                    # 确保月和日是两位数
                    month = month.zfill(2)
                    day = day.zfill(2)
                    api_data["birth_date"] = f"{year}-{month}-{day}"
            elif "-" in date_str:  # 格式如 1975-03-08
                # 已经是ISO格式，直接使用
                api_data["birth_date"] = date_str
            else:
                # 尝试使用datetime解析
                try:
                    date_obj = datetime.strptime(date_str, "%Y%m%d")
                    api_data["birth_date"] = date_obj.strftime("%Y-%m-%d")
                except ValueError:
                    # 如果解析失败，记录警告并使用原始值
                    logger.warning(f"无法解析日期: {date_str}，使用原始值")
                    api_data["birth_date"] = date_str

            logger.info(f"日期转换: {mobile_data['birth_date']} -> {api_data['birth_date']}")
        except Exception as e:
            logger.error(f"日期转换失败: {str(e)}")
            # 如果转换失败，使用原始值
            api_data["birth_date"] = mobile_data["birth_date"]

    # 处理角色信息 - 确保additional_roles是字符串列表
    if "additional_roles" in mobile_data:
        if isinstance(mobile_data["additional_roles"], list):
            # 确保列表中的所有元素都是字符串
            api_data["additional_roles"] = [str(role) for role in mobile_data["additional_roles"]]
        else:
            # 如果不是列表，尝试转换
            try:
                # 转换为字符串列表，不再使用整数
                api_data["additional_roles"] = [str(mobile_data["additional_roles"])]
            except (ValueError, TypeError):
                logger.warning("无法将additional_roles转换为列表")
                api_data["additional_roles"] = []

        # 记录转换后的角色列表
        logger.info(f"转换后的角色列表: {api_data['additional_roles']}")

    # 确保必填字段存在
    required_fields = ["username", "password", "email"]
    for field in required_fields:
        if field not in api_data or not api_data[field]:
            if field == "email" and "phone" in api_data:
                # 如果没有email但有phone，生成一个临时email
                api_data["email"] = f"{api_data['phone']}@example.com"
                logger.info(f"为用户 {api_data.get('username')} 生成临时邮箱：{api_data['email']}")
            else:
                logger.error(f"缺少必要字段: {field}")
                return None

    # 添加注册来源标记
    api_data["registration_source"] = "mobile"

    # 确保角色字段正确
    if "role" in mobile_data and mobile_data["role"]:
        api_data["role"] = mobile_data["role"]
        logger.info(f"设置主要角色: {api_data['role']}")
    else:
        # 如果没有设置角色，但有additional_roles，尝试从中选择最高权限的角色
        if "additional_roles" in api_data and api_data["additional_roles"]:
            # 角色优先级映射（数字越大优先级越高）
            role_priority = {
                "super_admin": 4,
                "admin": 3,
                "consultant": 2,
                "unit_admin": 2,
                "personal": 1
            }

            # 找出最高优先级的角色
            highest_role = "personal"
            highest_priority = 0

            for role in api_data["additional_roles"]:
                role_str = str(role).lower()
                priority = role_priority.get(role_str, 0)
                if priority > highest_priority:
                    highest_priority = priority
                    highest_role = role_str

            api_data["role"] = highest_role
            logger.info(f"从附加角色中选择主要角色: {api_data['role']}")
        else:
            # 默认为个人用户
            api_data["role"] = "personal"
            logger.info(f"使用默认角色: personal")

    # 记录适配后的数据（不包含密码）
    log_data = api_data.copy()
    if "password" in log_data:
        log_data["password"] = "******"  # 隐藏密码
    logger.info(f"已适配注册数据: {log_data}")

    return api_data

def adapt_login_data(mobile_data):
    """
    将移动端登录数据适配为云端API期望的格式

    Args:
        mobile_data (dict): 移动端登录数据

    Returns:
        dict: 适配后的云端API数据
    """
    # 创建登录数据 - 简化为只包含必要字段
    login_data = {
        "username": mobile_data.get("username"),
        "password": mobile_data.get("password"),
        # 移除不必要的字段，确保与后端API一致
    }

    # 记录适配后的数据（不包含密码）
    log_data = login_data.copy()
    if "password" in log_data:
        log_data["password"] = "******"  # 隐藏密码
    logger.debug(f"适配后的登录数据: {log_data}")

    return login_data

def adapt_user_data(backend_data):
    """
    将后端用户数据适配为移动端期望的格式，统一使用标准字段名

    Args:
        backend_data (dict): 后端返回的用户数据

    Returns:
        dict: 适配后的移动端用户数据
    """
    # 创建一个新字典，避免修改原始数据
    mobile_data = {}

    # 字段映射 - 后端字段名到移动端标准字段名的映射
    field_mapping = {
        "username": "username",
        "email": "email",
        "phone": "phone",
        "full_name": "full_name",
        "id_number": "id_number",
        "gender": "gender",
        "address": "address",
        "emergency_contact": "emergency_contact",
        "emergency_phone": "emergency_phone",
        "registration_type": "registration_type",
        "relationship_type": "relationship",
        "role": "role",
        "custom_id": "custom_id",  # 使用后端的custom_id作为用户ID
        "id": "id"  # 保留后端的主键ID
    }

    # 根据映射复制字段
    for backend_field, mobile_field in field_mapping.items():
        if backend_field in backend_data and backend_data[backend_field] is not None:
            mobile_data[mobile_field] = backend_data[backend_field]

    # 处理旧字段名映射
    if "real_name" in backend_data and backend_data["real_name"] and "full_name" not in mobile_data:
        mobile_data["full_name"] = backend_data["real_name"]
        logger.info(f"从旧字段real_name映射到full_name: {backend_data['real_name']}")

    if "identity" in backend_data and backend_data["identity"] and "role" not in mobile_data:
        mobile_data["role"] = backend_data["identity"]
        logger.info(f"从旧字段identity映射到role: {backend_data['identity']}")

    # 确保custom_id字段存在
    if "custom_id" not in mobile_data:
        # 不再生成临时ID
        logger.warning("后端未提供custom_id，这可能导致某些功能无法正常工作")

    # 不应该使用后端数据库主键id作为用户ID
    if "id" in mobile_data:
        logger.info(f"忽略后端数据库主键id: {mobile_data['id']}")

    # 记录适配后的数据
    logger.info(f"适配后的用户数据: {mobile_data}")

    return mobile_data

def adapt_health_record_data(mobile_data):
    """
    将移动端健康记录数据适配为云端API期望的格式

    Args:
        mobile_data (dict): 移动端健康记录数据

    Returns:
        dict: 适配后的云端API数据
    """
    # 创建一个新字典，避免修改原始数据
    api_data = {}

    # 用户ID - 使用custom_id如果有，否则使用user_id
    if "custom_id" in mobile_data:
        api_data["user_id"] = mobile_data.get("custom_id")
    elif "user_id" in mobile_data:
        api_data["user_id"] = mobile_data.get("user_id")

    # 记录类型
    api_data["record_type"] = mobile_data.get("record_type", "basic_info")

    # 记录值
    api_data["record_value"] = mobile_data.get("record_value", mobile_data.get("value", ""))

    # 记录单位
    if "record_unit" in mobile_data:
        api_data["record_unit"] = mobile_data.get("record_unit")
    elif "unit" in mobile_data:
        api_data["record_unit"] = mobile_data.get("unit")

    # 备注
    if "notes" in mobile_data:
        api_data["notes"] = mobile_data.get("notes")
    elif "description" in mobile_data:
        api_data["notes"] = mobile_data.get("description")

    # 记录日期 - 如果没有提供，使用当前时间
    if "record_date" in mobile_data:
        api_data["record_date"] = mobile_data.get("record_date")
    else:
        from datetime import datetime
        api_data["record_date"] = datetime.now().isoformat()

    # 添加其他可能的字段
    for field in ["height", "weight", "blood_type", "blood_pressure", "heart_rate", "temperature"]:
        if field in mobile_data:
            api_data[field] = mobile_data.get(field)

    # 记录日志
    logger.info(f"适配后的健康记录数据: {api_data}")

    return api_data

def adapt_document_upload_data(mobile_data, file):
    """
    将移动端文档上传数据适配为云端API期望的格式

    Args:
        mobile_data (dict): 移动端文档元数据
        file: 文件对象

    Returns:
        tuple: (form_data, files) 适配后的表单数据和文件
    """
    # 创建表单数据
    form_data = {}

    # 添加用户ID - 优先使用custom_id，并且确保使用正确的字段名
    if "custom_id" in mobile_data and mobile_data.get("custom_id"):
        # 使用custom_id字段，而不是user_id字段
        form_data["custom_id"] = mobile_data.get("custom_id")
        logger.info(f"使用custom_id: {mobile_data.get('custom_id')}")

        # 为了兼容性，也设置user_id字段，但使用custom_id的值
        # 这样后端可以根据custom_id找到正确的用户
        form_data["user_id"] = mobile_data.get("custom_id")
        logger.info(f"同时设置user_id为custom_id的值: {mobile_data.get('custom_id')}")
    elif "user_id" in mobile_data and mobile_data.get("user_id"):
        # 如果没有custom_id但有user_id，检查是否是临时ID
        user_id = mobile_data.get("user_id")
        if isinstance(user_id, str) and user_id.startswith("TEMP_"):
            logger.warning(f"检测到临时ID: {user_id}，这可能导致文档关联到错误的用户")

            # 如果有其他可用的用户标识，尝试使用它
            if hasattr(mobile_data, "username") and mobile_data.get("username"):
                logger.info(f"使用username作为备用标识: {mobile_data.get('username')}")
                form_data["username"] = mobile_data.get("username")

        # 仍然设置user_id以保持兼容性
        form_data["user_id"] = user_id
        logger.info(f"使用user_id: {user_id}")

    # 文档类型
    if "document_type" in mobile_data:
        form_data["document_type"] = mobile_data.get("document_type")

    # 描述
    if "description" in mobile_data:
        form_data["description"] = mobile_data.get("description")

    # 添加来源标记
    form_data["source"] = "mobile"

    # 添加时间戳
    from datetime import datetime
    form_data["created_at"] = datetime.now().isoformat()

    # 文件
    files = {"file": (file.name, file, file.content_type)}

    # 记录日志
    logger.info(f"适配后的文档上传数据: {form_data}")

    return form_data, files
