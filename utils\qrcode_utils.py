"""
QR码工具模块 - 提供二维码生成和扫描功能。
该模块可以为用户创建动态二维码，并允许其他用户通过扫描二维码添加为家庭成员。
还支持通过二维码查看个人健康状况。
"""

import os
import json
import time
import uuid
import threading
import base64
from io import BytesIO
from datetime import datetime, timedelta

# 第三方库依赖
import qrcode
from PIL import Image, ImageDraw, ImageFont
from pyzbar import pyzbar
import cv2
import numpy as np

# Kivy相关导入
from kivy.core.image import Image as CoreImage
from kivy.graphics.texture import Texture
from kivy.clock import Clock
from kivy.utils import platform

# 应用程序路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)
TEMP_DIR = os.path.join(APP_DIR, "temp")

# 确保临时目录存在
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)

# QR码配置
class QRConfig:
    # 二维码数据类型
    TYPE_USER_INFO = "user_info"        # 用户基本信息
    TYPE_HEALTH_DATA = "health_data"    # 健康数据
    TYPE_ADD_MEMBER = "add_member"      # 添加成员
    
    # 二维码有效期（秒）
    DEFAULT_EXPIRY = 120  # 2分钟
    
    # 二维码样式参数
    QR_VERSION = 3                  # 二维码版本，1-40
    QR_SIZE = 300                   # 二维码尺寸像素
    QR_BORDER = 4                   # 二维码边距
    QR_LOGO_SIZE_RATIO = 0.25       # logo占二维码大小的比例
    
    # 健康状态颜色
    HEALTH_COLOR_NORMAL = (32, 184, 64)  # 绿色，健康
    HEALTH_COLOR_WARNING = (255, 205, 0)  # 黄色，警告
    HEALTH_COLOR_DANGER = (230, 0, 0)     # 红色，危险
    
    # 应用标识符
    APP_PREFIX = "HEALTH_TREA:"

# 二维码数据加密与解密
class QRCodeCrypto:
    @staticmethod
    def encrypt_data(data):
        """简单的数据"加密"，仅用于演示"""
        # 在实际应用中应使用真正的加密算法
        # 这里仅将数据转为JSON并进行Base64编码
        json_data = json.dumps(data, ensure_ascii=False)
        return base64.b64encode(json_data.encode('utf-8')).decode('utf-8')
    
    @staticmethod
    def decrypt_data(encrypted_data):
        """解密数据"""
        try:
            # 解码Base64并解析JSON
            decoded = base64.b64decode(encrypted_data).decode('utf-8')
            return json.loads(decoded)
        except Exception as e:
            print(f"解密数据失败: {e}")
            return None

# 二维码生成器
class QRCodeGenerator:
    @staticmethod
    def create_qr_data(type, user_data, expiry=None):
        """创建二维码数据"""
        expiry_time = int(time.time() + (expiry or QRConfig.DEFAULT_EXPIRY))
        
        qr_data = {
            "type": type,
            "data": user_data,
            "expiry": expiry_time,
            "uuid": str(uuid.uuid4())
        }
        
        # 添加应用程序标识前缀，确保只有本应用能识别
        return f"{QRConfig.APP_PREFIX}{QRCodeCrypto.encrypt_data(qr_data)}"
    
    @staticmethod
    def create_user_info_qr(user_data, expiry=None, logo_path=None):
        """生成包含用户信息的二维码"""
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_USER_INFO, 
            user_data, 
            expiry
        )
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path)
    
    @staticmethod
    def create_health_data_qr(health_data, expiry=None, logo_path=None, health_status="normal"):
        """生成包含健康数据的二维码"""
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_HEALTH_DATA, 
            health_data, 
            expiry
        )
        
        # 根据健康状态选择颜色
        if health_status == "warning":
            color = QRConfig.HEALTH_COLOR_WARNING
        elif health_status == "danger":
            color = QRConfig.HEALTH_COLOR_DANGER
        else:
            color = QRConfig.HEALTH_COLOR_NORMAL
            
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path, color)
    
    @staticmethod
    def create_add_member_qr(user_id, user_name, expiry=None, logo_path=None):
        """生成添加成员的二维码"""
        member_data = {
            "user_id": user_id,
            "name": user_name,
            "timestamp": int(time.time())
        }
        
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_ADD_MEMBER, 
            member_data, 
            expiry
        )
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path)
    
    @staticmethod
    def generate_qr_image(data, logo_path=None, color=(0, 0, 0)):
        """生成QR码图像"""
        qr = qrcode.QRCode(
            version=QRConfig.QR_VERSION,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=10,
            border=QRConfig.QR_BORDER
        )
        
        qr.add_data(data)
        qr.make(fit=True)
        
        # 创建带颜色的QR码图像
        qr_img = qr.make_image(fill_color=color, back_color="white").convert('RGBA')
        
        # 如果提供了logo，则添加到QR码中心
        if logo_path and os.path.exists(logo_path):
            try:
                # 打开logo图像
                logo = Image.open(logo_path).convert('RGBA')
                
                # 调整logo大小
                qr_width, qr_height = qr_img.size
                logo_size = int(min(qr_width, qr_height) * QRConfig.QR_LOGO_SIZE_RATIO)
                logo = logo.resize((logo_size, logo_size), Image.LANCZOS)
                
                # 计算logo位置（居中）
                logo_pos = ((qr_width - logo_size) // 2, (qr_height - logo_size) // 2)
                
                # 创建一个新图像用于合成
                combined = Image.new('RGBA', qr_img.size, (255, 255, 255, 0))
                combined.paste(qr_img, (0, 0))
                combined.paste(logo, logo_pos, logo)
                
                return combined
            except Exception as e:
                print(f"添加Logo失败: {e}")
        
        return qr_img
    
    @staticmethod
    def get_qr_texture(qr_image):
        """将PIL图像转换为Kivy纹理"""
        # 保存图像到内存缓冲区
        buf = BytesIO()
        qr_image.save(buf, format='PNG')
        buf.seek(0)
        
        # 转换为Kivy纹理
        data = buf.read()
        buf.close()
        
        # 使用CoreImage创建纹理
        core_img = CoreImage(BytesIO(data), ext='png')
        texture = core_img.texture
        
        return texture
    
    @staticmethod
    def save_qr_image(qr_image, filename=None):
        """保存QR码图像到文件"""
        if filename is None:
            filename = f"qrcode_{int(time.time())}.png"
        
        # 确保目录存在
        file_path = os.path.join(TEMP_DIR, filename)
        qr_image.save(file_path)
        
        return file_path

# 二维码扫描器
class QRCodeScanner:
    def __init__(self, callback=None):
        """初始化扫描器"""
        self.camera = None
        self.callback = callback
        self.scanning = False
        self.last_scan_time = 0
        self.scan_interval = 0.5  # 扫描间隔（秒）
        self.scan_thread = None
    
    def start_scanning(self, camera_index=0):
        """开始扫描二维码"""
        if self.scanning:
            return False
        
        try:
            self.camera = cv2.VideoCapture(camera_index)
            if not self.camera or not self.camera.isOpened():
                print("无法打开摄像头")
                return False
            
            self.scanning = True
            self.scan_thread = threading.Thread(target=self._scan_loop)
            self.scan_thread.daemon = True
            self.scan_thread.start()
            return True
        except Exception as e:
            print(f"启动扫描失败: {e}")
            return False
    
    def stop_scanning(self):
        """停止扫描"""
        self.scanning = False
        if self.scan_thread:
            self.scan_thread.join(timeout=1.0)
        
        if self.camera and hasattr(self.camera, 'release'):
            self.camera.release()
            self.camera = None
    
    def _scan_loop(self):
        """扫描循环"""
        while self.scanning:
            # 检查扫描间隔
            current_time = time.time()
            if current_time - self.last_scan_time < self.scan_interval:
                time.sleep(0.1)
                continue
            
            if self.camera and hasattr(self.camera, 'read'):
                ret, frame = self.camera.read()
                if not ret:
                    continue
                
                # 尝试解码图像中的二维码
                qr_data = self._decode_frame(frame)
                if qr_data:
                    self.last_scan_time = current_time
                    
                    # 使用回调函数返回结果，创建一个本地函数确保类型安全
                    callback_func = self.callback
                    if callback_func is not None:
                        def schedule_callback(dt, data=qr_data):
                            if callback_func is not None:
                                callback_func(data)
                        Clock.schedule_once(schedule_callback, 0)
    
    def _decode_frame(self, frame):
        """解码帧中的二维码"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 使用pyzbar解码二维码
        decoded_objects = pyzbar.decode(gray)
        for obj in decoded_objects:
            # 获取二维码数据
            qr_text = obj.data.decode('utf-8')
            
            # 验证是否是我们的应用二维码
            if qr_text.startswith(QRConfig.APP_PREFIX):
                # 提取实际数据
                encrypted_data = qr_text[len(QRConfig.APP_PREFIX):]
                data = QRCodeCrypto.decrypt_data(encrypted_data)
                
                if data:
                    # 检查有效期
                    if data.get("expiry", 0) > time.time():
                        return data
                    else:
                        print("二维码已过期")
        
        return None
    
    @staticmethod
    def scan_from_image(image_path):
        """从图像文件扫描二维码"""
        try:
            # 读取图像
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
            else:
                # 假设是Pillow图像
                image = cv2.cvtColor(np.array(image_path), cv2.COLOR_RGB2BGR)
            
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 解码二维码
            decoded_objects = pyzbar.decode(gray)
            for obj in decoded_objects:
                qr_text = obj.data.decode('utf-8')
                
                # 验证是否是我们的应用二维码
                if qr_text.startswith(QRConfig.APP_PREFIX):
                    # 提取实际数据
                    encrypted_data = qr_text[len(QRConfig.APP_PREFIX):]
                    data = QRCodeCrypto.decrypt_data(encrypted_data)
                    
                    if data:
                        # 检查有效期
                        if data.get("expiry", 0) > time.time():
                            return data
                        else:
                            return {"error": "二维码已过期"}
            
            return {"error": "未找到有效的二维码"}
        except Exception as e:
            print(f"扫描图像失败: {e}")
            return {"error": f"扫描图像失败: {str(e)}"}

# 动态二维码管理
class DynamicQRCode:
    def __init__(self, user_id, update_interval=30):
        """初始化动态二维码管理器"""
        self.user_id = user_id
        self.update_interval = update_interval  # 更新间隔（秒）
        self.qr_image = None
        self.qr_texture = None
        self.update_event = None
        self.user_data = None
        self.health_data = None
        self.logo_path = None
    
    def start(self, user_data, health_data=None, logo_path=None, callback=None):
        """开始动态二维码更新"""
        self.user_data = user_data
        self.health_data = health_data
        self.logo_path = logo_path
        self.callback = callback
        
        # 初始生成二维码
        self._update_qr()
        
        # 设置定时更新
        self.update_event = Clock.schedule_interval(
            lambda dt: self._update_qr(), 
            self.update_interval
        )
    
    def stop(self):
        """停止动态二维码更新"""
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None
    
    def _update_qr(self):
        """更新二维码"""
        if self.health_data:
            # 确定健康状态
            health_status = self._calculate_health_status(self.health_data)
            
            # 生成带健康状态的二维码
            self.qr_image = QRCodeGenerator.create_health_data_qr(
                self.health_data,
                expiry=self.update_interval + 10,  # 有效期略长于更新间隔
                logo_path=self.logo_path,
                health_status=health_status
            )
        else:
            # 生成用户信息二维码
            self.qr_image = QRCodeGenerator.create_user_info_qr(
                self.user_data,
                expiry=self.update_interval + 10,
                logo_path=self.logo_path
            )
        
        # 更新纹理
        self.qr_texture = QRCodeGenerator.get_qr_texture(self.qr_image)
        
        # 调用回调函数更新UI
        if self.callback:
            self.callback(self.qr_texture)
    
    def _calculate_health_status(self, health_data):
        """根据健康数据计算健康状态"""
        # 这里是简化的健康状态计算，实际应用应根据医学标准判断
        danger_count = 0
        warning_count = 0
        
        # 检查血压
        if 'blood_pressure' in health_data:
            systolic = health_data['blood_pressure'].get('systolic', 120)
            diastolic = health_data['blood_pressure'].get('diastolic', 80)
            
            if systolic > 180 or diastolic > 110:
                danger_count += 1
            elif systolic > 140 or diastolic > 90:
                warning_count += 1
        
        # 检查血糖
        if 'blood_glucose' in health_data:
            glucose = health_data['blood_glucose'].get('value', 5.5)
            
            if glucose > 11.1:
                danger_count += 1
            elif glucose > 7.0:
                warning_count += 1
        
        # 根据危险和警告计数确定状态
        if danger_count > 0:
            return "danger"
        elif warning_count > 0:
            return "warning"
        else:
            return "normal"
    
    def get_current_texture(self):
        """获取当前二维码纹理"""
        if not self.qr_texture:
            self._update_qr()
        return self.qr_texture
    
    def save_current_image(self, filename=None):
        """保存当前二维码图像"""
        if not self.qr_image:
            self._update_qr()
        
        return QRCodeGenerator.save_qr_image(self.qr_image, filename)

# 便捷函数
def generate_user_qrcode(user_data, logo_path=None):
    """生成用户信息二维码"""
    return QRCodeGenerator.create_user_info_qr(user_data, logo_path=logo_path)

def generate_health_qrcode(user_data, health_data, logo_path=None):
    """生成健康信息二维码"""
    return QRCodeGenerator.create_health_data_qr(health_data, logo_path=logo_path)

def generate_member_invitation_qrcode(user_id, user_name, logo_path=None):
    """生成家庭成员邀请二维码"""
    return QRCodeGenerator.create_add_member_qr(user_id, user_name, logo_path=logo_path)

def scan_qrcode_from_image(image_path):
    """从图像文件扫描二维码"""
    return QRCodeScanner.scan_from_image(image_path) 