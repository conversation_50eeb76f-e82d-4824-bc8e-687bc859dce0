"""
日志配置模块

提供应用程序的集中式日志管理功能，包括：
- 文件和控制台日志
- 日志级别管理
- 性能监控
- 用户行为跟踪
- 错误报告
"""

import os
import sys
import json
import time
import uuid
import platform
import logging
import logging.handlers
from datetime import datetime
from functools import wraps

# 默认配置
DEFAULT_CONFIG = {
    'app_name': 'health_trea',
    'log_dir': 'logs',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 10,
    'console_level': 'INFO',
    'file_level': 'DEBUG',
    'error_level': 'ERROR',
    'user_level': 'INFO',
    'performance_level': 'DEBUG',
    'rotation_when': 'midnight',
    'enable_user_tracking': True,
    'enable_performance_tracking': True,
    'enable_error_reporting': True,
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'console_format': '%(levelname)s: %(message)s',
}

# 日志类型
LOG_TYPES = {
    'app': 'app',           # 应用程序常规日志
    'error': 'error',       # 错误日志
    'user': 'user',         # 用户行为日志
    'performance': 'perf',  # 性能日志
    'http': 'http',         # HTTP请求日志
}

# 用于存储当前会话ID
_session_id = str(uuid.uuid4())

# 日志记录器缓存
_loggers = {}

def get_logger(name='app', log_type=None):
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        log_type: 日志类型，如None则使用默认类型
        
    Returns:
        配置好的Logger对象
    """
    if log_type is None:
        log_type = LOG_TYPES.get(name, 'app')
    
    # 生成logger的唯一标识符
    logger_id = f"{name}.{log_type}"
    
    # 如果已存在则直接返回
    if logger_id in _loggers:
        return _loggers[logger_id]
    
    # 创建新的logger
    logger = logging.getLogger(name)
    
    # 避免重复配置
    if logger.handlers:
        _loggers[logger_id] = logger
        return logger
    
    # 设置日志级别
    logger.setLevel(logging.DEBUG)  # 设置为最低，让handler控制级别
    
    # 确保日志目录存在
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), DEFAULT_CONFIG['log_dir'])
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except Exception as e:
            print(f"无法创建日志目录: {e}")
            log_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 创建文件处理器
    try:
        log_file = os.path.join(log_dir, f"{DEFAULT_CONFIG['app_name']}_{log_type}.log")
        file_handler = logging.handlers.TimedRotatingFileHandler(
            log_file,
            when=DEFAULT_CONFIG['rotation_when'],
            backupCount=DEFAULT_CONFIG['backup_count'],
            encoding='utf-8'
        )
        
        # 根据日志类型设置级别
        if log_type == LOG_TYPES['error']:
            level = getattr(logging, DEFAULT_CONFIG['error_level'])
        elif log_type == LOG_TYPES['user']:
            level = getattr(logging, DEFAULT_CONFIG['user_level'])
        elif log_type == LOG_TYPES['performance']:
            level = getattr(logging, DEFAULT_CONFIG['performance_level'])
        else:
            level = getattr(logging, DEFAULT_CONFIG['file_level'])
            
        file_handler.setLevel(level)
        
        # 设置格式
        formatter = logging.Formatter(DEFAULT_CONFIG['log_format'])
        file_handler.setFormatter(formatter)
        
        # 添加到logger
        logger.addHandler(file_handler)
        
        # 为错误日志额外添加错误文件处理器
        if log_type != LOG_TYPES['error'] and DEFAULT_CONFIG['enable_error_reporting']:
            error_file = os.path.join(log_dir, f"{DEFAULT_CONFIG['app_name']}_error.log")
            error_handler = logging.handlers.TimedRotatingFileHandler(
                error_file,
                when=DEFAULT_CONFIG['rotation_when'],
                backupCount=DEFAULT_CONFIG['backup_count'],
                encoding='utf-8'
            )
            error_handler.setLevel(getattr(logging, DEFAULT_CONFIG['error_level']))
            error_handler.setFormatter(formatter)
            # 设置过滤器，只处理ERROR及以上级别
            error_handler.addFilter(lambda record: record.levelno >= logging.ERROR)
            logger.addHandler(error_handler)
    
    except Exception as e:
        print(f"配置文件日志处理器失败: {e}")
    
    # 添加控制台处理器
    try:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, DEFAULT_CONFIG['console_level']))
        console_formatter = logging.Formatter(DEFAULT_CONFIG['console_format'])
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    except Exception as e:
        print(f"配置控制台日志处理器失败: {e}")
    
    # 保存并返回logger
    _loggers[logger_id] = logger
    return logger

def get_app_logger():
    """获取应用程序主日志记录器"""
    return get_logger('app', LOG_TYPES['app'])

def get_error_logger():
    """获取错误日志记录器"""
    return get_logger('error', LOG_TYPES['error'])

def get_user_logger():
    """获取用户行为日志记录器"""
    return get_logger('user', LOG_TYPES['user'])

def get_performance_logger():
    """获取性能日志记录器"""
    return get_logger('performance', LOG_TYPES['performance'])

def get_http_logger():
    """获取HTTP请求日志记录器"""
    return get_logger('http', LOG_TYPES['http'])

def log_user_action(user_id, action, details=None, status="success"):
    """
    记录用户行为
    
    Args:
        user_id: 用户ID或标识符
        action: 用户执行的操作
        details: 操作的详细信息
        status: 操作状态
    """
    if not DEFAULT_CONFIG['enable_user_tracking']:
        return
        
    logger = get_user_logger()
    
    log_data = {
        'timestamp': datetime.now().isoformat(),
        'session_id': _session_id,
        'user_id': user_id,
        'action': action,
        'details': details,
        'status': status,
    }
    
    logger.info(json.dumps(log_data, ensure_ascii=False))

def log_performance(operation, execution_time, details=None):
    """
    记录性能指标
    
    Args:
        operation: 操作名称
        execution_time: 执行时间(秒)
        details: 额外详情
    """
    if not DEFAULT_CONFIG['enable_performance_tracking']:
        return
        
    logger = get_performance_logger()
    
    log_data = {
        'timestamp': datetime.now().isoformat(),
        'session_id': _session_id,
        'operation': operation,
        'execution_time': execution_time,
        'details': details,
    }
    
    logger.debug(json.dumps(log_data, ensure_ascii=False))

def measure_performance(func=None, operation_name=None):
    """
    性能测量装饰器
    
    Args:
        func: 要装饰的函数
        operation_name: 操作名称，如果未提供则使用函数名
    """
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            if not DEFAULT_CONFIG['enable_performance_tracking']:
                return f(*args, **kwargs)
                
            # 确定操作名称
            op_name = operation_name or f.__qualname__
            
            # 测量执行时间
            start_time = time.time()
            try:
                result = f(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 记录性能
                log_performance(op_name, execution_time)
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                
                # 记录性能（失败）
                log_performance(op_name, execution_time, {'error': str(e)})
                
                # 重新抛出异常
                raise
        return wrapper
        
    if func:
        return decorator(func)
    return decorator

def log_system_info():
    """记录系统信息，通常在应用启动时调用"""
    logger = get_app_logger()
    
    try:
        system_info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'processor': platform.processor(),
            'machine': platform.machine(),
            'node': platform.node(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
        }
        
        logger.info(f"系统信息: {json.dumps(system_info, ensure_ascii=False)}")
    except Exception as e:
        logger.error(f"获取系统信息时发生错误: {str(e)}")

def log_exception(exc_info=True, level=logging.ERROR, logger_name='app'):
    """
    记录异常信息
    
    Args:
        exc_info: 是否包含异常信息
        level: 日志级别
        logger_name: 日志记录器名称
    """
    logger = get_logger(logger_name)
    logger.log(level, "发生异常", exc_info=exc_info)

def setup_logging(config=None):
    """
    设置日志系统
    
    Args:
        config: 自定义配置，如None则使用默认配置
    """
    global DEFAULT_CONFIG
    
    # 更新配置
    if config:
        DEFAULT_CONFIG.update(config)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 添加控制台处理器
    if not root_logger.handlers:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, DEFAULT_CONFIG['console_level']))
        formatter = logging.Formatter(DEFAULT_CONFIG['console_format'])
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 记录系统信息
    log_system_info()
    
    # 记录启动信息
    logger = get_app_logger()
    logger.info(f"应用程序日志系统已初始化 [会话ID: {_session_id}]")
    
    return logger

# 默认导出的日志记录器
logger = get_app_logger() 