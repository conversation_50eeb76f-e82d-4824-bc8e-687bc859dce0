#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手机号登录UI修改
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_phone_login_ui_changes():
    """测试手机号登录UI修改"""
    print("=== 测试手机号登录UI修改 ===")
    
    try:
        # 读取修改后的登录屏幕代码
        with open('screens/login_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查1: 确认删除了"请输入11位手机号"提示
        helper_text_removed = 'text: "请输入11位手机号"' not in content
        print(f"✅ 删除'请输入11位手机号'提示: {'成功' if helper_text_removed else '失败'}")
        
        # 检查2: 确认删除了"请输入6位验证码"提示
        verification_helper_removed = 'text: "请输入6位验证码"' not in content
        print(f"✅ 删除'请输入6位验证码'提示: {'成功' if verification_helper_removed else '失败'}")
        
        # 检查3: 确认获取验证码按钮比例缩短了
        button_ratio_changed = 'size_hint_x: 0.3  # 缩短按钮比例' in content
        print(f"✅ 缩短获取验证码按钮: {'成功' if button_ratio_changed else '失败'}")
        
        # 检查4: 确认验证码输入框比例增加了
        input_ratio_changed = 'size_hint_x: 0.7  # 增加输入框比例，给按钮更少空间' in content
        print(f"✅ 增加验证码输入框比例: {'成功' if input_ratio_changed else '失败'}")
        
        # 检查5: 确认添加了图标颜色主题
        icon_theme_added = 'theme_icon_color: "Custom"' in content and 'icon_color: app.theme.PRIMARY_COLOR' in content
        print(f"✅ 添加图标主题颜色: {'成功' if icon_theme_added else '失败'}")
        
        # 检查6: 确认保留了必要的提示文本
        phone_hint_kept = 'text: "请输入手机号"' in content
        verification_hint_kept = 'text: "请输入验证码"' in content
        print(f"✅ 保留手机号提示: {'成功' if phone_hint_kept else '失败'}")
        print(f"✅ 保留验证码提示: {'成功' if verification_hint_kept else '失败'}")
        
        # 检查7: 确认按钮文本字体大小调整
        button_font_adjusted = 'font_size: dp(13)  # 稍微减小字体以适应更窄的按钮' in content
        print(f"✅ 调整按钮字体大小: {'成功' if button_font_adjusted else '失败'}")
        
        # 统计结果
        checks = [
            helper_text_removed,
            verification_helper_removed, 
            button_ratio_changed,
            input_ratio_changed,
            icon_theme_added,
            phone_hint_kept,
            verification_hint_kept,
            button_font_adjusted
        ]
        
        passed = sum(checks)
        total = len(checks)
        
        print(f"\n总体结果: {passed}/{total} 项检查通过")
        
        if passed == total:
            print("🎉 所有UI修改都已成功应用!")
        else:
            print("⚠️ 部分修改可能需要进一步调整")
            
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_layout_improvements():
    """测试UI布局改进"""
    print("\n=== 测试UI布局改进 ===")
    
    try:
        # 读取修改后的登录屏幕代码
        with open('screens/login_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局改进
        improvements = {
            "验证码输入框比例": "size_hint_x: 0.7",
            "获取验证码按钮比例": "size_hint_x: 0.3",
            "按钮内边距优化": "padding: [dp(2), 0, dp(2), 0]",
            "图标主题颜色": "theme_icon_color: \"Custom\"",
            "输入框高度统一": "height: dp(56)",
            "字体大小统一": "font_size: dp(16)"
        }
        
        for improvement, check_text in improvements.items():
            found = check_text in content
            print(f"✅ {improvement}: {'已应用' if found else '未找到'}")
        
        # 检查是否移除了不必要的helper text
        removed_texts = [
            "请输入11位手机号",
            "请输入6位验证码"
        ]
        
        for text in removed_texts:
            not_found = f'text: "{text}"' not in content
            print(f"✅ 移除'{text}': {'成功' if not_found else '仍存在'}")
        
        print("\n布局优化总结:")
        print("- 获取验证码按钮从35%缩短到30%")
        print("- 验证码输入框从65%增加到70%") 
        print("- 移除了冗余的helper text提示")
        print("- 统一了图标颜色主题")
        print("- 优化了按钮内边距和字体大小")
        
        return True
        
    except Exception as e:
        print(f"❌ 布局测试过程中出错: {e}")
        return False

def show_ui_comparison():
    """显示UI修改前后对比"""
    print("\n=== UI修改前后对比 ===")
    
    print("修改前:")
    print("- 手机号输入框: 有'请输入11位手机号'提示")
    print("- 验证码输入框: 有'请输入6位验证码'提示")
    print("- 获取验证码按钮: 占35%宽度，较长")
    print("- 验证码输入框: 占65%宽度")
    
    print("\n修改后:")
    print("- 手机号输入框: 只保留'请输入手机号'提示")
    print("- 验证码输入框: 只保留'请输入验证码'提示")
    print("- 获取验证码按钮: 占30%宽度，更紧凑")
    print("- 验证码输入框: 占70%宽度，更宽敞")
    print("- 图标颜色: 统一使用主题色")
    print("- 按钮字体: 适应更窄的按钮宽度")
    
    print("\n预期效果:")
    print("✨ 界面更简洁，减少视觉干扰")
    print("✨ 获取验证码按钮更紧凑，不会显得过长")
    print("✨ 验证码输入区域更宽敞，输入体验更好")
    print("✨ 整体布局更协调，符合移动端设计规范")

if __name__ == "__main__":
    print("开始测试手机号登录UI修改...")
    
    # 运行测试
    ui_test_passed = test_phone_login_ui_changes()
    layout_test_passed = test_ui_layout_improvements()
    
    # 显示对比
    show_ui_comparison()
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结:")
    print(f"UI修改测试: {'✅ 通过' if ui_test_passed else '❌ 失败'}")
    print(f"布局改进测试: {'✅ 通过' if layout_test_passed else '❌ 失败'}")
    
    if ui_test_passed and layout_test_passed:
        print("\n🎉 所有测试通过! 手机号登录UI已成功优化")
        print("建议运行应用查看实际效果")
    else:
        print("\n⚠️ 部分测试未通过，请检查修改内容")
    
    print("测试完成!")
