import json
import logging
import os
from datetime import datetime
from .database import get_db_manager
from .db_models import DBInitializer

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('db_service')

class DBService:
    """数据库服务类，提供对数据库管理器的高级封装"""
    
    def __init__(self):
        """初始化数据库服务"""
        self.db_manager = get_db_manager()
        self.current_user_id = None
    
    def connect_user(self, user_id):
        """连接到指定用户的数据库"""
        if not user_id:
            logger.error("用户ID不能为空")
            return False
        
        result = self.db_manager.connect(user_id)
        if result:
            self.current_user_id = user_id
            # 确保基础表结构存在
            self._ensure_base_tables()
        
        return result
    
    def disconnect(self):
        """断开当前数据库连接"""
        self.db_manager.disconnect()
        self.current_user_id = None
    
    def _ensure_base_tables(self):
        """确保基础表结构存在"""
        # 用户信息表
        self.create_table_if_not_exists(
            "user_info",
            [
                "id INTEGER PRIMARY KEY AUTOINCREMENT",
                "user_id TEXT NOT NULL UNIQUE",
                "username TEXT",
                "phone TEXT",
                "email TEXT",
                "avatar TEXT",
                "created_at TEXT NOT NULL",
                "updated_at TEXT NOT NULL"
            ]
        )
        
        # 健康基本信息表
        self.create_table_if_not_exists(
            "health_basic_info",
            [
                "id INTEGER PRIMARY KEY AUTOINCREMENT",
                "user_id TEXT NOT NULL",
                "height REAL",
                "weight REAL",
                "blood_type TEXT",
                "allergies TEXT",
                "chronic_diseases TEXT",
                "created_at TEXT NOT NULL",
                "updated_at TEXT NOT NULL",
                "UNIQUE(user_id)"
            ]
        )
        
        # 同步状态表
        self.create_table_if_not_exists(
            "sync_status",
            [
                "id INTEGER PRIMARY KEY AUTOINCREMENT",
                "table_name TEXT NOT NULL",
                "record_id INTEGER NOT NULL",
                "sync_status TEXT NOT NULL",
                "last_sync_time TEXT",
                "error_message TEXT",
                "created_at TEXT NOT NULL",
                "updated_at TEXT NOT NULL",
                "UNIQUE(table_name, record_id)"
            ]
        )
        
        # 确保必要的索引存在
        self.db_manager.create_index("health_basic_info", ["user_id"])
        self.db_manager.create_index("sync_status", ["table_name", "sync_status"])
    
    def create_table_if_not_exists(self, table_name, columns):
        """如果表不存在，创建表"""
        return self.db_manager.create_table(table_name, columns, self.current_user_id)
    
    def table_exists(self, table_name):
        """检查表是否存在"""
        return self.db_manager.table_exists(table_name, self.current_user_id)
    
    def insert(self, table_name, data):
        """向表中插入数据
        
        Args:
            table_name: 表名
            data: 字典形式的数据
            
        Returns:
            int: 插入的记录ID
        """
        if not data:
            logger.error("插入数据不能为空")
            return None
        
        # 添加创建和更新时间
        now = datetime.now().isoformat()
        if 'created_at' not in data:
            data['created_at'] = now
        if 'updated_at' not in data:
            data['updated_at'] = now
        
        # 构建插入语句
        columns = list(data.keys())
        placeholders = ['?' for _ in columns]
        values = [data[col] for col in columns]
        
        query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
        
        return self.db_manager.execute_insert(query, values, self.current_user_id)
    
    def update(self, table_name, data, condition):
        """更新表中的数据
        
        Args:
            table_name: 表名
            data: 字典形式的更新数据
            condition: 字典形式的条件
            
        Returns:
            int: 更新的记录数
        """
        if not data:
            logger.error("更新数据不能为空")
            return 0
        
        # 添加更新时间
        now = datetime.now().isoformat()
        if 'updated_at' not in data:
            data['updated_at'] = now
        
        # 构建更新语句
        set_clause = []
        values = []
        
        for key, value in data.items():
            set_clause.append(f"{key} = ?")
            values.append(value)
        
        # 构建条件语句
        where_clause = []
        for key, value in condition.items():
            where_clause.append(f"{key} = ?")
            values.append(value)
        
        query = f"UPDATE {table_name} SET {', '.join(set_clause)} WHERE {' AND '.join(where_clause)}"
        
        return self.db_manager.execute_update(query, values, self.current_user_id)
    
    def delete(self, table_name, condition):
        """从表中删除数据
        
        Args:
            table_name: 表名
            condition: 字典形式的条件
            
        Returns:
            int: 删除的记录数
        """
        # 构建条件语句
        where_clause = []
        values = []
        
        for key, value in condition.items():
            where_clause.append(f"{key} = ?")
            values.append(value)
        
        query = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clause)}"
        
        return self.db_manager.execute_update(query, values, self.current_user_id)
    
    def select(self, table_name, columns="*", condition=None, order_by=None, limit=None):
        """从表中查询数据
        
        Args:
            table_name: 表名
            columns: 查询的列，默认为所有列
            condition: 字典形式的条件
            order_by: 排序子句
            limit: 限制返回的记录数
            
        Returns:
            list: 查询结果
        """
        # 构建查询语句
        if isinstance(columns, list):
            columns = ", ".join(columns)
        
        query = f"SELECT {columns} FROM {table_name}"
        
        values = []
        
        # 添加条件
        if condition:
            where_clause = []
            for key, value in condition.items():
                where_clause.append(f"{key} = ?")
                values.append(value)
            
            query += f" WHERE {' AND '.join(where_clause)}"
        
        # 添加排序
        if order_by:
            query += f" ORDER BY {order_by}"
        
        # 添加限制
        if limit:
            query += f" LIMIT {limit}"
        
        return self.db_manager.execute_query(query, values, self.current_user_id)
    
    def execute_custom_query(self, query, params=()):
        """执行自定义查询"""
        return self.db_manager.execute_query(query, params, self.current_user_id)
    
    def execute_custom_update(self, query, params=()):
        """执行自定义更新"""
        return self.db_manager.execute_update(query, params, self.current_user_id)
    
    def backup_database(self, backup_dir=None):
        """备份当前用户的数据库"""
        if not self.current_user_id:
            logger.error("未连接到用户数据库，无法备份")
            return False
        
        return self.db_manager.backup_database(self.current_user_id, backup_dir)
    
    def get_database_path(self):
        """获取当前用户数据库的路径"""
        if not self.current_user_id:
            return None
        
        return self.db_manager.get_db_path(self.current_user_id)

# 单例模式
_db_service_instance = None

def get_db_service():
    """获取数据库服务实例（单例模式）"""
    global _db_service_instance
    if _db_service_instance is None:
        _db_service_instance = DBService()
    return _db_service_instance 