import os
from kivy.app import App
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.clock import Clock
from theme import AppTheme, AppMetrics
from utils.storage import get_storage
from datetime import datetime
from kivy.factory import Factory
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件

# 定义表格组件类
class TableHeaderLabel(Label):
    """表格标题标签"""
    pass

class TableCellLabel(Label):
    """表格单元格标签"""
    pass

class TableRowLayout(BoxLayout):
    """表格行布局"""
    pass

class TableSection(BoxLayout):
    """表格区块"""
    pass

# 设计健康状况一览表UI
Builder.load_string("""
<TableHeaderLabel>:
    size_hint_y: None
    height: dp(45)
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    color: app.theme.TEXT_LIGHT
    bold: True
    text_size: self.size
    halign: 'center'
    valign: 'middle'
    font_size: dp(18)

<TableCellLabel>:
    size_hint_y: None
    height: dp(40)
    canvas.before:
        Color:
            rgba: app.theme.CARD_BACKGROUND
        Rectangle:
            pos: self.pos
            size: self.size
    color: app.theme.TEXT_PRIMARY
    text_size: self.size
    halign: 'left'
    valign: 'middle'
    padding: [dp(15), 0, dp(15), 0]
    font_size: dp(14)

<TableRowLayout>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(35)
    canvas.before:
        Color:
            rgba: 0.95, 0.95, 0.95, 1
        Rectangle:
            pos: self.pos
            size: self.size

<TableSection>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    canvas.before:
        Color:
            rgba: app.theme.CARD_BACKGROUND
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [dp(app.metrics.CORNER_RADIUS)]
    padding: dp(15)
    spacing: dp(4)
    margin: dp(5)

<GeneralHealthScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size

    BoxLayout:
        orientation: 'vertical'
        padding: dp(app.metrics.PADDING_LARGE)
        spacing: dp(app.metrics.PADDING_LARGE)

        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [dp(app.metrics.CORNER_RADIUS)]

            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo

        # 健康状况一览表标签
        BoxLayout:
            size_hint_y: None
            height: dp(40)
            padding: [dp(10), dp(5), dp(10), dp(5)]

            Label:
                text: "健康状况一览表"
                font_size: dp(20)
                color: app.theme.TEXT_PRIMARY
                bold: True
                halign: 'left'
                text_size: self.size

        # 健康状况一览表内容
        ScrollView:
            do_scroll_x: False
            do_scroll_y: True

            BoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                spacing: dp(5)
                padding: [0, dp(5), 0, dp(5)]

                # 基本信息部分
                TableSection:
                    id: basic_info_section

                    TableHeaderLabel:
                        text: "基本信息"

                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(2)
                        padding: [dp(0), dp(2), dp(0), dp(2)]

                        # 第一行：姓名和性别
                        GridLayout:
                            cols: 4
                            size_hint_y: None
                            height: dp(40)
                            spacing: [dp(0), dp(2)]

                            # 姓名标签
                            TableCellLabel:
                                text: "姓名:"
                                size_hint_x: 0.25

                            # 姓名值
                            TableCellLabel:
                                id: name_value
                                text: ""
                                size_hint_x: 0.25

                            # 性别标签
                            TableCellLabel:
                                text: "性别:"
                                size_hint_x: 0.25

                            # 性别值
                            TableCellLabel:
                                id: gender_value
                                text: ""
                                size_hint_x: 0.25

                        # 第二行：出生日期和民族
                        GridLayout:
                            cols: 4
                            size_hint_y: None
                            height: dp(40)
                            spacing: [dp(0), dp(2)]

                            # 出生日期标签
                            TableCellLabel:
                                text: "出生日期:"
                                size_hint_x: 0.4

                            # 出生日期值
                            TableCellLabel:
                                id: birth_date_value
                                text: ""
                                size_hint_x: 0.1

                            # 民族标签
                            TableCellLabel:
                                text: "民族:"
                                size_hint_x: 0.25

                            # 民族值
                            TableCellLabel:
                                id: ethnic_value
                                text: ""
                                size_hint_x: 0.25

                        # 第三行：教育程度和联系电话
                        GridLayout:
                            cols: 4
                            size_hint_y: None
                            height: dp(40)
                            spacing: [dp(0), dp(2)]

                            # 教育程度标签
                            TableCellLabel:
                                text: "教育程度:"
                                size_hint_x: 0.4

                            # 教育程度值
                            TableCellLabel:
                                id: education_value
                                text: ""
                                size_hint_x: 0.1

                            # 联系电话标签
                            TableCellLabel:
                                text: "联系电话:"
                                size_hint_x: 0.4

                            # 联系电话值
                            TableCellLabel:
                                id: phone_value
                                text: ""
                                size_hint_x: 0.1

                        # 第四行：紧急联系人和联系电话
                        GridLayout:
                            cols: 4
                            size_hint_y: None
                            height: dp(40)
                            spacing: [dp(0), dp(2)]

                            # 紧急联系人标签
                            TableCellLabel:
                                text: "紧急联系人:"
                                size_hint_x: 0.45

                            # 紧急联系人值
                            TableCellLabel:
                                id: emergency_contact_value
                                text: ""
                                size_hint_x: 0.05

                            # 紧急联系电话标签
                            TableCellLabel:
                                text: "紧急电话:"
                                size_hint_x: 0.4

                            # 紧急联系电话值
                            TableCellLabel:
                                id: emergency_phone_value
                                text: ""
                                size_hint_x: 0.1

                        # 第五行：家庭住址（单独一行）
                        GridLayout:
                            cols: 2
                            size_hint_y: None
                            height: dp(40)
                            spacing: [dp(10), dp(2)]

                            # 家庭住址标签
                            TableCellLabel:
                                text: "家庭住址:"
                                size_hint_x: 0.4

                            # 家庭住址值
                            TableCellLabel:
                                id: address_value
                                text: ""
                                size_hint_x: 0.6

                # 重要生活方式
                TableSection:
                    id: lifestyle_section

                    TableHeaderLabel:
                        text: "重要生活方式"

                    GridLayout:
                        cols: 3
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: [0, dp(5)]

                        TableCellLabel:
                            text: "饮食:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "烟:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "酒:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: diet_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: smoking_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: alcohol_value
                            text: ""
                            size_hint_x: 0.33

                    TableCellLabel:
                        id: exercise_value
                        text: "运动情况:"
                        size_hint_x: 1.0

                # 重要生理状态
                TableSection:
                    id: physiological_section

                    TableHeaderLabel:
                        text: "重要生理状态"

                    GridLayout:
                        cols: 3
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: [0, dp(5)]

                        TableCellLabel:
                            text: "大便:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "小便:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "睡眠情况:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: stool_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: urine_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: sleep_value
                            text: ""
                            size_hint_x: 0.33

                # 重要病史
                TableSection:
                    id: disease_history_section

                    TableHeaderLabel:
                        text: "重要病史"

                    BoxLayout:
                        id: disease_history_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

                # 家族史
                TableSection:
                    id: family_history_section

                    TableHeaderLabel:
                        text: "家族史"

                    BoxLayout:
                        id: family_history_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

                # 血型及基因信息
                TableSection:
                    id: blood_gene_section

                    TableHeaderLabel:
                        text: "血型及基因信息"

                    GridLayout:
                        cols: 2
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: [0, dp(5)]

                        TableCellLabel:
                            text: "血型:"
                            size_hint_x: 0.5

                        TableCellLabel:
                            id: blood_type_value
                            text: ""
                            size_hint_x: 0.5

                        TableCellLabel:
                            text: "Rh():"
                            size_hint_x: 0.5

                        TableCellLabel:
                            id: rh_value
                            text: ""
                            size_hint_x: 0.5

                    BoxLayout:
                        id: gene_info_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

                # 近期生命体征（原重要基础监测结果位置）
                TableSection:
                    id: vital_signs_section

                    TableHeaderLabel:
                        text: "近期生命体征"

                    GridLayout:
                        cols: 3
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: [0, dp(5)]

                        TableCellLabel:
                            text: "身高(CM):"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "体重(Kg):"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "BMI(Kg/m²):"
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: height_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: weight_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: bmi_value
                            text: ""
                            size_hint_x: 0.33

                    GridLayout:
                        cols: 3
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: [0, dp(5)]

                        TableCellLabel:
                            text: "血压(左侧):"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "血压(右侧):"
                            size_hint_x: 0.33

                        TableCellLabel:
                            text: "心率:"
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: left_bp_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: right_bp_value
                            text: ""
                            size_hint_x: 0.33

                        TableCellLabel:
                            id: heart_rate_value
                            text: ""
                            size_hint_x: 0.33

                # 主要诊断
                TableSection:
                    id: diagnosis_section

                    TableHeaderLabel:
                        text: "主要诊断"

                    BoxLayout:
                        id: diagnosis_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

                # 药物过敏史 - 新增独立框
                TableSection:
                    id: allergies_section

                    TableHeaderLabel:
                        text: "药物过敏史"

                    BoxLayout:
                        id: allergies_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

                # 存在的主要健康风险
                TableSection:
                    id: risks_section

                    TableHeaderLabel:
                        text: "存在的主要健康风险"

                    BoxLayout:
                        id: risks_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

                # 目前用药
                TableSection:
                    id: medication_section

                    TableHeaderLabel:
                        text: "目前用药"

                    BoxLayout:
                        id: medication_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)

        # 按钮区域
        BoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(60)
            spacing: dp(20)

            Button:
                text: "返回"
                background_color: app.theme.PRIMARY_COLOR
                color: app.theme.TEXT_LIGHT
                font_size: dp(16)
                on_release: app.root.current = 'homepage_screen'

            Button:
                text: "刷新数据"
                background_color: app.theme.PRIMARY_COLOR
                color: app.theme.TEXT_LIGHT
                font_size: dp(16)
                on_release: root.load_health_data()
""")

class GeneralHealthScreen(Screen):
    """健康状况一览表屏幕"""

    def __init__(self, **kwargs):
        super(GeneralHealthScreen, self).__init__(**kwargs)
        self.theme = AppTheme()
        # 延迟加载数据，确保界面完全初始化后
        Clock.schedule_once(self.load_health_data, 0.1)

    def on_enter(self):
        """每次进入页面时刷新数据"""
        Clock.schedule_once(self.load_health_data, 0.1)

    def load_health_data(self, *args):
        """加载用户健康数据"""
        try:
            # 获取当前用户ID
            app = App.get_running_app()
            user_id = "default_user_id"

            # 尝试安全地获取用户ID
            try:
                user_data = getattr(app, 'user_data', {})
                if isinstance(user_data, dict):
                    if 'id' in user_data:
                        user_id = user_data['id']
                    elif 'user_id' in user_data:
                        user_id = user_data['user_id']
                    elif 'username' in user_data:
                        user_id = user_data['username']

                # 填充基本信息
                self._populate_basic_info(user_data)

            except Exception as e:
                print(f"获取用户数据时出错: {str(e)}")

            # 从存储中获取健康数据
            self._load_health_info(user_id)

        except Exception as e:
            print(f"加载健康数据时出错: {str(e)}")

    def _populate_basic_info(self, user_data):
        """填充用户基本信息"""
        # 姓名
        if 'real_name' in user_data:
            self.ids.name_value.text = user_data.get('real_name', '')
        elif 'username' in user_data:
            self.ids.name_value.text = user_data.get('username', '')
        else:
            self.ids.name_value.text = "未知"

        # 性别
        self.ids.gender_value.text = user_data.get('gender', '')

        # 出生日期
        self.ids.birth_date_value.text = user_data.get('birth_date', '')

        # 民族
        self.ids.ethnic_value.text = user_data.get('ethnic', '')

        # 教育程度
        self.ids.education_value.text = user_data.get('education', '')

        # 联系电话
        self.ids.phone_value.text = user_data.get('phone', '')

        # 家庭住址
        self.ids.address_value.text = user_data.get('address', '')

        # 紧急联系人
        self.ids.emergency_contact_value.text = user_data.get('emergency_contact', '')

        # 紧急联系电话
        self.ids.emergency_phone_value.text = user_data.get('emergency_phone', '')

        # 模拟一些基本用户数据（实际应用中应从真实数据源获取）
        if not self.ids.name_value.text or self.ids.name_value.text == "未知":
            # 如果没有获取到用户名，使用测试数据
            self.ids.name_value.text = "张三"
            self.ids.gender_value.text = "男"
            self.ids.birth_date_value.text = "1985-06-12"
            self.ids.ethnic_value.text = "汉族"
            self.ids.education_value.text = "大学本科"
            self.ids.phone_value.text = "13812345678"
            self.ids.address_value.text = "北京市海淀区中关村大街123号"
            self.ids.emergency_contact_value.text = "李四"
            self.ids.emergency_phone_value.text = "13987654321"

    def _load_health_info(self, user_id):
        """从存储加载健康信息"""
        try:
            # 清除现有数据，避免刷新时重复添加
            self._clear_containers()

            # 获取存储实例
            storage = get_storage()
            health_info = storage.get_health_info(user_id)

            if health_info:
                # 加载疾病史
                self._load_disease_history(health_info.get('diseases', []))

                # 加载家族史
                self._load_family_history(health_info.get('family_diseases', []))

                # 加载血型信息
                blood_type = health_info.get('blood_type', '')
                if blood_type:
                    self.ids.blood_type_value.text = blood_type

                # 加载Rh信息
                rh_value = health_info.get('rh_factor', '')
                if rh_value:
                    self.ids.rh_value.text = rh_value

                # 加载基因信息
                self._load_gene_info(health_info.get('gene_info', []))

                # 加载药物过敏信息
                self._load_allergies(health_info.get('drug_allergies', []))

            # 加载生活方式信息 - 这里假设从其他地方获取
            self._load_lifestyle_info(user_id)

            # 加载诊断信息 - 这里假设从其他地方获取
            self._load_diagnosis_info(user_id)

            # 加载用药信息 - 这里假设从其他地方获取
            self._load_medication_info(user_id)

            # 加载健康风险信息 - 这里假设是基于其他数据计算得出
            self._calculate_health_risks()

        except Exception as e:
            print(f"从存储加载健康信息时出错: {str(e)}")

    def _clear_containers(self):
        """清除所有容器中的现有数据，避免刷新时重复添加"""
        # 清除疾病史容器
        if hasattr(self.ids, 'disease_history_container'):
            self.ids.disease_history_container.clear_widgets()

        # 清除家族史容器
        if hasattr(self.ids, 'family_history_container'):
            self.ids.family_history_container.clear_widgets()

        # 清除基因信息容器
        if hasattr(self.ids, 'gene_info_container'):
            self.ids.gene_info_container.clear_widgets()

        # 清除诊断信息容器
        if hasattr(self.ids, 'diagnosis_container'):
            self.ids.diagnosis_container.clear_widgets()

        # 清除药物过敏史容器
        if hasattr(self.ids, 'allergies_container'):
            self.ids.allergies_container.clear_widgets()

        # 清除健康风险容器
        if hasattr(self.ids, 'risks_container'):
            self.ids.risks_container.clear_widgets()

        # 清除用药信息容器
        if hasattr(self.ids, 'medication_container'):
            self.ids.medication_container.clear_widgets()

    def _load_disease_history(self, diseases):
        """加载疾病史"""
        container = self.ids.disease_history_container

        if not diseases:
            container.add_widget(TableCellLabel(text="无记录"))
            return

        for i, disease in enumerate(diseases):
            disease_text = f"{i+1}. {disease.get('name', '')}"
            if disease.get('duration'):
                disease_text += f" | 病程: {disease.get('duration', '')}"
            if disease.get('medication'):
                disease_text += f" | 用药: {disease.get('medication', '')}"
            if disease.get('effect') and disease.get('effect') != "控制效果":
                disease_text += f" | 控制效果: {disease.get('effect', '')}"

            container.add_widget(TableCellLabel(text=disease_text))

    def _load_family_history(self, family_diseases):
        """加载家族史"""
        container = self.ids.family_history_container

        if not family_diseases:
            container.add_widget(TableCellLabel(text="无记录"))
            return

        for i, disease in enumerate(family_diseases):
            disease_text = f"{i+1}. {disease.get('name', '')}"
            if disease.get('relation'):
                disease_text += f" | 关系: {disease.get('relation', '')}"

            container.add_widget(TableCellLabel(text=disease_text))

    def _load_gene_info(self, gene_info):
        """加载基因信息"""
        container = self.ids.gene_info_container

        if not gene_info:
            container.add_widget(TableCellLabel(text="无基因信息记录"))
            return

        container.add_widget(TableCellLabel(text="基因信息:"))
        for i, info in enumerate(gene_info):
            container.add_widget(TableCellLabel(text=f"{i+1}. {info}"))

    def _load_allergies(self, allergies):
        """加载药物过敏信息"""
        allergies_container = self.ids.allergies_container

        if not allergies:
            allergies_container.add_widget(TableCellLabel(text="无药物过敏记录"))
            return

        for i, allergy in enumerate(allergies):
            allergies_container.add_widget(TableCellLabel(text=f"{i+1}. {allergy}"))

    def _load_lifestyle_info(self, user_id):
        """加载生活方式信息"""
        # 这里假设从另一个数据源加载生活方式信息
        # 实际应用中需要替换为真实的数据源接口

        # 示例数据
        lifestyle_data = {
            "diet": "清淡饮食",
            "smoking": "不吸烟",
            "alcohol": "偶尔饮酒",
            "exercise": "每周运动3次，每次30分钟"
        }

        # 更新生活方式
        self.ids.diet_value.text = lifestyle_data.get("diet", "")
        self.ids.smoking_value.text = lifestyle_data.get("smoking", "")
        self.ids.alcohol_value.text = lifestyle_data.get("alcohol", "")
        self.ids.exercise_value.text = f"运动情况: {lifestyle_data.get('exercise', '')}"

        # 更新生理状态
        stool_info = "正常"
        urine_info = "正常"
        sleep_info = "6-8小时/天"

        self.ids.stool_value.text = stool_info
        self.ids.urine_value.text = urine_info
        self.ids.sleep_value.text = sleep_info

        # 身体数据
        height = 175  # 厘米
        weight = 70   # 千克

        # 计算BMI
        if height > 0:
            bmi = weight / ((height/100) ** 2)
            bmi_text = f"{bmi:.1f}"

            # BMI判断
            if bmi < 18.5:
                bmi_text += " (偏瘦)"
            elif bmi < 24:
                bmi_text += " (正常)"
            elif bmi < 28:
                bmi_text += " (超重)"
            else:
                bmi_text += " (肥胖)"
        else:
            bmi_text = "无数据"

        self.ids.height_value.text = f"{height}"
        self.ids.weight_value.text = f"{weight}"
        self.ids.bmi_value.text = bmi_text

        # 血压和心率信息
        self.ids.left_bp_value.text = "120/80 mmHg"
        self.ids.right_bp_value.text = "125/85 mmHg"
        self.ids.heart_rate_value.text = "75 次/分"

    def _load_diagnosis_info(self, user_id):
        """加载诊断信息"""
        container = self.ids.diagnosis_container

        # 示例数据 - 实际应用中需要替换
        diagnoses = [
            "高血压 II级",
            "2型糖尿病",
            "血脂异常"
        ]

        if not diagnoses:
            container.add_widget(TableCellLabel(text="无诊断记录"))
            return

        for i, diagnosis in enumerate(diagnoses):
            container.add_widget(TableCellLabel(text=f"{i+1}. {diagnosis}"))

    def _load_medication_info(self, user_id):
        """加载用药信息"""
        container = self.ids.medication_container

        # 示例数据 - 实际应用中需要替换
        medications = [
            {"name": "硝苯地平缓释片", "dosage": "10mg", "frequency": "每日两次"},
            {"name": "阿司匹林", "dosage": "100mg", "frequency": "每日一次"},
            {"name": "二甲双胍", "dosage": "500mg", "frequency": "每日三次"}
        ]

        if not medications:
            container.add_widget(TableCellLabel(text="无用药记录"))
            return

        for i, med in enumerate(medications):
            med_text = f"{i+1}. {med.get('name', '')}"
            if med.get('dosage'):
                med_text += f" | 剂量: {med.get('dosage', '')}"
            if med.get('frequency'):
                med_text += f" | 频次: {med.get('frequency', '')}"

            container.add_widget(TableCellLabel(text=med_text))

    def _calculate_health_risks(self):
        """计算健康风险"""
        container = self.ids.risks_container

        # 示例数据 - 实际应用中需要基于真实的健康指标计算
        risks = [
            "心血管疾病风险中等",
            "糖尿病并发症风险",
            "跌倒风险低"
        ]

        if not risks:
            container.add_widget(TableCellLabel(text="未检测到明显健康风险"))
            return

        for i, risk in enumerate(risks):
            container.add_widget(TableCellLabel(text=f"{i+1}. {risk}"))