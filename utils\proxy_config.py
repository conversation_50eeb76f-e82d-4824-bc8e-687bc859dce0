"""
代理配置工具模块
用于禁用代理设置，避免代理连接问题
"""

import os
import logging

logger = logging.getLogger(__name__)

def disable_proxy_globally():
    """
    全局禁用代理设置
    清除所有可能的代理环境变量
    """
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy',
        'ALL_PROXY', 'all_proxy',
        'NO_PROXY', 'no_proxy'
    ]
    
    removed_count = 0
    for var in proxy_vars:
        if var in os.environ:
            old_value = os.environ.pop(var)
            logger.info(f"已移除代理环境变量 {var}: {old_value}")
            removed_count += 1
    
    if removed_count > 0:
        logger.info(f"已移除 {removed_count} 个代理环境变量")
    else:
        logger.debug("未发现代理环境变量")

def get_no_proxy_config():
    """
    获取禁用代理的requests配置
    
    Returns:
        dict: 包含proxies=None的配置字典
    """
    return {
        'proxies': {
            'http': None,
            'https': None,
            'ftp': None
        }
    }

def apply_no_proxy_to_requests():
    """
    为requests库应用全局代理禁用设置
    """
    try:
        import requests
        
        # 保存原始的request方法
        if not hasattr(requests, '_original_request'):
            requests._original_request = requests.request
        
        # 创建包装函数
        def no_proxy_request(*args, **kwargs):
            # 如果没有指定proxies参数，则禁用代理
            if 'proxies' not in kwargs:
                kwargs['proxies'] = {
                    'http': None,
                    'https': None,
                    'ftp': None
                }
            return requests._original_request(*args, **kwargs)
        
        # 替换request方法
        requests.request = no_proxy_request
        
        logger.info("已为requests库应用全局代理禁用设置")
        
    except ImportError:
        logger.warning("requests库未安装，无法应用代理禁用设置")
    except Exception as e:
        logger.error(f"应用代理禁用设置时出错: {e}")

def restore_requests_original():
    """
    恢复requests库的原始方法
    """
    try:
        import requests
        
        if hasattr(requests, '_original_request'):
            requests.request = requests._original_request
            delattr(requests, '_original_request')
            logger.info("已恢复requests库的原始方法")
        else:
            logger.warning("未找到requests库的原始方法")
            
    except ImportError:
        logger.warning("requests库未安装")
    except Exception as e:
        logger.error(f"恢复requests原始方法时出错: {e}")

def check_proxy_status():
    """
    检查当前代理状态
    
    Returns:
        dict: 包含代理状态信息的字典
    """
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy',
        'ALL_PROXY', 'all_proxy'
    ]
    
    active_proxies = {}
    for var in proxy_vars:
        if var in os.environ:
            active_proxies[var] = os.environ[var]
    
    status = {
        'has_proxy_vars': len(active_proxies) > 0,
        'active_proxies': active_proxies,
        'proxy_count': len(active_proxies)
    }
    
    return status

def log_proxy_status():
    """
    记录当前代理状态到日志
    """
    status = check_proxy_status()
    
    if status['has_proxy_vars']:
        logger.warning(f"检测到 {status['proxy_count']} 个活动代理变量:")
        for var, value in status['active_proxies'].items():
            logger.warning(f"  {var} = {value}")
    else:
        logger.info("未检测到代理环境变量")

# 初始化时自动禁用代理
def initialize_proxy_config():
    """
    初始化代理配置
    自动禁用代理设置
    """
    logger.info("初始化代理配置...")
    
    # 记录当前状态
    log_proxy_status()
    
    # 禁用代理
    disable_proxy_globally()
    
    # 为requests应用代理禁用
    apply_no_proxy_to_requests()
    
    logger.info("代理配置初始化完成")

# 模块导入时自动初始化
if __name__ != "__main__":
    initialize_proxy_config()
