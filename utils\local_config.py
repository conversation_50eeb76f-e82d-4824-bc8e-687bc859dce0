"""
本地开发环境配置
"""

# 服务器配置
SERVER_CONFIG = {
    # 本地服务器配置
    "LOCAL": {
        "name": "本地服务器",
        "url": "http://localhost:8006",
        "api_prefix": "api"
    },
    # 本地回环地址配置
    "LOOPBACK": {
        "name": "本地回环地址",
        "url": "http://127.0.0.1:8006",
        "api_prefix": "api"
    },
    # 公网服务器配置
    "PUBLIC": {
        "name": "公网服务器",
        "url": "http://************:80",
        "api_prefix": "api"
    }
}

# 默认使用的服务器
DEFAULT_SERVER = "PUBLIC"

# 服务器优先级顺序（从高到低）- 远程服务器优先
SERVER_PRIORITY = ["PUBLIC", "LOCAL", "LOOPBACK"]

# API超时设置（秒）
API_TIMEOUT = 30

# 最大重试次数
MAX_RETRIES = 3

# 健康检查超时（秒）
HEALTH_CHECK_TIMEOUT = 3

# 健康检查缓存时间（秒）
HEALTH_CHECK_CACHE_TIME = 60

# 服务器失败阈值
SERVER_FAILURE_THRESHOLD = 3

# 降级模式持续时间（秒）
DEGRADED_MODE_DURATION = 300  # 5分钟
