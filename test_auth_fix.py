#!/usr/bin/env python3
"""
测试认证修复的脚本
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_auth_manager_imports():
    """测试认证管理器的导入"""
    try:
        from utils.auth_manager import get_auth_manager
        logger.info("✓ 认证管理器导入成功")
        
        auth_manager = get_auth_manager()
        logger.info("✓ 认证管理器实例创建成功")
        
        # 测试获取用户信息
        user_info = auth_manager.get_current_user_info()
        logger.info(f"用户信息: {user_info}")
        
        # 测试获取认证头
        headers = auth_manager.get_auth_headers()
        logger.info(f"认证头: {headers}")
        
        return True
    except Exception as e:
        logger.error(f"✗ 认证管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_cloud_api_imports():
    """测试云API的导入"""
    try:
        from utils.cloud_api import CloudAPI
        logger.info("✓ 云API导入成功")
        
        # 创建实例
        api = CloudAPI()
        logger.info("✓ 云API实例创建成功")
        
        return True
    except Exception as e:
        logger.error(f"✗ 云API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_storage_imports():
    """测试存储模块的导入"""
    try:
        from utils.storage import UserStorage
        logger.info("✓ 用户存储导入成功")
        
        # 测试获取用户数据
        user_data = UserStorage.get_user_data(sync_from_backend=False)
        logger.info(f"用户数据: {user_data}")
        
        return True
    except Exception as e:
        logger.error(f"✗ 用户存储测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_user_manager_imports():
    """测试用户管理器的导入"""
    try:
        from utils.user_manager import get_user_manager
        logger.info("✓ 用户管理器导入成功")
        
        user_manager = get_user_manager()
        logger.info("✓ 用户管理器实例创建成功")
        
        return True
    except Exception as e:
        logger.error(f"✗ 用户管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始测试认证修复...")
    
    tests = [
        ("认证管理器", test_auth_manager_imports),
        ("云API", test_cloud_api_imports),
        ("用户存储", test_storage_imports),
        ("用户管理器", test_user_manager_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试 {test_name} ---")
        if test_func():
            passed += 1
            logger.info(f"✓ {test_name} 测试通过")
        else:
            logger.error(f"✗ {test_name} 测试失败")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
