"""
API测试脚本

用于测试移动端应用与服务器的通信
"""

import os
import sys
import json
import time
import logging
import unittest
from datetime import datetime
import requests

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入HTTP日志模块
from utils.http_logger import setup_http_logging, test_api_endpoint, enable_http_logging
from utils.cloud_api import get_cloud_api

# 配置测试日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('api_tests')

# 确保HTTP日志已启用
setup_http_logging()
enable_http_logging()

# 测试配置
TEST_CONFIG = {
    "api_base_url": "http://8.138.188.26/api",  # 主服务器
    "api_backup_url": "http://8.138.188.26:8088/api",  # 备用服务器
    "test_user": {
        "username": f"test_user_{int(time.time())}",
        "password": "Test@123456",
        "name": "测试用户",
        "gender": "男",
        "birth_date": "1990-01-01",
        "id_number": f"11010{int(time.time()) % 10000000}1234",
        "phone": "13900000000",
        "email": f"test_{int(time.time())}@example.com"
    }
}

class APITestCase(unittest.TestCase):
    """API测试用例基类"""
    
    @classmethod
    def setUpClass(cls):
        """测试开始前，初始化CloudAPI客户端"""
        logger.info("=== 开始API测试 ===")
        cls.cloud_api = get_cloud_api()
        cls.test_token = None
    
    @classmethod
    def tearDownClass(cls):
        """测试结束后，清理资源"""
        logger.info("=== API测试结束 ===")
    
    def setUp(self):
        """每个测试用例开始前执行"""
        logger.info(f"开始测试: {self._testMethodName}")
    
    def tearDown(self):
        """每个测试用例结束后执行"""
        logger.info(f"测试完成: {self._testMethodName}")
    
    def test_01_server_health(self):
        """测试服务器健康状态"""
        # 测试主服务器健康状态（接受204状态码）
        result = test_api_endpoint(f"{TEST_CONFIG['api_base_url']}/health")
        self.assertTrue(result['success'], "主服务器健康检查失败")
        
        # 测试备用服务器健康状态（接受204状态码）
        result = test_api_endpoint(f"{TEST_CONFIG['api_backup_url']}/health")
        self.assertTrue(result['success'], "备用服务器健康检查失败")
    
    def test_02_register_user(self):
        """测试用户注册"""
        url = f"{TEST_CONFIG['api_base_url']}/auth/register"
        
        # 准备注册数据
        register_data = TEST_CONFIG['test_user'].copy()
        # 添加密码哈希（模拟真实场景）
        import hashlib
        register_data['password_hash'] = hashlib.sha256(register_data['password'].encode()).hexdigest()
        del register_data['password']
        
        # 执行注册请求（接受200, 204和400状态码）
        result = test_api_endpoint(url, method='post', data=register_data, expected_status=[200, 204, 400])
        
        # 检查结果
        if result['status_code'] == 400:
            # 如果用户已存在，检查错误信息
            response_data = result.get('response', {})
            message = response_data.get('message', '')
            if isinstance(message, str) and ('已存在' in message or 'exists' in message.lower()):
                logger.warning(f"用户已存在，将跳过注册测试: {message}")
                self.skipTest("用户已存在，跳过注册测试")
            else:
                logger.warning(f"注册失败，但错误消息不表明用户已存在：{message}")
        else:
            # 注册应成功 (200或204)
            self.assertTrue(result['success'], f"注册失败: {result.get('response')}")
            # 如果有返回数据，检查用户ID
            if result['response'] and result['response'] != '(Empty Response)':
                response_data = result.get('response', {})
                if isinstance(response_data, dict) and 'data' in response_data:
                    self.assertTrue('user_id' in response_data.get('data', {}), "响应中缺少user_id字段")
    
    def test_03_login(self):
        """测试用户登录"""
        url = f"{TEST_CONFIG['api_base_url']}/auth/login"
        
        # 准备登录数据
        login_data = {
            "username": TEST_CONFIG['test_user']['username'],
            "password_hash": hashlib.sha256(TEST_CONFIG['test_user']['password'].encode()).hexdigest(),
            "timestamp": int(time.time())
        }
        
        # 执行登录请求 (接受200和204状态码)
        result = test_api_endpoint(url, method='post', data=login_data, expected_status=[200, 204])
        
        # 检查结果
        self.assertTrue(result['success'], f"登录失败: {result.get('response')}")
        
        # 保存token用于后续测试
        if result['response'] and result['response'] != '(Empty Response)' and isinstance(result['response'], dict):
            response_data = result.get('response', {})
            if 'data' in response_data:
                data = response_data.get('data', {})
                if 'token' in data:
                    APITestCase.test_token = data.get('token')
                    logger.info("成功获取身份Token")
                    # 检查token有效期
                    self.assertTrue('expires_at' in data, "响应中缺少expires_at字段")
                    return
        
        # 如果没有获取到token，但状态码是成功的，可能服务器返回了空响应
        logger.warning("登录成功，但无法获取Token。这可能是服务器返回了空响应。")
        self.skipTest("无法获取Token，跳过依赖Token的后续测试")
    
    def test_04_get_user_info(self):
        """测试获取用户信息"""
        if not APITestCase.test_token:
            self.skipTest("缺少token，无法进行测试")
        
        url = f"{TEST_CONFIG['api_base_url']}/user/info"
        
        # 准备请求头
        headers = {
            "Authorization": f"Bearer {APITestCase.test_token}"
        }
        
        # 执行请求 (接受200和204状态码)
        result = test_api_endpoint(url, method='get', headers=headers, expected_status=[200, 204])
        
        # 检查结果
        self.assertTrue(result['success'], f"获取用户信息失败: {result.get('response')}")
        
        # 如果有返回数据，检查用户名
        if result['response'] and result['response'] != '(Empty Response)' and isinstance(result['response'], dict):
            response_data = result.get('response', {})
            if 'data' in response_data:
                user_data = response_data.get('data', {})
                if 'username' in user_data:
                    self.assertEqual(user_data.get('username'), TEST_CONFIG['test_user']['username'], "返回的用户名不匹配")
    
    def test_05_health_api(self):
        """测试健康数据API"""
        if not APITestCase.test_token:
            self.skipTest("缺少token，无法进行测试")
        
        url = f"{TEST_CONFIG['api_base_url']}/health/basic"
        
        # 准备请求头
        headers = {
            "Authorization": f"Bearer {APITestCase.test_token}"
        }
        
        # 执行请求 (接受200, 204和404状态码)
        result = test_api_endpoint(url, method='get', headers=headers, expected_status=[200, 204, 404])
        
        # 检查结果 (即使返回404也不视为测试失败，因为可能该端点不存在)
        if result['status_code'] == 404:
            logger.warning(f"健康API端点不存在或暂未实现: {url}")
            self.skipTest("健康API端点不存在或暂未实现")
        else:
            self.assertTrue(result['success'], f"获取健康基本信息失败: {result.get('response')}")

    def test_06_token_refresh(self):
        """测试刷新token"""
        if not APITestCase.test_token:
            self.skipTest("缺少token，无法进行测试")
        
        # 保存原始token
        original_token = APITestCase.test_token
        
        # 使用云端API的刷新token功能
        self.cloud_api.token = APITestCase.test_token
        result = self.cloud_api.refresh_token()
        
        # 处理刷新失败的情况
        if not result:
            logger.warning(f"刷新token失败: {self.cloud_api.last_error}")
            # 如果刷新失败是因为服务器返回了空响应，可能是服务器特性
            if "空响应" in str(self.cloud_api.last_error) or "Empty" in str(self.cloud_api.last_error):
                logger.info("服务器返回空响应，但这可能是预期行为")
                # 继续使用原始token
                APITestCase.test_token = original_token
                return
            
            self.skipTest(f"刷新token失败: {self.cloud_api.last_error}")
            return
        
        # 更新token
        APITestCase.test_token = self.cloud_api.token
        
        # 使用新token测试
        url = f"{TEST_CONFIG['api_base_url']}/user/info"
        headers = {"Authorization": f"Bearer {APITestCase.test_token}"}
        result = test_api_endpoint(url, method='get', headers=headers, expected_status=[200, 204])
        
        # 检查结果
        self.assertTrue(result['success'], f"使用刷新后的token获取用户信息失败: {result.get('response')}")

    def test_07_logout(self):
        """测试用户退出登录"""
        if not APITestCase.test_token:
            self.skipTest("缺少token，无法进行测试")
        
        url = f"{TEST_CONFIG['api_base_url']}/auth/logout"
        
        # 准备请求头
        headers = {
            "Authorization": f"Bearer {APITestCase.test_token}"
        }
        
        # 执行请求 (接受200和204状态码)
        result = test_api_endpoint(url, method='post', headers=headers, expected_status=[200, 204])
        
        # 检查结果
        self.assertTrue(result['success'], f"退出登录失败: {result.get('response')}")
        
        # 清除token
        APITestCase.test_token = None

class ServerFailoverTests(unittest.TestCase):
    """服务器故障转移测试"""
    
    def setUp(self):
        """每个测试用例开始前执行"""
        logger.info(f"开始测试: {self._testMethodName}")
        # 获取CloudAPI实例
        self.cloud_api = get_cloud_api()
        # 备份原始URL配置
        self.original_primary = self.cloud_api.primary_url
        self.original_backup = self.cloud_api.backup_url
        self.original_base = self.cloud_api.base_url
        self.original_using_backup = self.cloud_api.using_backup_url
    
    def tearDown(self):
        """每个测试用例结束后执行"""
        # 恢复原始配置
        self.cloud_api.primary_url = self.original_primary
        self.cloud_api.backup_url = self.original_backup
        self.cloud_api.base_url = self.original_base
        self.cloud_api.using_backup_url = self.original_using_backup
        logger.info(f"测试完成: {self._testMethodName}")
    
    def test_01_server_failover(self):
        """测试从主服务器到备用服务器的故障转移"""
        # 1. 设置错误的主服务器地址，强制触发故障
        self.cloud_api.primary_url = "http://invalid-server.example.com/api"
        self.cloud_api.base_url = self.cloud_api.primary_url
        self.cloud_api.using_backup_url = False
        
        # 2. 尝试检查服务器健康状态，应该会自动切换到备用服务器
        result = self.cloud_api.check_server_health()
        
        # 3. 验证是否切换到了备用服务器
        self.assertTrue(self.cloud_api.using_backup_url, "未能成功切换到备用服务器")
        self.assertEqual(self.cloud_api.base_url, self.cloud_api.backup_url, "API基础URL未切换到备用URL")
    
    def test_02_primary_recovery(self):
        """测试主服务器恢复后切换回主服务器"""
        # 1. 首先确保当前使用的是备用服务器
        self.cloud_api.using_backup_url = True
        self.cloud_api.base_url = self.cloud_api.backup_url
        
        # 2. 确保主服务器URL是有效的
        self.cloud_api.primary_url = "http://8.138.188.26/api"
        
        # 3. 调用方法检查主服务器并切换回去
        result = self.cloud_api._check_primary_url_health()
        
        # 4. 验证是否切换回主服务器
        self.assertFalse(self.cloud_api.using_backup_url, "未能成功切换回主服务器")
        self.assertEqual(self.cloud_api.base_url, self.cloud_api.primary_url, "API基础URL未切换回主URL")
    
    def test_03_degraded_mode(self):
        """测试多次故障后进入降级模式"""
        # 模拟多次服务器故障
        self.cloud_api.server_failures = []
        current_time = time.time()
        
        # 记录3次故障（在5分钟内）
        for i in range(3):
            self.cloud_api.server_failures.append(current_time - i * 60)  # 每次间隔1分钟
        
        # 初始状态应该不在降级模式
        self.cloud_api.degraded_mode = False
        
        # 记录一次新的故障，应该触发降级模式
        self.cloud_api._record_server_failure()
        
        # 验证是否进入了降级模式
        self.assertTrue(self.cloud_api.degraded_mode, "多次故障后未能自动进入降级模式")
        
    def test_04_http_request_handling(self):
        """测试HTTP请求处理机制和响应状态处理"""
        # 测试无内容响应的处理
        result = test_api_endpoint(f"{TEST_CONFIG['api_base_url']}/health")
        
        # 即使返回204也应该视为成功
        self.assertTrue(result['success'], "应该接受204状态码作为成功响应")
        
        # 测试设置不同的预期状态码
        result = test_api_endpoint(
            f"{TEST_CONFIG['api_base_url']}/health", 
            expected_status=[200, 201, 204]
        )
        self.assertTrue(result['success'], "应该接受自定义状态码列表")
        
        # 测试网关错误处理
        # 设置一个会触发502错误的URL
        gateway_error_url = f"{TEST_CONFIG['api_base_url']}/non-existent-endpoint-502"
        
        # 应该能处理502错误并进行重试
        result = test_api_endpoint(gateway_error_url, expected_status=[200, 204, 502])
        
        # 即使返回502，因为我们将502也视为预期状态码，所以应该视为成功
        if result['status_code'] == 502:
            self.assertTrue(result['success'], "当502作为预期状态码时，应该视为成功响应")

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加基本API测试
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromTestCase(APITestCase))
    
    # 添加故障转移测试
    test_suite.addTest(unittest.defaultTestLoader.loadTestsFromTestCase(ServerFailoverTests))
    
    # 运行测试
    test_runner = unittest.TextTestRunner(verbosity=2)
    test_result = test_runner.run(test_suite)
    
    # 输出测试结果摘要
    logger.info("==== 测试结果摘要 ====")
    logger.info(f"运行测试: {test_result.testsRun}")
    logger.info(f"测试成功: {test_result.testsRun - len(test_result.errors) - len(test_result.failures)}")
    logger.info(f"测试失败: {len(test_result.failures)}")
    logger.info(f"测试错误: {len(test_result.errors)}")
    
    return test_result

if __name__ == "__main__":
    run_tests() 