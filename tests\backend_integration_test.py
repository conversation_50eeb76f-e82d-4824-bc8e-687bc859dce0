#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
移动端API单元测试
使用FastAPI的TestClient测试移动端API接口
"""

import os
import sys
import json
import pytest
import hashlib
from datetime import datetime

# 添加项目根目录到路径中
sys.path.append(os.path.abspath(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 尝试导入FastAPI测试客户端
try:
    from fastapi.testclient import TestClient
    from backend.app import app  # 假设后端的FastAPI应用实例在backend/app.py
    
    # 创建测试客户端
    client = TestClient(app)
    FASTAPI_CLIENT_AVAILABLE = True
except (ImportError, ModuleNotFoundError):
    print("警告: 无法导入FastAPI TestClient或后端app实例。将使用requests库进行测试。")
    FASTAPI_CLIENT_AVAILABLE = False
    import time
    import requests

# 测试数据
test_mobile_user = {
    "username": "mobile_testuser",
    "fullName": "移动测试用户",
    "password": "mobilepass123",
    "email": "<EMAIL>",
    "role": "personal_user"
}

# 存储认证信息
auth_token = None
user_id = None

def make_request(method, url, json=None, headers=None):
    """根据环境使用TestClient或requests发送请求"""
    if FASTAPI_CLIENT_AVAILABLE:
        # 使用FastAPI TestClient
        if method == 'get':
            response = client.get(url, headers=headers)
        elif method == 'post':
            response = client.post(url, json=json, headers=headers)
        elif method == 'put':
            response = client.put(url, json=json, headers=headers)
        elif method == 'delete':
            response = client.delete(url, headers=headers)
        else:
            raise ValueError(f"不支持的请求方法: {method}")
    else:
        # 使用requests库
        url = f"http://8.138.188.26{url}"  # 使用实际服务器URL
        if method == 'get':
            response = requests.get(url, headers=headers)
        elif method == 'post':
            response = requests.post(url, json=json, headers=headers)
        elif method == 'put':
            response = requests.put(url, json=json, headers=headers)
        elif method == 'delete':
            response = requests.delete(url, headers=headers)
        else:
            raise ValueError(f"不支持的请求方法: {method}")
    
    return response

def test_mobile_register():
    """测试移动端用户注册API"""
    global user_id
    
    # 发送注册请求
    response = make_request(
        'post',
        "/api/auth/register",
        json={
            "username": test_mobile_user["username"],
            "fullName": test_mobile_user["fullName"],
            "password": test_mobile_user["password"],
            "email": test_mobile_user["email"],
            "role": test_mobile_user["role"]
        }
    )
    
    # 断言响应状态码和内容
    assert response.status_code in [200, 201, 204, 400], f"注册API返回了意外的状态码: {response.status_code}"
    
    # 检查响应内容
    result = response.json()
    
    # 处理重复注册的情况
    if response.status_code == 400 or result.get("success") is False:
        message = result.get("message", "")
        if "已存在" in message or "exists" in message.lower():
            print(f"用户已存在: {message}")
            return
        else:
            assert False, f"注册失败: {message}"
    
    # 注册成功的情况
    assert result["success"] is True, f"注册失败: {result}"
    assert "data" in result, "响应中缺少data字段"
    
    # 保存用户ID
    if "user_id" in result["data"]:
        user_id = result["data"]["user_id"]
        print(f"注册成功，用户ID: {user_id}")
    
    # 验证其他返回字段
    assert result["data"]["username"] == test_mobile_user["username"], "返回的用户名不匹配"
    assert result["data"]["email"] == test_mobile_user["email"], "返回的邮箱不匹配"

def test_mobile_login():
    """测试移动端登录API"""
    global auth_token, user_id
    
    # 创建密码哈希
    password_hash = hashlib.sha256(test_mobile_user["password"].encode()).hexdigest()
    
    # 发送登录请求
    response = make_request(
        'post',
        "/api/auth/login",
        json={
            "username": test_mobile_user["username"],
            "password_hash": password_hash,
            "timestamp": int(hashlib.sha256(b"timestamp").hexdigest(), 16) % 10**10
        }
    )
    
    # 断言响应状态码
    assert response.status_code in [200, 201, 204], f"登录API返回了意外的状态码: {response.status_code}"
    
    # 检查响应内容
    result = response.json()
    
    # 处理登录失败的情况
    if result.get("success") is False:
        message = result.get("message", "")
        print(f"登录失败: {message}")
        
        # 如果是用户名或密码错误，不视为测试失败
        if "用户名或密码错误" in message or "invalid" in message.lower():
            print("这可能是因为测试用户不存在，请先确保注册测试成功")
            return
        else:
            assert False, f"登录失败: {message}"
    
    # 登录成功的情况
    assert result["success"] is True, f"登录失败: {result}"
    assert "data" in result, "响应中缺少data字段"
    assert "token" in result["data"], "响应中缺少token字段"
    
    # 保存认证信息供后续测试使用
    auth_token = result["data"]["token"]
    if "user_id" in result["data"]:
        user_id = result["data"]["user_id"]
    
    print(f"登录成功，获取到token: {auth_token[:10]}...")

def test_mobile_user_info():
    """测试移动端获取用户信息API"""
    # 确保已登录
    if not auth_token:
        pytest.skip("需要先登录才能测试此接口")
    
    # 发送请求
    response = make_request(
        'get',
        "/api/user/info",
        headers={"Authorization": f"Bearer {auth_token}"}
    )
    
    # 断言响应状态码
    assert response.status_code in [200, 201, 204], f"获取用户信息API返回了意外的状态码: {response.status_code}"
    
    # 检查响应内容
    result = response.json()
    assert result["success"] is True, f"获取用户信息失败: {result}"
    assert "data" in result, "响应中缺少data字段"
    assert result["data"]["username"] == test_mobile_user["username"], "返回的用户名不匹配"
    
    print(f"成功获取用户信息: {result['data']['username']}")

def test_mobile_logout():
    """测试移动端退出登录API"""
    # 确保已登录
    if not auth_token:
        pytest.skip("需要先登录才能测试此接口")
    
    # 发送请求
    response = make_request(
        'post',
        "/api/auth/logout",
        headers={"Authorization": f"Bearer {auth_token}"}
    )
    
    # 断言响应状态码
    assert response.status_code in [200, 201, 204], f"退出登录API返回了意外的状态码: {response.status_code}"
    
    # 检查响应内容
    result = response.json()
    assert result["success"] is True, f"退出登录失败: {result}"
    
    print("成功退出登录")

def run_all_tests():
    """按顺序运行所有测试"""
    print("\n=== 开始移动端API测试 ===")
    
    print("\n测试1: 用户注册")
    try:
        test_mobile_register()
    except Exception as e:
        print(f"测试1失败: {str(e)}")
    
    print("\n测试2: 用户登录")
    try:
        test_mobile_login()
    except Exception as e:
        print(f"测试2失败: {str(e)}")
    
    print("\n测试3: 获取用户信息")
    try:
        test_mobile_user_info()
    except Exception as e:
        print(f"测试3失败: {str(e)}")
    
    print("\n测试4: 用户退出登录")
    try:
        test_mobile_logout()
    except Exception as e:
        print(f"测试4失败: {str(e)}")
    
    print("\n=== 移动端API测试结束 ===")

if __name__ == "__main__":
    if FASTAPI_CLIENT_AVAILABLE:
        # 使用pytest运行测试
        pytest.main(["-v", __file__])
    else:
        # 手动运行测试
        run_all_tests() 