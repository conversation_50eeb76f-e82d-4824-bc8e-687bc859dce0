import logging
import os
import json
import time
from functools import wraps
from collections import OrderedDict
import threading

# 极简日志配置，避免任何可能的递归问题
def configure_logging():
    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 定义日志文件路径
    log_file = os.path.join(log_dir, 'app.log')

    # 重置所有日志配置
    logging.shutdown()
    logging._handlerList.clear()

    # 使用最基本的日志配置，将默认级别设置为WARNING，减少日志输出
    logging.basicConfig(
        level=logging.WARNING,  # 提高默认日志级别到WARNING
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    # 为主应用模块设置INFO级别，保留重要信息
    logging.getLogger('__main__').setLevel(logging.INFO)

    # 设置各模块的日志级别

    # 第三方库日志级别
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('kivy').setLevel(logging.INFO)  # 保留Kivy的INFO日志

    # 应用模块日志级别
    logging.getLogger('mobile.utils.fix_logos').setLevel(logging.INFO)  # 只显示INFO及以上级别
    logging.getLogger('mobile.utils.logo_monitor').setLevel(logging.INFO)  # 只显示INFO及以上级别
    logging.getLogger('mobile.utils.ui_cleanup').setLevel(logging.INFO)  # 只显示INFO及以上级别
    logging.getLogger('mobile.widgets.logo_manager').setLevel(logging.INFO)  # 只显示INFO及以上级别

    # API相关模块保留INFO级别，因为这些是重要的操作日志
    logging.getLogger('utils.cloud_api').setLevel(logging.INFO)
    logging.getLogger('utils.local_api_client').setLevel(logging.INFO)
    logging.getLogger('api.api_client').setLevel(logging.INFO)

    # 初始化LRU缓存管理
    global lru_cache
    lru_cache = LRUCache(max_size_mb=10)
    
    # 启动定期清理线程
    cleanup_thread = threading.Thread(target=periodic_cleanup, daemon=True)
    cleanup_thread.start()

    print("优化日志配置已应用: 日志将被记录到 " + log_file)

class LRUCache:
    """LRU缓存管理类，用于管理JSON数据文件"""
    def __init__(self, max_size_mb=10):
        self.cache = OrderedDict()
        self.max_size = max_size_mb * 1024 * 1024  # 转换为字节
        self.current_size = 0
        self.lock = threading.Lock()
        
    def track_access(self, filepath):
        """记录文件访问"""
        with self.lock:
            if filepath in self.cache:
                self.cache.move_to_end(filepath)
            else:
                file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
                self.cache[filepath] = {'size': file_size, 'last_accessed': time.time()}
                self.current_size += file_size
                
                # 如果超过最大大小，清理最久未使用的文件
                while self.current_size > self.max_size and len(self.cache) > 1:
                    oldest = next(iter(self.cache))
                    self.current_size -= self.cache[oldest]['size']
                    self.cache.pop(oldest)
                    
    def cleanup(self):
        """清理过期的缓存文件"""
        with self.lock:
            for filepath in list(self.cache.keys()):
                if not os.path.exists(filepath):
                    self.current_size -= self.cache[filepath]['size']
                    self.cache.pop(filepath)

def periodic_cleanup():
    """定期清理缓存"""
    while True:
        time.sleep(3600)  # 每小时清理一次
        lru_cache.cleanup()

def track_file_access(func):
    """文件访问跟踪装饰器"""
    @wraps(func)
    def wrapper(filepath, *args, **kwargs):
        lru_cache.track_access(filepath)
        return func(filepath, *args, **kwargs)
    return wrapper

@track_file_access
def load_json_file(filepath):
    """加载JSON文件"""
    if not os.path.exists(filepath):
        return None
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

@track_file_access
def save_json_file(filepath, data):
    """保存JSON文件"""
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)