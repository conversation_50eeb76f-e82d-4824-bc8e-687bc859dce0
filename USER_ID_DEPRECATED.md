# user_id 字段弃用说明

## 背景

在移动端代码中，`user_id` 字段已被弃用，应该使用 `custom_id` 字段作为用户的唯一标识。这是因为 `custom_id` 是后端生成的带前缀的格式化ID（如 P_00000009），而 `user_id` 是旧版本使用的内部ID。

## 变更内容

我们已经修改了 `mobile\utils\user_manager.py` 文件，将所有 `user_id` 的引用替换为 `custom_id` 或标记为已弃用。具体变更如下：

1. 修改了 `UserAccount` 类的初始化方法，将 `user_id` 参数标记为已弃用
2. 修改了 `to_dict` 方法，不再优先返回 `user_id` 字段
3. 修改了 `save_accounts` 方法，不再保存 `user_id` 字段
4. 修改了 `load_current_user` 方法，优先使用 `custom_id` 查找用户
5. 修改了 `save_current_user` 方法，不再保存 `user_id` 字段
6. 修改了 `switch_user` 方法，优先使用 `custom_id` 查找用户
7. 修改了 `remove_account` 方法，使用 `custom_id` 而不是 `user_id`
8. 修改了 `update_account` 方法，使用 `custom_id` 而不是 `user_id`
9. 添加了 `get_user_by_custom_id` 方法，修改了 `get_user_by_id` 方法
10. 添加了 `get_current_user_custom_id` 方法，修改了 `get_current_user_id` 方法
11. 修改了 `save_user_to_local_db` 方法，不再返回 `user_id` 字段

## 新增方法

以下是新增的方法，应该优先使用这些方法：

1. `get_user_by_custom_id(custom_id)` - 根据 `custom_id` 获取用户
2. `get_current_user_custom_id()` - 获取当前用户的 `custom_id`

## 已弃用方法

以下方法已被标记为已弃用，但为了兼容旧代码仍然保留：

1. `get_user_by_id(user_id)` - 已弃用，请使用 `get_user_by_custom_id`
2. `get_current_user_id()` - 已弃用，请使用 `get_current_user_custom_id`
3. `generate_user_id(role)` - 已弃用，不再生成临时ID

## 使用建议

1. 所有新代码应该使用 `custom_id` 而不是 `user_id`
2. 使用 `get_current_user_custom_id()` 获取当前用户ID
3. 使用 `get_user_by_custom_id(custom_id)` 根据ID获取用户
4. 不要使用 `generate_user_id` 方法生成ID，所有ID应由后端生成

## 注意事项

1. 为了兼容旧代码，`user_id` 字段仍然存在，但不应再使用
2. 所有方法都会优先使用 `custom_id`，只有在找不到 `custom_id` 时才会尝试使用 `user_id`
3. 当使用 `user_id` 时，会记录警告信息
4. 如果用户没有 `custom_id`，某些功能可能无法正常工作
