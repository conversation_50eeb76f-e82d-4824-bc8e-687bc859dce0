"""
内存工具模块 - 提供内存管理和调试功能
"""
import gc
import sys
import logging

logger = logging.getLogger(__name__)

def check_for_circular_references():
    """
    优化的循环引用检查和清理

    Returns:
        int: 清理的对象数量
    """
    try:
        # 首先进行一次垃圾回收
        initial_collected = gc.collect()

        # 获取垃圾回收统计信息
        stats = gc.get_stats()
        if stats:
            logger.debug(f"垃圾回收统计: {stats}")

        # 检查是否有不可达的循环引用
        unreachable = gc.garbage
        if unreachable:
            logger.warning(f"发现 {len(unreachable)} 个不可达的循环引用对象")
            # 清理垃圾列表
            gc.garbage.clear()

        # 再次进行垃圾回收
        final_collected = gc.collect()

        total_cleaned = initial_collected + final_collected

        if total_cleaned > 0:
            logger.info(f"清理了 {total_cleaned} 个对象")

        return total_cleaned

    except Exception as e:
        logger.error(f"检查循环引用时出错: {e}")
        return 0

def optimize_memory():
    """
    内存优化函数

    Returns:
        dict: 优化结果统计
    """
    try:
        import psutil
        import os

        # 获取当前进程
        process = psutil.Process(os.getpid())

        # 记录优化前的内存使用
        memory_before = process.memory_info()

        # 执行垃圾回收
        collected = check_for_circular_references()

        # 记录优化后的内存使用
        memory_after = process.memory_info()

        # 计算内存节省
        memory_saved = memory_before.rss - memory_after.rss

        result = {
            'objects_collected': collected,
            'memory_before_mb': memory_before.rss / (1024 * 1024),
            'memory_after_mb': memory_after.rss / (1024 * 1024),
            'memory_saved_mb': memory_saved / (1024 * 1024)
        }

        logger.info(f"内存优化完成: 清理 {collected} 个对象, 节省 {result['memory_saved_mb']:.2f}MB")

        return result

    except ImportError:
        # 如果没有psutil，只执行垃圾回收
        collected = check_for_circular_references()
        return {'objects_collected': collected}
    except Exception as e:
        logger.error(f"内存优化时出错: {e}")
        return {'error': str(e)}

def log_memory_usage():
    """
    记录当前内存使用情况
    """
    try:
        import psutil
        import os

        # 获取当前进程
        process = psutil.Process(os.getpid())

        # 获取内存信息
        memory_info = process.memory_info()

        # 记录内存使用情况
        logger.info(f"内存使用: RSS={memory_info.rss / (1024*1024):.2f}MB, VMS={memory_info.vms / (1024*1024):.2f}MB")
    except ImportError:
        logger.warning("无法导入psutil模块，跳过内存使用记录")
    except Exception as e:
        logger.error(f"记录内存使用时出错: {e}")

def schedule_memory_cleanup():
    """
    调度定期内存清理
    """
    try:
        from kivy.clock import Clock

        def _cleanup_memory(dt):
            """定期内存清理回调"""
            try:
                result = optimize_memory()
                if result.get('objects_collected', 0) > 10:
                    logger.info(f"定期内存清理: 清理了 {result['objects_collected']} 个对象")
            except Exception as e:
                logger.error(f"定期内存清理时出错: {e}")

        # 每5分钟执行一次内存清理
        Clock.schedule_interval(_cleanup_memory, 300)
        logger.info("已启动定期内存清理任务")

    except ImportError:
        logger.warning("无法导入Clock模块，跳过定期内存清理")
    except Exception as e:
        logger.error(f"调度内存清理时出错: {e}")

def get_memory_stats():
    """
    获取内存统计信息

    Returns:
        dict: 内存统计信息
    """
    try:
        import psutil
        import os

        # 获取当前进程
        process = psutil.Process(os.getpid())

        # 获取内存信息
        memory_info = process.memory_info()

        # 获取系统内存信息
        system_memory = psutil.virtual_memory()

        stats = {
            'process_rss_mb': memory_info.rss / (1024 * 1024),
            'process_vms_mb': memory_info.vms / (1024 * 1024),
            'system_total_mb': system_memory.total / (1024 * 1024),
            'system_available_mb': system_memory.available / (1024 * 1024),
            'system_used_percent': system_memory.percent,
            'gc_counts': gc.get_count(),
            'gc_stats': gc.get_stats()
        }

        return stats

    except ImportError:
        return {'error': 'psutil not available'}
    except Exception as e:
        return {'error': str(e)}
