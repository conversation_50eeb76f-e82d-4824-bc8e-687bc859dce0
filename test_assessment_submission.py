#!/usr/bin/env python3
"""
测试评估结果提交API端点修复
"""

import requests
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_assessment_submission_endpoints():
    """测试评估结果提交端点"""
    base_url = "http://8.138.188.26:80/api"
    custom_id = "U_0002"  # 使用示例用户ID
    
    # 测试端点列表
    endpoints = [
        f"user-health-records/{custom_id}",  # 新的正确端点
        "assessment-responses",              # 原来的错误端点
        "assessments",                       # 量表列表端点（用于对比）
    ]
    
    # 模拟评估结果数据
    test_data = {
        'record_type': 'assessment',
        'assessment_id': 14,
        'assessment_title': '抑郁自评量表',
        'submitted_at': datetime.now().isoformat(),
        'answers': [
            {
                'question_id': 1,
                'question_text': '我感到情绪低落',
                'answer': '2'
            }
        ],
        'total_score': 2
    }
    
    logger.info(f"测试服务器: {base_url}")
    logger.info(f"测试用户ID: {custom_id}")
    
    for endpoint in endpoints:
        url = f"{base_url}/{endpoint}"
        logger.info(f"\n测试端点: {endpoint}")
        logger.info(f"完整URL: {url}")
        
        try:
            # 设置代理禁用
            proxies = {
                'http': None,
                'https': None
            }
            
            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'X-User-ID': custom_id
            }
            
            # 发送POST请求
            response = requests.post(
                url, 
                json=test_data,
                headers=headers,
                timeout=10, 
                proxies=proxies
            )
            
            logger.info(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"✓ {endpoint} 端点可用（提交成功）")
                try:
                    result = response.json()
                    logger.info(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                except:
                    logger.info(f"响应文本: {response.text[:200]}...")
            elif response.status_code == 201:
                logger.info(f"✓ {endpoint} 端点可用（创建成功）")
            elif response.status_code == 401:
                logger.info(f"? {endpoint} 端点存在但需要认证 (401)")
            elif response.status_code == 403:
                logger.info(f"? {endpoint} 端点存在但权限不足 (403)")
            elif response.status_code == 404:
                logger.error(f"✗ {endpoint} 端点不存在 (404)")
            elif response.status_code == 405:
                logger.warning(f"? {endpoint} 端点存在但不支持POST方法 (405)")
            elif response.status_code == 422:
                logger.warning(f"? {endpoint} 端点存在但数据格式错误 (422)")
                try:
                    error_detail = response.json()
                    logger.info(f"错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
                except:
                    logger.info(f"错误响应: {response.text[:200]}...")
            elif response.status_code == 500:
                logger.error(f"✗ {endpoint} 服务器内部错误 (500)")
            else:
                logger.warning(f"? {endpoint} 返回状态码: {response.status_code}")
                logger.info(f"响应内容: {response.text[:200]}...")
                
        except requests.exceptions.ConnectTimeout:
            logger.error(f"✗ {endpoint} 连接超时")
        except requests.exceptions.ConnectionError:
            logger.error(f"✗ {endpoint} 连接错误")
        except Exception as e:
            logger.error(f"✗ {endpoint} 请求失败: {str(e)}")

def test_get_endpoints():
    """测试GET端点（用于对比）"""
    base_url = "http://8.138.188.26:80/api"
    custom_id = "U_0002"
    
    get_endpoints = [
        "assessments",                       # 量表列表
        f"assessments/14",                   # 量表详情
        f"user-health-records/{custom_id}",  # 用户健康记录
    ]
    
    logger.info(f"\n=== 测试GET端点（用于对比） ===")
    
    for endpoint in get_endpoints:
        url = f"{base_url}/{endpoint}"
        logger.info(f"\n测试GET: {endpoint}")
        
        try:
            proxies = {
                'http': None,
                'https': None
            }
            
            headers = {
                'X-User-ID': custom_id
            }
            
            response = requests.get(url, headers=headers, timeout=10, proxies=proxies)
            logger.info(f"GET {endpoint} 状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"✓ GET {endpoint} 成功")
            elif response.status_code == 401:
                logger.info(f"? GET {endpoint} 需要认证")
            elif response.status_code == 404:
                logger.error(f"✗ GET {endpoint} 不存在")
            else:
                logger.warning(f"? GET {endpoint} 状态码: {response.status_code}")
                
        except Exception as e:
            logger.error(f"✗ GET {endpoint} 失败: {str(e)}")

def main():
    """主测试函数"""
    logger.info("开始测试评估结果提交API端点修复...")
    
    test_assessment_submission_endpoints()
    test_get_endpoints()
    
    logger.info("\n=== 测试总结 ===")
    logger.info("✅ 修复内容:")
    logger.info("1. 将提交端点从 'assessment-responses' 改为 'user-health-records/{custom_id}'")
    logger.info("2. 修改了数据格式，添加了 record_type, submitted_at, total_score 等字段")
    logger.info("3. 使用POST方法提交到用户健康记录端点")
    
    logger.info("\n📋 状态码含义:")
    logger.info("- 200/201: 提交成功")
    logger.info("- 401: 需要认证（端点存在）")
    logger.info("- 403: 权限不足（端点存在）")
    logger.info("- 404: 端点不存在")
    logger.info("- 405: 不支持POST方法")
    logger.info("- 422: 数据格式错误")
    logger.info("- 500: 服务器内部错误")
    
    logger.info("\n🔧 如果仍然404:")
    logger.info("1. 检查后端是否支持 user-health-records 端点")
    logger.info("2. 确认端点路径格式是否正确")
    logger.info("3. 查看后端API文档确认正确的提交端点")

if __name__ == "__main__":
    main()
