#!/usr/bin/env python3
"""
测试评估量表详情修复
"""

def test_assessment_detail_processing():
    """测试评估量表详情处理逻辑"""
    print("=== 测试评估量表详情处理逻辑 ===")
    
    # 模拟不同格式的量表详情响应
    test_cases = [
        {
            "name": "直接字典格式（量表详情对象）",
            "data": {
                "id": 15,
                "title": "焦虑自评量表",
                "description": "用于评估焦虑程度",
                "questions": [
                    {
                        "question_id": 1,
                        "question_text": "您是否感到紧张或焦虑？",
                        "options": [
                            {"label": "从不", "value": "1", "score": 1},
                            {"label": "有时", "value": "2", "score": 2},
                            {"label": "经常", "value": "3", "score": 3},
                            {"label": "总是", "value": "4", "score": 4}
                        ]
                    }
                ]
            }
        },
        {
            "name": "标准成功响应格式",
            "data": {
                "status": "success",
                "data": {
                    "id": 15,
                    "title": "焦虑自评量表",
                    "questions": [
                        {
                            "question_id": 1,
                            "question_text": "您是否感到紧张或焦虑？",
                            "options": [
                                {"label": "从不", "value": "1", "score": 1}
                            ]
                        }
                    ]
                }
            }
        },
        {
            "name": "列表格式（包含单个量表详情）",
            "data": [
                {
                    "id": 15,
                    "title": "焦虑自评量表",
                    "questions": [
                        {
                            "question_id": 1,
                            "question_text": "您是否感到紧张或焦虑？",
                            "options": [
                                {"label": "从不", "value": "1", "score": 1}
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "name": "空选项的题目",
            "data": {
                "id": 15,
                "title": "测试量表",
                "questions": [
                    {
                        "question_id": 1,
                        "question_text": "正常题目",
                        "options": [
                            {"label": "选项1", "value": "1", "score": 1}
                        ]
                    },
                    {
                        "question_id": 2,
                        "question_text": "空选项题目",
                        "options": None  # 这会导致 NoneType 错误
                    },
                    {
                        "question_id": 3,
                        "question_text": "空列表题目",
                        "options": []  # 这也会被跳过
                    }
                ]
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        raw_result = test_case['data']
        
        # 模拟修复后的响应处理逻辑
        try:
            # 检查数据格式，判断是否为字典（量表详情应该是单个对象）
            if isinstance(raw_result, dict):
                # 如果是字典，检查是否已经是标准格式
                if 'status' in raw_result or 'data' in raw_result:
                    result = raw_result
                    print("✓ 标准响应格式")
                else:
                    # 如果是普通字典（直接的量表详情），包装为标准格式
                    result = {
                        "status": "success",
                        "data": raw_result
                    }
                    print("✓ 直接字典格式，已包装为标准格式")
            elif isinstance(raw_result, list):
                # 如果是列表，取第一个元素作为详情
                if raw_result:
                    result = {
                        "status": "success",
                        "data": raw_result[0]
                    }
                    print("✓ 列表格式，取第一个元素")
                else:
                    result = {"status": "error", "message": "服务器返回空列表"}
                    print("✗ 空列表")
            else:
                # 其他情况，包装为标准格式
                result = {
                    "status": "success",
                    "data": raw_result
                }
                print("✓ 其他格式，已包装")
            
            # 模拟获取量表详情
            if result and result.get('status') == 'success':
                assessment_detail = result.get('data', {})
                
                if not assessment_detail:
                    print("✗ 获取到的量表详情为空")
                    continue
                
                # 模拟显示量表表单的逻辑
                questions = assessment_detail.get('questions', [])
                
                if not questions:
                    print("✗ 该量表没有题目")
                    continue
                
                print(f"✓ 量表标题: {assessment_detail.get('title', '未命名量表')}")
                print(f"✓ 题目数量: {len(questions)}")
                
                # 检查每个题目的选项
                valid_questions = 0
                for i, q in enumerate(questions):
                    question_text = q.get('question_text', '')
                    options = q.get('options', [])
                    
                    # 确保options不为None
                    if options is None:
                        options = []
                        print(f"  ⚠️ 题目 {i+1} 选项为None，已修复为空列表")
                    
                    # 如果没有选项，跳过这个题目
                    if not options:
                        print(f"  ⚠️ 题目 {i+1} 没有选项，将被跳过")
                        continue
                    
                    valid_questions += 1
                    print(f"  ✓ 题目 {i+1}: {question_text} ({len(options)} 个选项)")
                
                print(f"✓ 有效题目数量: {valid_questions}")
                
            else:
                error_msg = result.get('message', '未知错误') if result else '请求失败'
                print(f"✗ 处理失败: {error_msg}")
                
        except Exception as e:
            print(f"✗ 处理异常: {str(e)}")

def main():
    """主函数"""
    print("验证评估量表详情修复...")
    
    test_assessment_detail_processing()
    
    print("\n=== 修复总结 ===")
    print("✅ 已修复 'object of type 'NoneType' has no len()' 错误")
    print("✅ 现在可以正确处理以下情况:")
    print("   1. 直接返回的量表详情字典格式")
    print("   2. 标准的成功响应格式")
    print("   3. 列表格式的响应（取第一个元素）")
    print("   4. 题目选项为None的情况（自动修复为空列表）")
    print("   5. 题目选项为空列表的情况（跳过该题目）")
    
    print("\n=== 修复的关键点 ===")
    print("1. 增强了量表详情响应格式检测")
    print("2. 添加了对None选项的检查和修复")
    print("3. 跳过没有选项的题目，避免布局错误")
    print("4. 增加了详细的日志记录")
    
    print("\n=== 建议测试步骤 ===")
    print("1. 重启移动应用")
    print("2. 重新登录获取有效token")
    print("3. 进入评估量表页面")
    print("4. 点击任意评估量表")
    print("5. 检查是否能正常显示量表表单")
    print("6. 查看日志中的数据类型和处理信息")

if __name__ == "__main__":
    main()
