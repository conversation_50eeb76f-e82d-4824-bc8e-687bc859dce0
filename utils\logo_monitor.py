"""
Logo监控工具模块

这个模块提供了一些工具函数，用于监控应用中Logo的使用情况，
帮助开发者发现和解决重复Logo的问题。
"""
from kivy.clock import Clock
from kivy.app import App
from kivy.uix.widget import Widget
from kivy.uix.screenmanager import ScreenManager, Screen

def find_all_logos(root_widget=None):
    """查找所有Logo实例

    这个函数会递归遍历指定的根组件及其所有子组件，
    找出所有的HealthLogo实例。

    Args:
        root_widget: 要搜索的根组件，默认为应用的根组件

    Returns:
        list: 找到的所有HealthLogo实例列表
    """
    from widgets.logo import HealthLogo

    # 如果没有指定根组件，使用应用的根组件
    if root_widget is None:
        app = App.get_running_app()
        if not app or not hasattr(app, 'root'):
            return []
        root_widget = app.root

    # 存储找到的所有Logo实例
    logos = []

    # 递归查找函数
    def _find_logos(widget):
        # 检查当前组件是否为HealthLogo
        if isinstance(widget, HealthLogo):
            logos.append(widget)

        # 递归检查所有子组件
        if hasattr(widget, 'children'):
            for child in widget.children:
                _find_logos(child)

    # 开始递归查找
    _find_logos(root_widget)

    return logos

def analyze_logo_usage(dt=None):
    """分析Logo使用情况

    这个函数会分析应用中所有屏幕的Logo使用情况，
    并输出详细的报告。

    Args:
        dt: Clock调度触发的时间增量（可选）

    Returns:
        dict: Logo使用情况报告
    """
    # 获取应用实例
    app = App.get_running_app()
    if not app or not hasattr(app, 'root'):
        return {"error": "无法获取应用实例"}

    # 获取屏幕管理器
    screen_manager = app.root
    if not isinstance(screen_manager, ScreenManager):
        return {"error": "应用根组件不是ScreenManager"}

    # 获取Logo管理器
    from widgets.logo_manager import get_logo_manager
    logo_manager = get_logo_manager()

    # 先更新Logo的屏幕分配
    logo_manager.update_logo_screen_assignments()

    # 分析结果
    result = {
        "total_logos": 0,
        "screens": {},
        "duplicate_screens": [],
        "missing_screens": [],
        "orphaned_logos": 0
    }

    # 检查是否有无屏幕归属的Logo
    orphaned_logos = [logo for logo in logo_manager._logo_instances if not logo_manager._find_parent_screen(logo)]
    result["orphaned_logos"] = len(orphaned_logos)

    # 遍历所有屏幕
    for screen_name in screen_manager.screen_names:
        screen = screen_manager.get_screen(screen_name)
        if screen:
            # 获取屏幕类名
            screen_class_name = screen.__class__.__name__

            # 查找当前屏幕中的所有Logo
            all_logos = find_all_logos(screen)

            # 记录结果
            result["screens"][screen_name] = len(all_logos)
            result["total_logos"] += len(all_logos)

            # 检查是否有重复的Logo
            if len(all_logos) > 1:
                result["duplicate_screens"].append(screen_name)

            # 检查是否缺失Logo
            if len(all_logos) == 0:
                result["missing_screens"].append(screen_name)

    # 输出报告（仅在有问题时输出详细信息）
    import logging
    logger = logging.getLogger(__name__)

    # 检查是否有问题
    has_issues = (result["orphaned_logos"] > 0 or
                 result["duplicate_screens"] or
                 result["missing_screens"])

    if has_issues:
        # 如果有问题，输出详细报告
        logger.warning("\n===== Logo使用情况报告 =====")
        logger.warning(f"总Logo数量: {result['total_logos']}")

        if result["orphaned_logos"] > 0:
            logger.warning(f"无屏幕归属的Logo: {result['orphaned_logos']} 个")

        if result["duplicate_screens"]:
            logger.warning("存在重复Logo的屏幕:")
            for screen_name in result["duplicate_screens"]:
                logger.warning(f"  {screen_name}: {result['screens'][screen_name]}个Logo")

        if result["missing_screens"]:
            logger.warning("缺失Logo的屏幕:")
            for screen_name in result["missing_screens"]:
                logger.warning(f"  {screen_name}")

        logger.warning("===========================")
    elif logger.isEnabledFor(logging.DEBUG):
        # 如果没有问题，只在调试模式下输出简要报告
        logger.debug("\n===== Logo使用情况报告 =====")
        logger.debug(f"总Logo数量: {result['total_logos']}")
        logger.debug("所有屏幕的Logo都正常，很好！")
        logger.debug("===========================")

    # 如果有无屏幕归属的Logo，清理它们
    if result["orphaned_logos"] > 0:
        logger.info("清理无屏幕归属的Logo...")
        logo_manager.cleanup_duplicate_logos()

    # 尝试修复缺失的Logo
    if result["missing_screens"]:
        logger.info("尝试修复缺失的Logo...")
        from widgets.logo import HealthLogo
        from utils.ui_cleanup import cleanup_duplicate_logos

        # 清理并修复Logo
        cleanup_duplicate_logos()

        # 再次分析（但不递归调用，避免无限循环）
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("修复后将在下次调度时再次分析")

    return result

def schedule_logo_analysis(delay=15):
    """调度Logo使用情况分析任务

    这个函数会在指定的延迟后调度一个Logo使用情况分析任务。

    Args:
        delay: 延迟时间（秒）

    Returns:
        Event: Clock调度的事件
    """
    return Clock.schedule_once(analyze_logo_usage, delay)

# 不再在模块加载时自动调度Logo使用情况分析任务，而是由主程序显式调用
# schedule_logo_analysis(15)
