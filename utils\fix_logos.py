"""
Logo修复工具

这个模块提供了一些工具函数，用于修复应用中缺失的Logo。
"""
from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.clock import Clock

def fix_missing_logos(dt=None):
    """修复应用中缺失的Logo并确保每个屏幕只有一个Logo

    这个函数会遍历应用中的所有屏幕，并为缺失Logo的屏幕添加Logo，
    同时确保每个屏幕只有一个Logo。

    Args:
        dt: Clock调度触发的时间增量（可选）

    Returns:
        tuple: (添加的Logo数量, 移除的重复Logo数量)
    """
    from widgets.logo_manager import get_logo_manager
    from widgets.logo import HealthLogo
    logo_manager = get_logo_manager()

    # 获取应用实例
    app = App.get_running_app()
    if not app or not hasattr(app, 'root'):
        return (0, 0)

    # 获取屏幕管理器
    screen_manager = app.root
    if not isinstance(screen_manager, ScreenManager):
        return (0, 0)

    # 先清理重复的Logo
    removed_count = logo_manager.cleanup_duplicate_logos()

    # 检查所有屏幕中的Logo数量
    from utils.logo_monitor import find_all_logos
    screen_logo_counts = {}

    for screen_name in screen_manager.screen_names:
        screen = screen_manager.get_screen(screen_name)
        if screen:
            # 查找当前屏幕中的所有Logo
            logos = find_all_logos(screen)
            screen_logo_counts[screen_name] = len(logos)

    # 只在调试模式下输出当前屏幕的Logo数量
    import logging
    logger = logging.getLogger(__name__)
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug("清理后的屏幕Logo数量:")
        for screen_name, logo_count in screen_logo_counts.items():
            logger.debug(f"  {screen_name}: {logo_count}")

    # 遍历所有屏幕，为缺失Logo的屏幕添加Logo
    added_count = 0
    for screen_name in screen_manager.screen_names:
        screen = screen_manager.get_screen(screen_name)
        if screen and screen_logo_counts.get(screen_name, 0) == 0:
            # 尝试在屏幕中找到合适的容器来添加Logo
            container = find_logo_container(screen)
            if container:
                # 创建并添加Logo
                logo = HealthLogo()
                container.add_widget(logo)

                # 设置Logo属性
                logo.size_hint = (None, None)
                logo.size = (150, 75)  # 设置适当的大小
                logo.pos_hint = {"center_x": 0.5, "top": 1}

                # 注册Logo
                logo_manager.register_logo(logo)

                added_count += 1
                logger.debug(f"已为屏幕 {screen_name} 添加Logo")
        elif screen and screen_logo_counts.get(screen_name, 0) > 1:
            # 如果屏幕有多个Logo，再次清理
            # 查找当前屏幕中的所有Logo
            logos = find_all_logos(screen)
            if len(logos) > 1:
                # 保留第一个Logo，移除其他的
                for logo in logos[1:]:
                    if logo.parent:
                        logo.parent.remove_widget(logo)
                        removed_count += 1
                logger.debug(f"已再次清理屏幕 {screen_name} 中的 {len(logos)-1} 个重复Logo")

    if added_count > 0 or removed_count > 0:
        logger.info(f"Logo修复: 添加了{added_count}个，清理了{removed_count}个")

        # 最后再次清理可能的重复Logo
        logo_manager.cleanup_duplicate_logos()

        # 再次检查所有屏幕中的Logo数量（仅在调试模式下）
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("修复后的屏幕Logo数量:")
            for screen_name in screen_manager.screen_names:
                screen = screen_manager.get_screen(screen_name)
                if screen:
                    logos = find_all_logos(screen)
                    logger.debug(f"  {screen_name}: {len(logos)}")
    else:
        logger.debug("所有屏幕都已有一个Logo，无需添加或清理")

    return (added_count, removed_count)

def find_logo_container(screen):
    """在屏幕中查找合适的Logo容器

    这个函数会尝试在屏幕中查找名为logo_container的容器，
    如果找不到，则尝试查找其他合适的容器。

    Args:
        screen: 要查找的屏幕

    Returns:
        Widget: 找到的容器，如果找不到则返回屏幕本身
    """
    # 尝试查找名为logo_container的容器
    if hasattr(screen, 'ids') and 'logo_container' in screen.ids:
        return screen.ids.logo_container

    # 尝试查找其他可能的容器名称
    possible_container_names = [
        'logo_container', 'brand_layout', 'header', 'top_layout',
        'title_bar', 'app_bar', 'toolbar', 'main_layout', 'content_layout',
        'top_bar', 'header_layout', 'title_layout', 'top_section'
    ]

    for name in possible_container_names:
        if hasattr(screen, 'ids') and name in screen.ids:
            return screen.ids[name]

    # 如果找不到特定名称的容器，尝试查找第一个BoxLayout或MDBoxLayout
    from kivy.uix.boxlayout import BoxLayout
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.card import MDCard

    # 检查是否有带有"logo"或"brand"标识的布局
    for child in screen.walk():
        if hasattr(child, 'id') and child.id and ('logo' in str(child.id).lower() or 'brand' in str(child.id).lower()):
            # 找到了带有logo或brand标识的容器
            return child

    # 首先尝试查找顶部容器
    for child in screen.walk():
        if isinstance(child, (BoxLayout, MDBoxLayout)) and child != screen:
            # 检查是否是顶部容器
            if hasattr(child, 'pos_hint') and child.pos_hint and ('top' in child.pos_hint or 'y' in child.pos_hint and child.pos_hint.get('y', 0) > 0.8):
                return child

    # 尝试查找垂直布局的第一个子元素
    for child in screen.walk():
        if isinstance(child, (BoxLayout, MDBoxLayout)) and child != screen:
            if hasattr(child, 'orientation') and child.orientation == 'vertical':
                if hasattr(child, 'children') and child.children:
                    # 返回垂直布局本身，而不是它的子元素
                    return child

    # 尝试查找卡片容器
    for child in screen.walk():
        if isinstance(child, MDCard) and child != screen:
            return child

    # 如果还是找不到，就返回任何布局容器
    for child in screen.walk():
        if isinstance(child, (BoxLayout, MDBoxLayout)) and child != screen:
            return child

    # 如果找不到合适的容器，返回屏幕本身
    print(f"在屏幕 {screen.__class__.__name__} 中找不到合适的Logo容器，使用屏幕本身")
    return screen

def schedule_logo_fix(delay=20):
    """调度Logo修复任务

    这个函数会在指定的延迟后调度一个Logo修复任务。

    Args:
        delay: 延迟时间（秒）

    Returns:
        Event: Clock调度的事件
    """
    return Clock.schedule_once(fix_missing_logos, delay)

# 不再在模块加载时自动调度Logo修复任务，而是由主程序显式调用
# schedule_logo_fix(20)

def fix_register_screen_logo(register_screen):
    """专门用于修复RegisterScreen上的Logo问题

    这个方法现在使用RegisterScreen的_add_logo方法来添加Logo，
    确保与RegisterScreen的实现保持一致。

    Args:
        register_screen: RegisterScreen实例

    Returns:
        bool: 如果成功修复或已有Logo，返回True；否则返回False
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        # 检查RegisterScreen是否有_add_logo方法
        if hasattr(register_screen, '_add_logo'):
            # 使用RegisterScreen自己的方法添加Logo
            register_screen._add_logo(None)
            register_screen.logo_added = True
            logger.debug("使用RegisterScreen的_add_logo方法添加Logo")
            return True
        else:
            logger.warning("RegisterScreen没有_add_logo方法，无法修复Logo")
            return False
    except Exception as e:
        logger.error(f"修复RegisterScreen Logo出错: {str(e)}")
        return False
