2025-04-13 23:14:40,068 - kivy - INFO - deps: Successfully imported "kivy_deps.angle" 0.4.0
2025-04-13 23:14:40,073 - kivy - INFO - Logger: Record log in C:\Users\<USER>\.kivy\logs\kivy_25-04-13_43.txt
2025-04-13 23:14:40,079 - kivy - INFO - deps: Successfully imported "kivy_deps.glew" 0.3.1
2025-04-13 23:14:40,081 - kivy - INFO - deps: Successfully imported "kivy_deps.sdl2" 0.8.0
2025-04-13 23:14:40,087 - kivy - INFO - Kivy: v2.3.1
2025-04-13 23:14:40,088 - kivy - INFO - Kivy: Installed at "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\kivy\__init__.py"
2025-04-13 23:14:40,089 - kivy - INFO - Python: v3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-04-13 23:14:40,092 - kivy - INFO - Python: Interpreter at "C:\Users\<USER>\Desktop\health-Trea\myenv\Scripts\python.exe"
2025-04-13 23:14:40,099 - kivy - INFO - Logger: Purge log fired. Processing...
2025-04-13 23:14:40,123 - kivy - INFO - Logger: Purge finished!
2025-04-13 23:14:40,388 - kivy - INFO - Factory: 195 symbols loaded
2025-04-13 23:14:40,496 - kivy - INFO - Image: Providers: img_tex, img_dds, img_sdl2, img_pil (img_ffpyplayer ignored)
2025-04-13 23:14:40,758 - kivy - INFO - Window: Provider: sdl2
2025-04-13 23:14:41,173 - kivy - INFO - GL: Using the "OpenGL" graphics system
2025-04-13 23:14:41,175 - kivy - INFO - GL: GLEW initialization succeeded
2025-04-13 23:14:41,175 - kivy - INFO - GL: Backend used <glew>
2025-04-13 23:14:41,176 - kivy - INFO - GL: OpenGL version <b'4.6.0 - Build 30.0.100.9865'>
2025-04-13 23:14:41,177 - kivy - INFO - GL: OpenGL vendor <b'Intel'>
2025-04-13 23:14:41,178 - kivy - INFO - GL: OpenGL renderer <b'Intel(R) HD Graphics 520'>
2025-04-13 23:14:41,180 - kivy - INFO - GL: OpenGL parsed version: 4, 6
2025-04-13 23:14:41,181 - kivy - INFO - GL: Shading version <b'4.60 - Build 30.0.100.9865'>
2025-04-13 23:14:41,182 - kivy - INFO - GL: Texture max size <16384>
2025-04-13 23:14:41,183 - kivy - INFO - GL: Texture max units <32>
2025-04-13 23:14:41,228 - kivy - INFO - Window: auto add sdl2 input provider
2025-04-13 23:14:41,231 - kivy - INFO - Window: virtual keyboard not allowed, single mode, not docked
2025-04-13 23:14:41,235 - kivy - INFO - KivyMD: 2.0.1.dev0, git-Unknown, 2025-03-17 (installed at "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\kivymd\__init__.py")
2025-04-13 23:14:41,248 - kivy - INFO - Text: Provider: sdl2
2025-04-13 23:14:41,573 - kivy - INFO - Camera: Provider: opencv(['camera_picamera', 'camera_gi'] ignored)
2025-04-13 23:14:41,585 - kivy - INFO - Clipboard: Provider: winctypes
2025-04-13 23:14:41,658 - __main__ - INFO - 检测到KivyMD版本: 2.0.1.dev0
2025-04-13 23:14:41,662 - __main__ - INFO - 检测到KivyMD 2.0系列版本，应用相应补丁
2025-04-13 23:14:41,663 - __main__ - INFO - 检测到开发版本，可能需要特殊处理
2025-04-13 23:14:41,663 - __main__ - INFO - 为MDLabel添加padding_x参数支持
2025-04-13 23:14:41,664 - __main__ - INFO - MDLabel padding_x补丁已应用
2025-04-13 23:14:41,665 - __main__ - INFO - KivyMD兼容性补丁应用完成
2025-04-13 23:14:43,602 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:14:43,603 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:14:43,604 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:14:43,605 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:14:43,607 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:14:43,740 - kivy - INFO - GL: NPOT texture support is available
2025-04-13 23:14:44,734 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:14:44,735 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:14:44,736 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:14:44,738 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:14:44,740 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:14:44,972 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:14:44,973 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:14:44,974 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:14:44,976 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:14:44,979 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:14:45,574 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:14:45,575 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:14:45,576 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:14:45,578 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:14:45,581 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:14:45,653 - kivy - ERROR - Image: Not found <mobile/assets/avatars/avatar3.png>
2025-04-13 23:14:46,048 - kivy - INFO - Base: Start application main loop
2025-04-13 23:14:49,637 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:14:49,640 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:14:49,642 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:14:50,831 - cloud_api - WARNING - 服务器健康状态异常: HTTP 502
2025-04-13 23:14:50,833 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:14:50,835 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:14:51,047 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:14:51,049 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:14:51,050 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:14:51,051 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:14:51,052 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:14:51,053 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:14:51,055 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:14:51,636 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:14:51,638 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:14:51,639 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:14:57,060 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:14:57,063 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:14:57,065 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:15:02,616 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:15:02,617 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:15:02,618 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:15:02,619 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:15:02,622 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:15:02,623 - api.api_client - INFO - 尝试用户登录: mjj
2025-04-13 23:15:02,624 - cloud_api - INFO - 发送 POST 请求 : http://************:8088/api/auth/login
2025-04-13 23:15:07,652 - cloud_api - WARNING - API网关错误 : 502 Bad Gateway
2025-04-13 23:15:07,655 - cloud_api - INFO - 将在 2 秒后重试请求
2025-04-13 23:15:08,625 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:15:08,629 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:15:08,632 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:15:09,657 - cloud_api - INFO - 发送 POST 请求 (重试 1/3): http://************:8088/api/auth/login
2025-04-13 23:15:14,680 - cloud_api - WARNING - API网关错误 (重试 1/3): 502 Bad Gateway
2025-04-13 23:15:14,683 - cloud_api - INFO - 将在 4 秒后重试请求
2025-04-13 23:15:18,687 - cloud_api - INFO - 发送 POST 请求 (重试 2/3): http://************:8088/api/auth/login
2025-04-13 23:15:23,719 - cloud_api - WARNING - API网关错误 (重试 2/3): 502 Bad Gateway
2025-04-13 23:15:23,729 - cloud_api - INFO - 将在 8 秒后重试请求
2025-04-13 23:15:31,731 - cloud_api - INFO - 发送 POST 请求 (重试 3/3): http://************:8088/api/auth/login
2025-04-13 23:15:36,751 - cloud_api - WARNING - API网关错误 (重试 3/3): 502 Bad Gateway
2025-04-13 23:15:36,755 - cloud_api - ERROR - API请求失败，已达到最大重试次数: 3
2025-04-13 23:15:36,758 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 1800秒
2025-04-13 23:15:36,761 - cloud_api - WARNING - 认证失败，用户名或密码错误: mjj
2025-04-13 23:15:36,763 - api.api_client - INFO - 云端认证失败: 网关错误(502)，服务器暂时不可用
2025-04-13 23:15:36,766 - api.api_client - INFO - 回退到本地认证...
2025-04-13 23:15:36,769 - api.api_client - INFO - 本地账户数量: 9
2025-04-13 23:15:36,773 - api.api_client - ERROR - 本地认证过程发生异常: 'UserManager' object has no attribute 'authenticate'
2025-04-13 23:15:40,475 - kivy - INFO - Base: Leaving application in progress...
2025-04-13 23:44:56,187 - kivy - INFO - deps: Successfully imported "kivy_deps.angle" 0.4.0
2025-04-13 23:44:56,196 - kivy - INFO - Logger: Record log in C:\Users\<USER>\.kivy\logs\kivy_25-04-13_45.txt
2025-04-13 23:44:56,198 - kivy - INFO - deps: Successfully imported "kivy_deps.glew" 0.3.1
2025-04-13 23:44:56,199 - kivy - INFO - deps: Successfully imported "kivy_deps.sdl2" 0.8.0
2025-04-13 23:44:56,200 - kivy - INFO - Kivy: v2.3.1
2025-04-13 23:44:56,201 - kivy - INFO - Kivy: Installed at "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\kivy\__init__.py"
2025-04-13 23:44:56,204 - kivy - INFO - Python: v3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-04-13 23:44:56,206 - kivy - INFO - Python: Interpreter at "C:\Users\<USER>\Desktop\health-Trea\myenv\Scripts\python.exe"
2025-04-13 23:44:56,211 - kivy - INFO - Logger: Purge log fired. Processing...
2025-04-13 23:44:56,235 - kivy - INFO - Logger: Purge finished!
2025-04-13 23:44:56,374 - kivy - INFO - Factory: 195 symbols loaded
2025-04-13 23:44:56,483 - kivy - INFO - Image: Providers: img_tex, img_dds, img_sdl2, img_pil (img_ffpyplayer ignored)
2025-04-13 23:44:56,733 - kivy - INFO - Window: Provider: sdl2
2025-04-13 23:44:57,232 - kivy - INFO - GL: Using the "OpenGL" graphics system
2025-04-13 23:44:57,234 - kivy - INFO - GL: GLEW initialization succeeded
2025-04-13 23:44:57,235 - kivy - INFO - GL: Backend used <glew>
2025-04-13 23:44:57,235 - kivy - INFO - GL: OpenGL version <b'4.6.0 - Build 30.0.100.9865'>
2025-04-13 23:44:57,236 - kivy - INFO - GL: OpenGL vendor <b'Intel'>
2025-04-13 23:44:57,237 - kivy - INFO - GL: OpenGL renderer <b'Intel(R) HD Graphics 520'>
2025-04-13 23:44:57,238 - kivy - INFO - GL: OpenGL parsed version: 4, 6
2025-04-13 23:44:57,238 - kivy - INFO - GL: Shading version <b'4.60 - Build 30.0.100.9865'>
2025-04-13 23:44:57,239 - kivy - INFO - GL: Texture max size <16384>
2025-04-13 23:44:57,240 - kivy - INFO - GL: Texture max units <32>
2025-04-13 23:44:57,287 - kivy - INFO - Window: auto add sdl2 input provider
2025-04-13 23:44:57,290 - kivy - INFO - Window: virtual keyboard not allowed, single mode, not docked
2025-04-13 23:44:57,297 - kivy - INFO - KivyMD: 2.0.1.dev0, git-Unknown, 2025-03-17 (installed at "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\kivymd\__init__.py")
2025-04-13 23:44:57,311 - kivy - INFO - Text: Provider: sdl2
2025-04-13 23:44:57,647 - kivy - INFO - Camera: Provider: opencv(['camera_picamera', 'camera_gi'] ignored)
2025-04-13 23:44:57,663 - kivy - INFO - Clipboard: Provider: winctypes
2025-04-13 23:44:57,759 - __main__ - INFO - 检测到KivyMD版本: 2.0.1.dev0
2025-04-13 23:44:57,762 - __main__ - INFO - 检测到KivyMD 2.0系列版本，应用相应补丁
2025-04-13 23:44:57,763 - __main__ - INFO - 检测到开发版本，可能需要特殊处理
2025-04-13 23:44:57,764 - __main__ - INFO - 为MDLabel添加padding_x参数支持
2025-04-13 23:44:57,765 - __main__ - INFO - MDLabel padding_x补丁已应用
2025-04-13 23:44:57,766 - __main__ - INFO - KivyMD兼容性补丁应用完成
2025-04-13 23:44:59,406 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:44:59,409 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:44:59,410 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:44:59,413 - cloud_api - INFO - 检测到系统代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890', 'ftp': 'http://127.0.0.1:7890'}
2025-04-13 23:44:59,415 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:45:04,421 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:45:04,423 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:45:04,424 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:45:04,429 - cloud_api - DEBUG - 显示降级模式通知失败: Win platform does not support Android Toast
2025-04-13 23:45:04,430 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:45:04,514 - kivy - INFO - GL: NPOT texture support is available
2025-04-13 23:45:05,419 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:45:05,421 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:45:05,422 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:45:05,425 - cloud_api - INFO - 检测到系统代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890', 'ftp': 'http://127.0.0.1:7890'}
2025-04-13 23:45:05,427 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:45:10,432 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:45:10,434 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:45:10,436 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:45:10,442 - cloud_api - DEBUG - 显示降级模式通知失败: Win platform does not support Android Toast
2025-04-13 23:45:10,444 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:45:10,692 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:45:10,693 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:45:10,694 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:45:10,696 - cloud_api - INFO - 检测到系统代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890', 'ftp': 'http://127.0.0.1:7890'}
2025-04-13 23:45:10,698 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:45:15,702 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:45:15,703 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:45:15,704 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:45:15,707 - cloud_api - DEBUG - 显示降级模式通知失败: Win platform does not support Android Toast
2025-04-13 23:45:15,708 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:45:16,347 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:45:16,349 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:45:16,349 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:45:16,351 - cloud_api - INFO - 检测到系统代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890', 'ftp': 'http://127.0.0.1:7890'}
2025-04-13 23:45:16,352 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:45:21,355 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:45:21,357 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:45:21,358 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:45:21,362 - cloud_api - DEBUG - 显示降级模式通知失败: Win platform does not support Android Toast
2025-04-13 23:45:21,364 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:45:21,421 - kivy - ERROR - Image: Not found <mobile/assets/avatars/avatar3.png>
2025-04-13 23:45:21,893 - kivy - INFO - Base: Start application main loop
2025-04-13 23:45:26,894 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:45:26,898 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:45:26,900 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:45:26,906 - cloud_api - INFO - 检测到系统代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890', 'ftp': 'http://127.0.0.1:7890'}
2025-04-13 23:45:26,911 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:45:27,063 - cloud_api - INFO - 初始化API客户端，基础URL: http://************:8088/api
2025-04-13 23:45:27,064 - cloud_api - INFO - 已从本地文件加载认证信息
2025-04-13 23:45:27,065 - cloud_api - INFO - 文件上传超时设置: 60秒
2025-04-13 23:45:27,067 - cloud_api - INFO - 检测到系统代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890', 'ftp': 'http://127.0.0.1:7890'}
2025-04-13 23:45:27,069 - cloud_api - INFO - 已启动服务器健康检查
2025-04-13 23:45:31,919 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:45:31,922 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:45:31,923 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:45:31,927 - cloud_api - DEBUG - 显示降级模式通知失败: Win platform does not support Android Toast
2025-04-13 23:45:31,937 - kivy - WARNING - stderr: Traceback (most recent call last):
2025-04-13 23:45:31,939 - kivy - WARNING - stderr:   File "C:\Users\<USER>\Desktop\health-Trea\mobile\main.py", line 272, in process_register_queue
2025-04-13 23:45:31,940 - kivy - WARNING - stderr:     queue_size = cloud_api.get_register_queue_size()
2025-04-13 23:45:31,941 - kivy - WARNING - stderr:                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-04-13 23:45:31,942 - kivy - WARNING - stderr: AttributeError: 'CloudAPI' object has no attribute 'get_register_queue_size'
2025-04-13 23:45:32,073 - cloud_api - WARNING - 服务器健康检查失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-13 23:45:32,076 - cloud_api - WARNING - 初始服务器健康检查失败，启用降级模式
2025-04-13 23:45:32,077 - cloud_api - WARNING - 进入服务器降级模式，持续时间: 600秒
2025-04-13 23:45:32,082 - cloud_api - DEBUG - 显示降级模式通知失败: Win platform does not support Android Toast
2025-04-13 23:45:32,084 - api.api_client - INFO - 已启动服务器健康检查
2025-04-13 23:45:32,086 - api.api_client - INFO - 尝试用户登录: mjj
2025-04-13 23:45:32,088 - api.api_client - INFO - 云端处于降级模式，直接使用本地认证: mjj
2025-04-13 23:45:32,090 - api.api_client - INFO - 本地账户数量: 9
2025-04-13 23:45:32,122 - api.api_client - INFO - 本地认证成功，找到匹配的账户: mjj
2025-04-13 23:45:32,147 - kivy - WARNING - stderr: Traceback (most recent call last):
2025-04-13 23:45:32,150 - kivy - WARNING - stderr:   File "C:\Users\<USER>\Desktop\health-Trea\mobile\utils\user_manager.py", line 184, in save_accounts
2025-04-13 23:45:32,151 - kivy - WARNING - stderr:     "user_id": self.current_user.user_id,
2025-04-13 23:45:32,152 - kivy - WARNING - stderr:                ^^^^^^^^^^^^^^^^^^^^^^^^^
2025-04-13 23:45:32,154 - kivy - WARNING - stderr: AttributeError: 'str' object has no attribute 'user_id'
2025-04-13 23:45:32,158 - kivy - WARNING - stderr: Traceback (most recent call last):
2025-04-13 23:45:32,161 - kivy - WARNING - stderr:   File "C:\Users\<USER>\Desktop\health-Trea\mobile\utils\user_manager.py", line 835, in set_current_user
2025-04-13 23:45:32,162 - kivy - WARNING - stderr:     print(f"已设置当前用户: {user_account.username}, ID: {user_account.user_id}")
2025-04-13 23:45:32,163 - kivy - WARNING - stderr:                              ^^^^^^^^^^^^^^^^^^^^^
2025-04-13 23:45:32,164 - kivy - WARNING - stderr: AttributeError: 'str' object has no attribute 'username'
2025-04-13 23:45:32,165 - api.api_client - ERROR - 本地认证过程发生异常: 'UserAccount' object has no attribute 'get'
2025-04-13 23:45:37,262 - kivy - INFO - Base: Leaving application in progress...
2025-04-24 21:34:08,187 - kivy - INFO - deps: Successfully imported "kivy_deps.angle" 0.4.0
2025-04-24 21:34:08,190 - kivy - INFO - Logger: Record log in C:\Users\<USER>\.kivy\logs\kivy_25-04-24_2.txt
2025-04-24 21:34:08,193 - kivy - INFO - deps: Successfully imported "kivy_deps.glew" 0.3.1
2025-04-24 21:34:08,194 - kivy - INFO - deps: Successfully imported "kivy_deps.sdl2" 0.8.0
2025-04-24 21:34:08,195 - kivy - INFO - Kivy: v2.3.1
2025-04-24 21:34:08,196 - kivy - INFO - Kivy: Installed at "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\kivy\__init__.py"
2025-04-24 21:34:08,200 - kivy - INFO - Python: v3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-04-24 21:34:08,205 - kivy - INFO - Python: Interpreter at "C:\Users\<USER>\Desktop\health-Trea\myenv\Scripts\python.exe"
2025-04-24 21:34:08,217 - kivy - INFO - Logger: Purge log fired. Processing...
2025-04-24 21:34:08,256 - kivy - INFO - Logger: Purge finished!
2025-04-24 21:34:08,432 - kivy - INFO - Factory: 195 symbols loaded
2025-04-24 21:34:08,551 - kivy - INFO - Image: Providers: img_tex, img_dds, img_sdl2, img_pil (img_ffpyplayer ignored)
2025-04-24 21:34:08,845 - kivy - INFO - Window: Provider: sdl2
2025-04-24 21:34:09,448 - kivy - INFO - GL: Using the "OpenGL" graphics system
2025-04-24 21:34:09,452 - kivy - INFO - GL: GLEW initialization succeeded
2025-04-24 21:34:09,454 - kivy - INFO - GL: Backend used <glew>
2025-04-24 21:34:09,455 - kivy - INFO - GL: OpenGL version <b'4.6.0 - Build 30.0.100.9865'>
2025-04-24 21:34:09,457 - kivy - INFO - GL: OpenGL vendor <b'Intel'>
2025-04-24 21:34:09,468 - kivy - INFO - GL: OpenGL renderer <b'Intel(R) HD Graphics 520'>
2025-04-24 21:34:09,472 - kivy - INFO - GL: OpenGL parsed version: 4, 6
2025-04-24 21:34:09,479 - kivy - INFO - GL: Shading version <b'4.60 - Build 30.0.100.9865'>
2025-04-24 21:34:09,483 - kivy - INFO - GL: Texture max size <16384>
2025-04-24 21:34:09,484 - kivy - INFO - GL: Texture max units <32>
2025-04-24 21:34:09,537 - kivy - INFO - Window: auto add sdl2 input provider
2025-04-24 21:34:09,540 - kivy - INFO - Window: virtual keyboard not allowed, single mode, not docked
2025-04-24 21:34:09,549 - kivy - INFO - KivyMD: 2.0.1.dev0, git-Unknown, 2025-03-17 (installed at "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\kivymd\__init__.py")
2025-04-24 21:34:09,564 - kivy - INFO - Text: Provider: sdl2
2025-04-24 21:34:10,048 - kivy - INFO - Camera: Provider: opencv(['camera_picamera', 'camera_gi'] ignored)
2025-04-24 21:34:10,182 - kivy - INFO - Clipboard: Provider: winctypes
2025-04-24 21:34:10,288 - __main__ - INFO - 检测到KivyMD版本: 2.0.1.dev0
2025-04-24 21:34:10,313 - __main__ - INFO - 检测到KivyMD 2.0系列版本，应用相应补丁
2025-04-24 21:34:10,317 - __main__ - INFO - 检测到开发版本，可能需要特殊处理
2025-04-24 21:34:10,320 - __main__ - INFO - 为MDLabel添加padding_x参数支持
2025-04-24 21:34:10,323 - __main__ - INFO - MDLabel padding_x补丁已应用
2025-04-24 21:34:10,328 - __main__ - INFO - KivyMD兼容性补丁应用完成
2025-04-24 21:34:11,684 - utils.cloud_api - INFO - 已加载认证信息
2025-04-24 21:34:11,685 - utils.cloud_api - INFO - 初始化API客户端，基础URL: http://************:80/api
2025-04-24 21:34:11,686 - utils.cloud_api - INFO - 文件上传超时设置: 30秒
2025-04-24 21:34:11,689 - api.api_client - INFO - 已启动服务器健康检查
2025-04-24 21:34:11,801 - kivy - INFO - GL: NPOT texture support is available
2025-04-24 21:34:12,663 - api.api_client - INFO - 已启动服务器健康检查
2025-04-24 21:34:14,071 - api.api_client - INFO - 已启动服务器健康检查
2025-04-24 21:34:14,167 - kivy - ERROR - Image: Not found <mobile/assets/avatars/avatar3.png>
2025-04-24 21:34:14,591 - kivy - INFO - Base: Start application main loop
2025-04-24 21:34:19,598 - mobile.utils.cloud_api - INFO - 已加载认证信息
2025-04-24 21:34:19,615 - mobile.utils.cloud_api - INFO - 初始化API客户端，基础URL: http://************/api
2025-04-24 21:34:19,617 - mobile.utils.cloud_api - INFO - 文件上传超时设置: 30秒
2025-04-24 21:34:19,619 - __main__ - DEBUG - 用户未登录，跳过注册队列处理
2025-04-24 21:34:20,726 - api.api_client - INFO - 已启动服务器健康检查
2025-04-24 21:34:20,729 - api.api_client - INFO - 尝试用户登录: markey34
2025-04-24 21:34:20,790 - api.api_client - WARNING - 服务器离线，直接使用本地认证
2025-04-24 21:34:20,801 - api.api_client - INFO - 本地账户数量: 43
2025-04-24 21:34:21,002 - api.api_client - INFO - 本地认证成功，找到匹配的账户: markey34
2025-04-24 21:34:21,049 - utils.cloud_api - ERROR - 未登录，无法获取用户信息
2025-04-24 21:34:21,050 - api.api_client - ERROR - 获取用户数据失败: None
2025-04-24 21:34:32,514 - kivy - INFO - Base: Leaving application in progress...
