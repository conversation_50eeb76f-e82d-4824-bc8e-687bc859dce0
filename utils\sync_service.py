import json
import logging
import threading
import time
import queue
from datetime import datetime, timedelta
from .db_service import BaseDBService

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('sync_service')

class SyncManager:
    """数据同步管理器"""
    
    def __init__(self, cloud_api=None):
        """初始化同步管理器"""
        self.cloud_api = cloud_api
        self.sync_queue = queue.Queue()
        self.is_running = False
        self.sync_thread = None
        self.sync_interval = 300  # 默认5分钟同步一次
        self.last_sync_time = None
        self.current_user_id = None
        self.db_service = None
    
    def set_cloud_api(self, cloud_api):
        """设置云端API"""
        self.cloud_api = cloud_api
    
    def set_sync_interval(self, seconds):
        """设置同步间隔"""
        self.sync_interval = max(60, seconds)  # 最小1分钟
    
    def set_user_id(self, user_id):
        """设置当前用户ID"""
        if user_id != self.current_user_id:
            self.current_user_id = user_id
            if self.db_service:
                self.db_service.disconnect()
            self.db_service = BaseDBService(user_id)
    
    def start_sync(self):
        """启动同步线程"""
        if self.is_running:
            logger.warning("同步服务已经在运行中")
            return False
        
        self.is_running = True
        self.sync_thread = threading.Thread(target=self._sync_worker, daemon=True)
        self.sync_thread.start()
        logger.info("同步服务已启动")
        return True
    
    def stop_sync(self):
        """停止同步线程"""
        if not self.is_running:
            logger.warning("同步服务未在运行")
            return False
        
        self.is_running = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
            self.sync_thread = None
        
        logger.info("同步服务已停止")
        return True
    
    def queue_sync_task(self, user_id, table_name, record_id, priority=0):
        """将同步任务加入队列"""
        task = {
            'user_id': user_id,
            'table_name': table_name,
            'record_id': record_id,
            'priority': priority,
            'queued_time': datetime.now().isoformat()
        }
        self.sync_queue.put((priority, task))
        logger.debug(f"已将同步任务加入队列: {task}")
    
    def sync_now(self, user_id=None):
        """立即执行同步"""
        if not user_id and not self.current_user_id:
            logger.error("同步失败: 未指定用户ID")
            return False
        
        user_id = user_id or self.current_user_id
        
        if not self.cloud_api:
            logger.error("同步失败: 未设置云端API")
            return False
        
        try:
            # 如果当前服务的用户与请求同步的用户不同，创建新的数据库服务
            if not self.db_service or self.db_service.user_id != user_id:
                db_service = BaseDBService(user_id)
            else:
                db_service = self.db_service
            
            # 获取待同步记录
            pending_syncs = db_service.get_pending_syncs(limit=100)
            if not pending_syncs:
                logger.info(f"用户 {user_id} 没有待同步的记录")
                return True
            
            logger.info(f"开始同步用户 {user_id} 的 {len(pending_syncs)} 条记录")
            
            for sync_record in pending_syncs:
                self._sync_record(db_service, sync_record)
            
            self.last_sync_time = datetime.now()
            logger.info(f"用户 {user_id} 的同步完成")
            return True
            
        except Exception as e:
            logger.error(f"同步用户 {user_id} 数据时出错: {str(e)}")
            return False
    
    def _sync_worker(self):
        """同步工作线程"""
        while self.is_running:
            try:
                # 处理队列中的高优先级任务
                while not self.sync_queue.empty():
                    _, task = self.sync_queue.get(block=False)
                    try:
                        user_id = task['user_id']
                        table_name = task['table_name']
                        record_id = task['record_id']
                        
                        # 处理单个记录同步
                        db_service = None
                        if user_id != self.current_user_id:
                            db_service = BaseDBService(user_id)
                        else:
                            db_service = self.db_service
                        
                        # 确保db_service不为None
                        if db_service:
                            # 获取同步记录
                            sync_records = db_service.get_records('sync_record', {
                                'table_name': table_name, 
                                'record_id': record_id
                            })
                            
                            if sync_records:
                                self._sync_record(db_service, sync_records[0])
                        else:
                            logger.error(f"数据库服务为空，无法同步记录: {table_name}/{record_id}")
                        
                        self.sync_queue.task_done()
                    except Exception as e:
                        logger.error(f"处理同步任务出错: {str(e)}")
                        self.sync_queue.task_done()
                
                # 检查是否需要执行定期同步
                if self.current_user_id and self.cloud_api:
                    now = datetime.now()
                    if (not self.last_sync_time or 
                        (now - self.last_sync_time) > timedelta(seconds=self.sync_interval)):
                        self.sync_now(self.current_user_id)
                
                # 休眠一段时间
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"同步线程出错: {str(e)}")
                time.sleep(30)  # 出错后等待30秒再继续
    
    def _sync_record(self, db_service, sync_record):
        """同步单条记录"""
        if not self.cloud_api:
            logger.error("同步记录失败: 未设置云端API")
            return False
        
        # 确保db_service不为None
        if not db_service:
            logger.error("同步记录失败: 数据库服务为空")
            return False
        
        try:
            table_name = sync_record['table_name']
            record_id = sync_record['record_id']
            sync_status = sync_record['sync_status']
            
            if sync_status == 'deleted':
                # 处理删除同步
                result = self.cloud_api.delete_record(table_name, record_id)
                if result:
                    db_service.update_sync_status(sync_record['id'], 'synced')
                    logger.info(f"成功同步删除记录: {table_name}/{record_id}")
                    return True
                else:
                    db_service.update_sync_status(sync_record['id'], 'failed', "删除记录失败")
                    logger.error(f"同步删除记录失败: {table_name}/{record_id}")
                    return False
            else:
                # 获取要同步的记录
                record = db_service.get_record_by_id(table_name, record_id)
                if not record:
                    db_service.update_sync_status(sync_record['id'], 'failed', "记录不存在")
                    logger.error(f"同步失败: 记录不存在 {table_name}/{record_id}")
                    return False
                
                # 处理关联表的额外数据
                extra_data = {}
                if table_name == 'lab_report':
                    # 获取化验报告项目
                    items = db_service.get_records('lab_report_item', {'lab_report_id': record_id})
                    extra_data['items'] = items
                elif table_name == 'questionnaire':
                    # 获取问卷答案
                    answers = db_service.get_records('questionnaire_answer', {'questionnaire_id': record_id})
                    extra_data['answers'] = answers
                elif table_name == 'assessment_scale':
                    # 获取评估量表项目
                    items = db_service.get_records('assessment_scale_item', {'assessment_scale_id': record_id})
                    extra_data['items'] = items
                
                # 将记录同步到云端
                result = self.cloud_api.sync_record(table_name, record, extra_data)
                if result:
                    db_service.update_sync_status(sync_record['id'], 'synced')
                    logger.info(f"成功同步记录: {table_name}/{record_id}")
                    return True
                else:
                    db_service.update_sync_status(sync_record['id'], 'failed', "同步到云端失败")
                    logger.error(f"同步记录到云端失败: {table_name}/{record_id}")
                    return False
                
        except Exception as e:
            error_msg = f"同步记录出错: {str(e)}"
            db_service.update_sync_status(sync_record['id'], 'failed', error_msg)
            logger.error(error_msg)
            return False
    
    def download_from_cloud(self, user_id, last_sync_time=None):
        """从云端下载数据"""
        if not self.cloud_api:
            logger.error("从云端下载数据失败: 未设置云端API")
            return False
        
        try:
            # 创建数据库服务
            db_service = None
            if not self.db_service or self.db_service.user_id != user_id:
                db_service = BaseDBService(user_id)
            else:
                db_service = self.db_service
            
            # 确保db_service不为None
            if not db_service:
                logger.error(f"从云端下载数据失败: 无法创建数据库服务")
                return False
            
            # 获取上次同步时间
            if not last_sync_time:
                # 使用db_service提供的方法执行查询
                query = "SELECT MAX(sync_time) as last_sync FROM sync_record WHERE sync_status = 'synced'"
                result = db_service.execute_custom_query(query)
                last_sync_time = result[0]['last_sync'] if result and result[0]['last_sync'] else None
            
            # 从云端获取更新的数据
            updated_data = self.cloud_api.get_updates(user_id, last_sync_time)
            if not updated_data:
                logger.info(f"云端没有新数据需要下载")
                return True
            
            logger.info(f"从云端下载了 {len(updated_data)} 条记录")
            
            # 处理每条记录
            for item in updated_data:
                table_name = item['table_name']
                record = item['record']
                cloud_id = record.get('id')
                
                # 检查本地是否已有此记录
                local_id = None
                if cloud_id:
                    query = f"SELECT id FROM {table_name} WHERE cloud_id = ?"
                    params = (cloud_id,)
                    result = db_service.execute_custom_query(query, params)
                    if result:
                        local_id = result[0]['id']
                
                if local_id:
                    # 更新本地记录，但不标记为需要同步
                    db_service.update_record(table_name, local_id, record, sync=False)
                    logger.debug(f"更新了本地记录: {table_name}/{local_id}")
                else:
                    # 插入新记录，但不标记为需要同步
                    db_service.insert_record(table_name, record, sync=False)
                    logger.debug(f"插入了新记录: {table_name}")
                
                # 处理额外数据
                if 'extra_data' in item:
                    extra_data = item['extra_data']
                    if table_name == 'lab_report' and 'items' in extra_data:
                        for item_data in extra_data['items']:
                            item_data['lab_report_id'] = local_id
                            db_service.insert_record('lab_report_item', item_data, sync=False)
                    elif table_name == 'questionnaire' and 'answers' in extra_data:
                        for answer_data in extra_data['answers']:
                            answer_data['questionnaire_id'] = local_id
                            db_service.insert_record('questionnaire_answer', answer_data, sync=False)
                    elif table_name == 'assessment_scale' and 'items' in extra_data:
                        for item_data in extra_data['items']:
                            item_data['assessment_scale_id'] = local_id
                            db_service.insert_record('assessment_scale_item', item_data, sync=False)
            
            self.last_sync_time = datetime.now()
            logger.info(f"从云端下载数据完成")
            return True
            
        except Exception as e:
            logger.error(f"从云端下载数据时出错: {str(e)}")
            return False

# 单例模式
_sync_manager_instance = None

def get_sync_manager():
    """获取同步管理器实例（单例模式）"""
    global _sync_manager_instance
    if _sync_manager_instance is None:
        _sync_manager_instance = SyncManager()
    return _sync_manager_instance 