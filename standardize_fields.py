"""
字段名称标准化脚本

此脚本用于将移动端和后端的字段名称标准化，使用统一的字段名称：
- 用户ID：custom_id（替代user_id）
- 真实姓名：full_name（替代real_name）
- 用户名：username（保持不变）
- 用户角色：role（替代identity）

此脚本会更新用户数据文件，确保使用标准字段名称，并完全移除旧字段名称。
"""

import os
import sys
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "field_standardization.log"))
    ]
)
logger = logging.getLogger(__name__)

# 用户数据文件路径
USER_DATA_FILE = os.path.join(os.path.dirname(__file__), "user_data.json")

def standardize_user_data():
    """标准化用户数据字段名称"""
    try:
        # 检查文件是否存在
        if not os.path.exists(USER_DATA_FILE):
            logger.error(f"用户数据文件不存在: {USER_DATA_FILE}")
            return False

        # 读取用户数据
        with open(USER_DATA_FILE, "r", encoding="utf-8") as f:
            try:
                user_data = json.load(f)
            except json.JSONDecodeError:
                logger.error("用户数据文件格式错误")
                return False

        # 创建备份
        backup_file = f"{USER_DATA_FILE}.bak.{int(datetime.now().timestamp())}"
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(user_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已创建备份: {backup_file}")

        # 标准化顶层字段
        standardized_data = standardize_fields(user_data)

        # 标准化账户列表中的字段
        if "accounts" in standardized_data and isinstance(standardized_data["accounts"], list):
            for i, account in enumerate(standardized_data["accounts"]):
                if isinstance(account, dict):
                    standardized_data["accounts"][i] = standardize_fields(account)

        # 保存标准化后的数据
        with open(USER_DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(standardized_data, f, ensure_ascii=False, indent=2)

        logger.info("用户数据字段名称已标准化")
        return True

    except Exception as e:
        logger.error(f"标准化用户数据字段名称失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def standardize_fields(data):
    """标准化字段名称

    Args:
        data (dict): 用户数据字典

    Returns:
        dict: 标准化后的用户数据字典
    """
    if not isinstance(data, dict):
        return data

    # 创建新字典，只包含标准字段
    standardized_data = {}

    # 1. 处理用户名
    if "username" in data:
        standardized_data["username"] = data["username"]

    # 2. 处理密码
    if "password" in data:
        standardized_data["password"] = data["password"]

    # 3. 处理真实姓名
    if "full_name" in data:
        standardized_data["full_name"] = data["full_name"]
    elif "real_name" in data:
        standardized_data["full_name"] = data["real_name"]

    # 4. 处理用户角色
    if "role" in data:
        standardized_data["role"] = data["role"]
    elif "identity" in data:
        standardized_data["role"] = data["identity"]

    # 5. 处理用户ID
    if "custom_id" in data:
        standardized_data["custom_id"] = data["custom_id"]
    elif "user_id" in data:
        # 如果user_id看起来像是格式化的ID（包含前缀），则使用它作为custom_id
        user_id = data["user_id"]
        if isinstance(user_id, str) and ("_" in user_id or user_id.startswith("P") or user_id.startswith("D") or user_id.startswith("U") or user_id.startswith("SM")):
            standardized_data["custom_id"] = user_id
        else:
            # 不再生成临时ID
            print("警告: 数据中没有有效的custom_id，这可能导致某些功能无法正常工作")

    # 6. 处理其他字段
    for key, value in data.items():
        if key not in ["username", "password", "full_name", "real_name", "role", "identity", "custom_id", "user_id"]:
            standardized_data[key] = value

    # 7. 确保必要字段存在
    if "remember_password" not in standardized_data and "remember_password" in data:
        standardized_data["remember_password"] = data["remember_password"]

    if "last_login" not in standardized_data and "last_login" in data:
        standardized_data["last_login"] = data["last_login"]

    if "last_updated" not in standardized_data:
        standardized_data["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    return standardized_data

if __name__ == "__main__":
    logger.info("开始标准化用户数据字段名称...")
    success = standardize_user_data()
    if success:
        logger.info("用户数据字段名称标准化成功")
    else:
        logger.error("用户数据字段名称标准化失败")
