from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from kivy.uix.screenmanager import Screen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dropdownitem.dropdownitem import MDDropDownItem, MDDropDownItemText
from kivymd.uix.selectioncontrol import MDCheckbox

from utils.adaptive_layout import AdaptiveLayout

# 定义KV语言字符串
KV = '''
<LoginTab>:
    orientation: 'vertical'
    adaptive_height: True
    spacing: dp(20)
    padding: [0, dp(10), 0, 0]
    
    MDTextField:
        id: username
        hint_text: "用户名/手机号"
        mode: "outlined"
        icon_left: "account"
        size_hint_y: None
        height: dp(48)
        
    MDTextField:
        id: password
        hint_text: "请输入密码"
        mode: "outlined"
        icon_left: "lock"
        password: True
        size_hint_y: None
        height: dp(48)
        
    MDBoxLayout:
        size_hint_y: None
        height: dp(30)
        spacing: dp(8)
        padding: [dp(4), 0, dp(4), 0]
        
        # 左侧显示"记住密码"
        MDBoxLayout:
            adaptive_width: True
            spacing: dp(8)
            
            MDCheckbox:
                id: remember_password
                size_hint: None, None
                size: dp(24), dp(24)
                on_active: app.root.get_screen('login_screen').remember_password = self.active
                
            MDLabel:
                text: "记住密码"
                adaptive_size: True
                font_style: "Body"
                role: "small"
                pos_hint: {"center_y": 0.5}
        
        # 右侧显示"忘记密码"
        Widget:
            size_hint_x: 1
        
        MDButton:
            style: "text"
            pos_hint: {"center_y": 0.5}
            on_release: app.root.get_screen('login_screen').on_forgot_password()
            padding: [0, 0, 0, 0]
            
            MDButtonText:
                text: "忘记密码?"
                theme_text_color: "Primary" 
                font_style: "Body"
                role: "small"

<PhoneTab>:
    orientation: 'vertical'
    adaptive_height: True
    spacing: dp(20)
    padding: [0, dp(10), 0, 0]
    
    MDTextField:
        id: phone
        hint_text: "手机号"
        mode: "outlined"
        icon_left: "phone"
        size_hint_y: None
        height: dp(48)
        input_filter: "int"
        max_text_length: 11
        
    MDBoxLayout:
        size_hint_y: None
        height: dp(48)
        spacing: dp(10)
        
        MDTextField:
            id: verification_code
            hint_text: "验证码"
            mode: "outlined"
            icon_left: "numeric"
            size_hint_x: 0.7
            
        MDButton:
            style: "filled"
            size_hint_x: 0.3
            height: dp(48)
            on_release: app.root.get_screen('login_screen').on_request_verification_code()
            
            MDButtonText:
                text: "获取验证码"
    
    # 添加空白区块以保持与账号登录高度一致
    MDBoxLayout:
        size_hint_y: None
        height: dp(30)
        
    MDBoxLayout:
        size_hint_y: None
        height: dp(48)

<LoginScreen>:
    canvas.before:
        Color:
            rgba: 0.9, 0.95, 1, 1  # 浅蓝色背景
        Rectangle:
            pos: self.pos
            size: self.size
    
    ScrollView:
        id: scroll_view
        do_scroll_x: False
        do_scroll_y: True
        
        MDBoxLayout:
            id: main_layout
            orientation: 'vertical'
            adaptive_height: True
            padding: [dp(20), dp(20), dp(20), dp(20)]
            spacing: dp(20)
            
            # 顶部品牌区域 - 修改为顶置且宽度略小于屏宽
            MDBoxLayout:
                id: brand_layout
                orientation: 'vertical'
                adaptive_height: True
                spacing: dp(5)
                padding: [0, 0, 0, dp(20)]
                size_hint_x: 0.95
                pos_hint: {"center_x": 0.5}
                
                Image:
                    id: logo
                    source: "assets/icons/health-Logo .png"  # 使用Kivy默认图像作为占位符
                    size_hint_x: 1
                    size_hint_y: None
                    height: dp(120)
                    fit_mode: "contain"
                
                MDLabel:
                    id: app_title
                    text: "伴君一生智能健康管理"
                    halign: "center"
                    font_style: "Headline"
                    role: "medium"
                    adaptive_height: True
                    padding: [0, 0, 0, 0]
                
                MDLabel:
                    id: app_subtitle
                    text: "专业的健康管理平台"
                    halign: "center"
                    theme_text_color: "Secondary"
                    font_style: "Body"
                    role: "medium"
                    adaptive_height: True
                    padding: [0, 0, 0, 0]
            
            # 中部登录表单区域
            MDCard:
                id: login_card
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: dp(24)
                spacing: dp(20)
                elevation: 2
                radius: [dp(16)]
                shadow_softness: 8
                shadow_offset: (0, 1)
                
                MDBoxLayout:
                    id: identity_layout
                    size_hint_y: None
                    height: dp(56)
                    
                    MDLabel:
                        text: "请选择登录身份："
                        halign: "left"
                        adaptive_height: True
                        font_style: "Body"
                        role: "medium"
                        padding: [0, 0, 0, 0]
                        
                    MDDropDownItem:
                        id: identity_selector
                        size_hint_y: None
                        height: dp(48)
                        on_release: root.show_identity_menu(self)
                        
                        MDDropDownItemText:
                            id: identity_text
                            text: "请选择身份"
                
                MDBoxLayout:
                    id: tabs_layout
                    size_hint_y: None
                    height: dp(48)
                    padding: [dp(10), 0, dp(10), 0]
                    
                    MDButton:
                        id: account_tab_btn
                        style: "text"
                        size_hint_x: 0.5
                        md_bg_color: [0, 0, 0, 0]
                        on_release: 
                            root.current_tab = "account"
                            root.switch_tab("account")
                        
                        MDButtonText:
                            text: "账号登录"
                            theme_text_color: "Primary" if root.current_tab == "account" else "Secondary"
                            bold: root.current_tab == "account"
                    
                    MDButton:
                        id: phone_tab_btn
                        style: "text"
                        size_hint_x: 0.5
                        md_bg_color: [0, 0, 0, 0]
                        on_release: 
                            root.current_tab = "phone"
                            root.switch_tab("phone")
                        
                        MDButtonText:
                            text: "手机号登录"
                            theme_text_color: "Primary" if root.current_tab == "phone" else "Secondary"
                            bold: root.current_tab == "phone"
                
                MDBoxLayout:
                    id: tab_indicator
                    size_hint_y: None
                    height: dp(4)
                    
                    MDBoxLayout:
                        id: account_indicator
                        size_hint_x: 0.5
                        # No longer need to set md_bg_color here as it's handled in Python code
                    
                    MDBoxLayout:
                        id: phone_indicator
                        size_hint_x: 0.5
                        # No longer need to set md_bg_color here as it's handled in Python code
                
                MDBoxLayout:
                    id: tab_content
                    size_hint_y: None
                    height: self.minimum_height
                
                MDButton:
                    id: login_button
                    style: "elevated"
                    size_hint_x: 0.8  # 设置按钮宽度为卡片的80%
                    pos_hint: {"center_x": 0.5}  # 居中显示
                    height: dp(56)
                    elevation: 1
                    shadow_softness: 4
                    on_release: root.on_login()
                    
                    MDButtonText:
                        text: "登 录"
                        font_size: sp(18)
                        bold: True
            
            # 底部辅助操作区域
            MDBoxLayout:
                id: alternative_login_layout
                orientation: 'vertical'
                adaptive_height: True
                spacing: dp(20)
                
                MDLabel:
                    text: "其他登录方式"
                    halign: "center"
                    theme_text_color: "Secondary"
                    font_style: "Body"
                    role: "small"
                    adaptive_height: True
                    padding: [0, 0, 0, 0]
                
                MDBoxLayout:
                    adaptive_height: True
                    spacing: dp(30)
                    padding: [0, 0, 0, dp(20)]
                    
                    Widget:
                        size_hint_x: 0.2
                    
                    MDIconButton:
                        icon: "wechat"
                        icon_size: dp(32)
                        on_release: root.on_wechat_login()
                    
                    MDIconButton:
                        icon: "fingerprint"
                        icon_size: dp(32)
                        on_release: root.on_fingerprint_login()
                    
                    MDIconButton:
                        icon: "face-recognition"
                        icon_size: dp(32)
                        on_release: root.on_face_login()
                    
                    Widget:
                        size_hint_x: 0.2
                
                MDBoxLayout:
                    adaptive_height: True
                    
                    Widget:
                        size_hint_x: 0.2
                    
                    MDBoxLayout:
                        adaptive_height: True
                        
                        MDLabel:
                            text: "新用户注册"
                            halign: "center"
                            adaptive_height: True
                            padding: [0, 0, 0, 0]
                        
                        MDButton:
                            style: "text"
                            on_release: root.on_register()
                            
                            MDButtonText:
                                text: "立即注册"
                                theme_text_color: "Primary"
                    
                    Widget:
                        size_hint_x: 0.2
                
                MDLabel:
                    text: "登录即代表同意《用户协议》和《隐私政策》"
                    halign: "center"
                    theme_text_color: "Secondary"
                    font_style: "Body"
                    role: "small"
                    adaptive_height: True
                    padding: [0, 0, 0, 0]
'''

# 注册KV语言
Builder.load_string(KV)

class LoginTab(MDBoxLayout):
    """账号登录选项卡"""
    pass

class PhoneTab(MDBoxLayout):
    """手机号登录选项卡"""
    pass

class LoginScreen(Screen):
    """登录屏幕"""
    current_tab = StringProperty("account")
    identity = StringProperty("")
    remember_password = BooleanProperty(False)
    identity_menu = None  # 定义为实例变量
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.adaptive = AdaptiveLayout()
        Clock.schedule_once(self.init_ui)
        self.credentials_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../user_credentials.json")
    
    def init_ui(self, dt=0):
        """初始化UI组件"""
        # 获取当前app实例
        app = MDApp.get_running_app()
        # 设置初始选中的tab
        self.current_tab = 'account'
        # 直接切换到账号登录界面
        self.switch_tab('account')
        # 更新指示器
        self.update_tab_indicator()
        
        # 调整布局以适应屏幕
        self.adjust_layout()
        
        # 这里监听窗口大小变化
        Window.bind(on_resize=self.on_window_resize)
        
        # 加载已保存的凭据
        self.load_saved_credentials()
    
    def adjust_layout(self):
        """根据屏幕尺寸调整布局"""
        # 获取屏幕尺寸
        screen_width = Window.width
        
        # 获取设备类型
        device_type = self.adaptive.get_device_type(screen_width)
        
        # 计算内容区宽度
        content_width = self.adaptive.get_content_width(screen_width)
        
        # 调整登录卡片宽度
        self.ids.login_card.size_hint_x = content_width / screen_width
        self.ids.login_card.pos_hint = {"center_x": 0.5}
        
        # 调整logo尺寸
        self.ids.brand_layout.size_hint_x = min(0.95, content_width / screen_width)
        
        # 获取适合的间距和边距
        spacing = self.adaptive.get_spacing(screen_width)
        padding = self.adaptive.get_padding(screen_width)
        
        # 调整主布局
        self.ids.main_layout.spacing = spacing
        self.ids.main_layout.padding = padding
    
    def on_window_resize(self, instance, width, height):
        """窗口大小变化时调整布局"""
        self.adjust_layout()
    
    def switch_tab(self, tab_name):
        """切换标签页
        
        Args:
            tab_name: 标签页名称，'account' 或 'phone'
        """
        # 更新当前标签
        self.current_tab = tab_name
        
        # 根据标签页显示不同内容
        content_area = self.ids.tab_content
        content_area.clear_widgets()
        
        if tab_name == 'account':
            content_area.add_widget(LoginTab())
        else:
            content_area.add_widget(PhoneTab())
        
        # 更新指示器
        self.update_tab_indicator()
    
    def update_tab_indicator(self):
        """更新选项卡指示器"""
        # 更新按钮文本颜色
        for child in self.ids.account_tab_btn.children:
            if isinstance(child, MDButtonText):
                child.theme_text_color = "Primary" if self.current_tab == "account" else "Secondary"
                child.bold = self.current_tab == "account"
                
        for child in self.ids.phone_tab_btn.children:
            if isinstance(child, MDButtonText):
                child.theme_text_color = "Primary" if self.current_tab == "phone" else "Secondary"
                child.bold = self.current_tab == "phone"
        
        # 更新指示器颜色
        primary_color = MDApp.get_running_app().theme_cls.primaryColor
        
        # 设置指示器颜色
        self.ids.account_indicator.md_bg_color = primary_color if self.current_tab == "account" else [0, 0, 0, 0]
        self.ids.phone_indicator.md_bg_color = primary_color if self.current_tab == "phone" else [0, 0, 0, 0]
        
        # 增加选中标签的高亮效果 - 使用更淡的背景色
        light_primary = [primary_color[0], primary_color[1], primary_color[2], 0.15]
        self.ids.account_tab_btn.md_bg_color = light_primary if self.current_tab == "account" else [0, 0, 0, 0]
        self.ids.phone_tab_btn.md_bg_color = light_primary if self.current_tab == "phone" else [0, 0, 0, 0]
    
    def show_identity_menu(self, caller):
        """显示身份选择菜单
        
        Args:
            caller: 调用此方法的组件，通常是MDDropDownItem
        """
        if not self.identity_menu:
            menu_items = [
                {
                    "text": "个人用户",
                    "on_release": lambda x="个人用户": self.set_identity(x),
                },
                {
                    "text": "单位管理员",
                    "on_release": lambda x="单位管理员": self.set_identity(x),
                },
                {
                    "text": "健康顾问",
                    "on_release": lambda x="健康顾问": self.set_identity(x),
                },
                {
                    "text": "超级管理员",
                    "on_release": lambda x="超级管理员": self.set_identity(x),
                }
            ]
            self.identity_menu = MDDropdownMenu(
                caller=caller,
                items=menu_items,
                width=dp(200),
            )
        self.identity_menu.open()
    
    def set_identity(self, identity):
        """设置选择的身份
        
        Args:
            identity: 选择的身份
        """
        self.identity = identity
        self.ids.identity_text.text = identity
        self.identity_menu.dismiss()
    
    def on_login(self):
        """登录按钮点击事件"""
        # 检查是否选择了身份
        if not self.identity:
            self.show_error("请选择登录身份")
            return
        
        if self.current_tab == "account":
            # 获取用户名和密码
            username = self.ids.tab_content.children[0].ids.username.text
            password = self.ids.tab_content.children[0].ids.password.text
            
            # 验证用户名和密码是否填写
            if not username or not password:
                self.show_error("请填写完整的登录信息")
                return
            
            # 获取记住密码状态
            remember = self.remember_password
            
            # 保存凭据（如果选择了记住密码）
            self.save_credentials(username, password)
            
            # 显示登录成功消息
            self.show_success(f"以{self.identity}身份登录成功！")
            
            # 登录成功后，导航到主页
            Clock.schedule_once(lambda dt: self.navigate_to_homepage(), 1)
            
        else:  # phone tab
            # 获取手机号和验证码
            phone = self.ids.tab_content.children[0].ids.phone.text
            code = self.ids.tab_content.children[0].ids.verification_code.text
            
            # 验证手机号和验证码是否填写
            if not phone or not code:
                self.show_error("请填写完整的登录信息")
                return
            
            # 显示登录成功消息
            self.show_success(f"以{self.identity}身份使用手机号登录成功！")
            
            # 登录成功后，导航到主页
            Clock.schedule_once(lambda dt: self.navigate_to_homepage(), 1)
    
    def navigate_to_homepage(self):
        """导航到主页"""
        try:
            # 获取应用的屏幕管理器
            screen_manager = self.manager
            
            # 将屏幕切换到主页
            screen_manager.current = 'homepage_screen'
            
        except Exception as e:
            self.show_error(f"导航到主页时出错: {str(e)}")
            print(f"导航到主页时出错: {str(e)}")
    
    def on_forgot_password(self):
        """忘记密码点击事件"""
        self.show_info("忘记密码功能开发中...")
    
    def on_request_verification_code(self):
        """请求验证码点击事件"""
        # 获取手机号
        phone = self.ids.tab_content.children[0].ids.phone.text
        
        # 验证手机号是否填写
        if not phone or len(phone) != 11:
            self.show_error("请输入正确的手机号")
            return
        
        # 这里应该添加实际的验证码发送逻辑
        # 为了演示，我们假设发送成功
        self.show_success(f"验证码已发送至 {phone}")
    
    def on_wechat_login(self):
        """微信登录点击事件"""
        self.show_info("微信登录功能开发中...")
    
    def on_fingerprint_login(self):
        """指纹登录点击事件"""
        self.show_info("指纹登录功能开发中...")
    
    def on_face_login(self):
        """人脸登录点击事件"""
        self.show_info("人脸登录功能开发中...")
    
    def on_register(self):
        """注册点击事件"""
        try:
            # 获取应用的屏幕管理器
            screen_manager = self.manager
            
            # 切换到注册屏幕
            screen_manager.current = 'register'
            
        except Exception as e:
            self.show_error(f"导航到注册页面时出错: {str(e)}")
            print(f"导航到注册页面时出错: {str(e)}")
    
    def show_error(self, message):
        """显示错误消息
        
        Args:
            message: 错误信息
        """
        snackbar = MDSnackbar(
            MDSnackbarText(
                text=message,
            ),
        )
        snackbar.open()
    
    def show_success(self, message):
        """显示成功消息
        
        Args:
            message: 成功信息
        """
        snackbar = MDSnackbar(
            MDSnackbarText(
                text=message,
            ),
        )
        snackbar.open()
    
    def show_info(self, message):
        """显示提示消息
        
        Args:
            message: 提示信息
        """
        snackbar = MDSnackbar(
            MDSnackbarText(
                text=message,
            ),
        )
        snackbar.open()

    def load_saved_credentials(self):
        """加载已保存的凭据"""
        try:
            if os.path.exists(self.credentials_file):
                with open(self.credentials_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 检查是否有记住密码的标志
                    if isinstance(data, dict) and data.get("remember_password", False):
                        username = data.get("username", "")
                        password = data.get("password", "")
                        identity = data.get("identity", "")
                        
                        if username and password:
                            # 只有当登录标签内容已经准备好时才设置文本
                            Clock.schedule_once(lambda dt: self.set_saved_credentials(username, password, identity), 0.5)
        except Exception as e:
            print(f"加载保存的凭据时出错: {str(e)}")
    
    def set_saved_credentials(self, username, password, identity):
        """设置保存的凭据到UI"""
        try:
            # 确保tab_content已经存在且有children
            if hasattr(self.ids, 'tab_content') and self.ids.tab_content.children:
                # 账号登录标签应该是第一个子元素
                login_tab = self.ids.tab_content.children[0]
                
                # 设置用户名、密码和身份
                if hasattr(login_tab.ids, 'username'):
                    login_tab.ids.username.text = username
                
                if hasattr(login_tab.ids, 'password'):
                    login_tab.ids.password.text = password
                
                # 设置记住密码复选框
                if hasattr(login_tab.ids, 'remember_password'):
                    login_tab.ids.remember_password.active = True
                    self.remember_password = True
                
                # 设置身份
                if identity:
                    self.identity = identity
                    self.ids.identity_text.text = identity
        except Exception as e:
            print(f"设置保存的凭据时出错: {str(e)}")

    def save_credentials(self, username, password):
        """保存用户凭据"""
        try:
            if self.remember_password:
                data = {
                    "username": username,
                    "password": password,
                    "identity": self.identity,
                    "remember_password": True
                }
                
                # 确保目录存在
                os.makedirs(os.path.dirname(self.credentials_file), exist_ok=True)
                
                # 保存凭据
                with open(self.credentials_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False)
            else:
                # 如果取消了记住密码，删除保存的凭据
                if os.path.exists(self.credentials_file):
                    os.remove(self.credentials_file)
        except Exception as e:
            print(f"保存凭据时出错: {str(e)}")