# 临时ID弃用说明

## 背景

之前，移动端在某些情况下会生成临时ID（格式为`TEMP_时间戳`，例如`TEMP_1746432023`），这导致了一些问题，特别是在文件上传时。当移动端使用临时ID而不是后端生成的正式ID（如`P_00000009`）时，会导致"用户未登录"的错误。

## 变更内容

我们已经彻底弃用了临时ID的生成，所有用户ID现在都应该由后端生成。具体变更如下：

1. 移除了所有生成临时ID的代码
2. 当缺少正式ID时，不再生成临时ID，而是记录警告
3. 确保所有API调用优先使用`custom_id`字段

## 受影响的文件

以下文件已被修改，移除了临时ID生成代码：

1. `mobile/utils/user_manager.py`
   - `generate_user_id`方法（已弃用，返回None）
   - `add_account`方法（不再生成临时ID）

2. `mobile/screens/login_screen.py`
   - 登录成功后不再生成custom_id
   - 用户信息处理逻辑修改

3. `mobile/screens/register_screen.py`
   - `generate_user_id`方法（已弃用，返回None）

4. `mobile/utils/cloud_api.py`
   - 移除了所有生成custom_id的代码
   - 文件上传时使用后端提供的custom_id

5. `mobile/utils/local_api_client.py`
   - 移除了所有生成custom_id的代码
   - 登录成功后不再生成custom_id

6. `YUN/backend/app/services/id_generator.py`
   - 修改了错误处理逻辑，不再生成临时ID

7. `mobile/utils/api_adapters.py`
   - 用户数据适配逻辑修改，不再生成临时ID

## 注意事项

1. 所有用户ID现在都必须由后端生成，格式为`前缀_数字`
2. 如果后端未提供`custom_id`，系统会记录警告，某些功能可能无法正常工作
3. 开发人员应确保后端API始终返回`custom_id`字段
4. 不应再使用已弃用的`generate_user_id`方法

## 相关问题修复

此变更解决了以下问题：

1. 文件上传时出现"用户未登录"错误
2. 用户ID不一致导致的认证问题
3. 临时ID与后端ID不匹配导致的数据关联问题

## 后续工作

1. 确保后端API始终返回`custom_id`字段
2. 更新文档，明确说明用户ID的生成和使用规则
3. 监控系统日志，确保不再出现临时ID相关的问题
