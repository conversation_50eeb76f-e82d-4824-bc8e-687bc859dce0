"""
API客户端工厂 - 用于获取适当的API客户端实例
"""
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 缓存的API客户端实例
_api_client_instance = None

def get_api_client(client_type="local", force_new=False):
    """
    获取API客户端实例
    
    Args:
        client_type: API客户端类型，可选值：'local'（本地开发环境）, 'cloud'（云端环境）
        force_new: 是否强制创建新实例
    
    Returns:
        API客户端实例
    """
    global _api_client_instance
    
    # 如果已有实例且不强制创建新实例，直接返回
    if _api_client_instance is not None and not force_new:
        return _api_client_instance
    
    # 根据类型创建实例
    if client_type == "local":
        try:
            from .local_api_client import LocalApiClient
            _api_client_instance = LocalApiClient()
            logger.info("已创建本地API客户端实例")
        except ImportError as e:
            logger.error(f"创建本地API客户端失败: {str(e)}")
            # 回退到云端API客户端
            client_type = "cloud"
    
    if client_type == "cloud":
        try:
            from .cloud_api import CloudAPI
            _api_client_instance = CloudAPI()
            logger.info("已创建云端API客户端实例")
        except ImportError as e:
            logger.error(f"创建云端API客户端失败: {str(e)}")
            raise
    
    return _api_client_instance
