# mobile/api/api_client.py
import os
import logging
import sys
import time
from utils.cloud_api import CloudAPI, get_cloud_api
from datetime import datetime
from utils.user_manager import get_user_manager

# 配置日志 - 使用简单的日志配置
logger = logging.getLogger(__name__)
# 避免在模块级别调用任何可能导致递归的日志方法

class APIClient:
    """API客户端类，用于处理与服务器的通信"""

    def __init__(self):
        """初始化API客户端"""
        self.base_url = "http://************:80/api"  # 公网服务器主地址
        self.backup_url = "http://localhost:8006/api"     # 本地服务器备用地址
        self.token = None
        # 使用单例模式获取云端API客户端，确保全局只有一个实例
        from utils.cloud_api import get_cloud_api
        # 传递主地址和备用地址给CloudAPI，并设置自动切换
        self.cloud_api = get_cloud_api(base_url=self.base_url, backup_url=self.backup_url, auto_switch=True)

        # 检查上传队列大小，但不立即处理
        try:
            # 检查队列大小
            queue_size = self.cloud_api.get_upload_queue_size()
            if queue_size > 0:
                # 首先检查用户是否已登录
                from utils.user_manager import get_user_manager
                user_manager = get_user_manager()
                current_user = user_manager.get_current_user()

                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    # 用户已登录，可以处理上传队列
                    logger.info(f"发现 {queue_size} 个文件在上传队列中，用户已登录，尝试后台处理")
                    # 使用单独线程处理队列，避免阻塞UI
                    import threading
                    threading.Thread(
                        target=self._process_upload_queue_background,
                        args=(3,),  # 最多处理3个文件
                        daemon=True
                    ).start()
                else:
                    # 用户未登录，记录队列大小但不处理
                    logger.info(f"发现 {queue_size} 个文件在上传队列中，但用户未登录，等待用户登录后再处理")
            else:
                logger.info("上传队列为空，无需处理")
        except Exception as e:
            logger.error(f"初始化时检查上传队列时出错: {str(e)}")

        # 启动服务器健康检查
        self._start_health_check()

    def _inject_user_identity(self, params=None):
        """自动注入当前登录用户custom_id和token"""
        user = get_user_manager().get_current_user()
        if not params:
            params = {}
        if user:
            params['custom_id'] = getattr(user, 'custom_id', None)
            params['token'] = getattr(user, 'token', None)
        return params

    def post(self, url, data=None, **kwargs):
        data = self._inject_user_identity(data)
        return self.session.post(url, json=data, **kwargs)

    def get(self, url, params=None, **kwargs):
        params = self._inject_user_identity(params)
        return self.session.get(url, params=params, **kwargs)

    def _start_health_check(self):
        """启动服务器健康检查"""
        def check_server():
            while True:
                try:
                    # 每15分钟检查一次服务器状态
                    import time
                    time.sleep(900)

                    # 检查服务器状态
                    is_healthy = self.check_server_health()
                    if not is_healthy:
                        logger.warning("服务器健康检查失败，可能出现连接问题")
                except Exception as e:
                    logger.error(f"健康检查过程中出错: {str(e)}")

        # 在后台线程中运行
        import threading
        threading.Thread(target=check_server, daemon=True).start()
        logger.debug("已启动服务器健康检查")

        # 启动OCR队列处理
        self._start_ocr_queue_processor()

    def _start_ocr_queue_processor(self):
        """启动OCR队列处理器"""
        def process_ocr_queue():
            while True:
                try:
                    # 每5分钟处理一次OCR队列
                    import time
                    time.sleep(300)

                    # 检查认证状态
                    if not self.cloud_api.is_authenticated():
                        logger.debug("未认证，暂不处理OCR队列")
                        continue

                    # 处理OCR队列
                    self._process_ocr_queue()

                except Exception as e:
                    logger.error(f"处理OCR队列时出错: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # 出错后等待较长时间再重试
                    time.sleep(600)

        # 在后台线程中运行
        import threading
        threading.Thread(target=process_ocr_queue, daemon=True).start()
        logger.debug("已启动OCR队列处理器")

    def _process_ocr_queue(self, max_items=3):
        """处理OCR队列

        Args:
            max_items (int): 最大处理数量

        Returns:
            tuple: (成功数, 失败数)
        """
        # 获取OCR队列目录
        ocr_queue_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'ocr_queue')

        # 确保目录存在
        if not os.path.exists(ocr_queue_dir):
            os.makedirs(ocr_queue_dir, exist_ok=True)
            return (0, 0)

        # 获取队列文件
        import glob
        queue_files = glob.glob(os.path.join(ocr_queue_dir, "*.json"))

        if not queue_files:
            return (0, 0)

        # 按创建时间排序
        queue_files.sort(key=lambda x: os.path.getmtime(x))

        # 限制处理数量
        queue_files = queue_files[:max_items]

        success_count = 0
        fail_count = 0

        import json
        current_time = int(time.time())

        for file_path in queue_files:
            try:
                # 读取队列项
                with open(file_path, 'r', encoding='utf-8') as f:
                    queue_item = json.load(f)

                file_id = queue_item.get('file_id')
                options = queue_item.get('options', {})
                retries = queue_item.get('retries', 0)
                next_retry = queue_item.get('next_retry', 0)

                # 检查是否到达重试时间
                if next_retry > current_time:
                    logger.debug(f"OCR队列项 {file_id} 还未到达重试时间，跳过")
                    continue

                # 检查重试次数
                if retries >= 5:
                    logger.warning(f"OCR队列项 {file_id} 已达到最大重试次数，放弃处理")
                    os.remove(file_path)
                    fail_count += 1
                    continue

                # 请求OCR处理
                logger.info(f"从队列中处理OCR请求: {file_id}")
                ocr_result = self.cloud_api.request_ocr(file_id, options=options)

                if ocr_result and (ocr_result.get('success') or ocr_result.get('task_id')):
                    # 请求成功
                    task_id = ocr_result.get('task_id', '')
                    logger.info(f"OCR请求成功，任务ID: {task_id}")

                    # 更新本地数据库中的OCR状态
                    from utils.health_data_manager import get_health_data_manager
                    health_data_manager = get_health_data_manager()
                    health_data_manager.update_document_ocr_status(
                        file_id,
                        'processing',
                        task_id
                    )

                    # 启动异步OCR结果检查
                    import threading
                    threading.Thread(
                        target=self._check_and_process_ocr_result,
                        args=(file_id, task_id),
                        daemon=True
                    ).start()

                    # 删除队列项
                    os.remove(file_path)
                    success_count += 1
                else:
                    # 请求失败，更新重试信息
                    queue_item['retries'] = retries + 1

                    # 使用指数退避策略
                    backoff_time = min(2 ** (retries + 1), 60) * 60  # 最大60分钟
                    queue_item['next_retry'] = current_time + backoff_time

                    # 保存回队列
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(queue_item, f, ensure_ascii=False)

                    logger.warning(f"OCR请求失败，将在 {backoff_time/60:.1f} 分钟后重试: {file_id}")
                    fail_count += 1

            except Exception as e:
                logger.error(f"处理OCR队列项时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                fail_count += 1

        if success_count > 0 or fail_count > 0:
            logger.info(f"OCR队列处理完成: {success_count} 个成功, {fail_count} 个失败")

        return (success_count, fail_count)

    def check_server_health(self):
        """检查服务器健康状态"""
        if self.cloud_api:
            return self.cloud_api.check_server_health()
        return False

    def upload_document(self, file_path):
        """上传文档到服务器

        Args:
            file_path: 要上传的文件路径

        Returns:
            dict: 上传结果
        """
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": "文件不存在"}

            # 使用cloud_api上传文件
            return self.cloud_api.upload_file(file_path)
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_documents(self, custom_id=None, page=1, page_size=20):
        """
        获取用户文档列表

        Args:
            custom_id: 用户自定义ID (可选)
            page: 页码
            page_size: 每页数量
        Returns:
            dict: 文档列表
        """
        try:
            params = {
                'page': page,
                'page_size': page_size
            }
            if custom_id:
                params['custom_id'] = custom_id
            # 使用cloud_api获取文档列表
            result = self.cloud_api.get_documents(**params)
            return result
        except Exception as e:
            print(f"获取文档列表失败: {str(e)}")
            return None

    def _process_upload_queue_background(self, max_items=5):
        """在后台线程中处理上传队列

        Args:
            max_items: 最大处理数量
        """
        try:
            # 等待3秒，确保应用已完全初始化
            import time
            time.sleep(3)

            # 首先检查用户是否已登录
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            # 如果用户未登录，不处理上传队列
            if not current_user:
                logger.info("用户未登录，暂不处理上传队列")
                return

            # 尝试加载认证信息
            self.cloud_api.load_auth_info()

            # 检查是否是本地服务器模式
            is_local_server = "localhost" in self.cloud_api.base_url or "127.0.0.1" in self.cloud_api.base_url

            # 如果已认证，立即处理队列
            if self.cloud_api.is_authenticated():
                logger.info("开始处理上传队列...")
                success, fail = self.cloud_api.process_upload_queue(max_items)
                logger.info(f"上传队列处理完成: {success} 个成功, {fail} 个失败")
            else:
                # 检查是否有custom_id
                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    # 设置custom_id到cloud_api
                    self.cloud_api.custom_id = current_user.custom_id
                    logger.info(f"使用用户custom_id: {self.cloud_api.custom_id}处理上传队列")
                    success, fail = self.cloud_api.process_upload_queue(max_items)
                    logger.info(f"使用custom_id上传队列处理完成: {success} 个成功, {fail} 个失败")
                else:
                    if is_local_server:
                        logger.info("本地服务器模式下未认证，暂不处理上传队列，请先登录")
                    else:
                        logger.info("未认证，暂时不处理上传队列")

                    # 启动定时任务，每隔一段时间检查认证状态并尝试处理队列
                    import threading
                    def delayed_queue_processor():
                        # 等待30秒后再次尝试
                        time.sleep(30)
                        try:
                            # 再次检查用户是否已登录
                            current_user = user_manager.get_current_user()
                            if not current_user:
                                logger.info("用户仍未登录，暂不处理上传队列")
                                return

                            # 再次尝试加载认证信息
                            self.cloud_api.load_auth_info()

                            # 检查是否是本地服务器模式
                            is_local_server = "localhost" in self.cloud_api.base_url or "127.0.0.1" in self.cloud_api.base_url

                            if self.cloud_api.is_authenticated():
                                logger.info("延迟处理上传队列...")
                                success, fail = self.cloud_api.process_upload_queue(max_items)
                                logger.info(f"延迟上传队列处理完成: {success} 个成功, {fail} 个失败")
                            else:
                                # 检查是否有custom_id
                                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                                    # 设置custom_id到cloud_api
                                    self.cloud_api.custom_id = current_user.custom_id
                                    logger.info(f"使用用户custom_id: {self.cloud_api.custom_id}延迟处理上传队列")
                                    success, fail = self.cloud_api.process_upload_queue(max_items)
                                    logger.info(f"使用custom_id延迟上传队列处理完成: {success} 个成功, {fail} 个失败")
                                else:
                                    if is_local_server:
                                        logger.info("本地服务器模式下仍未认证，暂不处理上传队列，请先登录")
                                    else:
                                        logger.info("仍未认证，无法处理上传队列")
                        except Exception as e:
                            logger.error(f"延迟处理上传队列时出错: {str(e)}")

                    # 启动延迟处理线程
                    threading.Thread(target=delayed_queue_processor, daemon=True).start()
        except Exception as e:
            logger.error(f"处理上传队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())