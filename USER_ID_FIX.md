# 用户ID不一致问题修复

## 问题描述

移动端上传文件时出现"用户未登录"错误，但实际上用户已经登录。日志显示：

```
页最终显示: 姓名=马金江, 用户ID=P_00000009
设置欢迎消息: 姓名=马金江, 性别=先生
已更新欢迎消息: 欢迎 马金江 先生
从user_manager获取到用户信息: 姓名=马金江, ID=TEMP_1746432023
正在打开文件选择器...
选择的文件: C:\Users\<USER>\Desktop\医疗服务\马金江\2024-3-19（调药前）.pdf
用户未登录，无法上传文件
2025-05-05 17:55:38,194 - utils.cloud_api - INFO - 文件已添加到上传队列: C:\Users\<USER>\Desktop\医疗服务\马金江\2024-3-19（调药前）.pdf
```

问题原因是云端返回的用户ID（P_00000009）与从user_manager获取的用户ID（TEMP_1746432023）不一致，导致系统认为用户未登录。

## 修复方案

1. 修改 `CloudAPI` 类的 `_save_auth_info` 方法，保存 `custom_id` 字段
2. 修改 `CloudAPI` 类的 `load_auth_info` 方法，加载 `custom_id` 字段
3. 修改 `UserManager` 类的 `get_current_user_id` 方法，优先返回 `custom_id` 字段
4. 修改 `APIClient` 类的 `upload_document` 方法，优先使用 `custom_id` 字段

## 修复详情

### 1. 修改 CloudAPI._save_auth_info 方法

```python
def _save_auth_info(self, token, user_id):
    """保存认证信息到本地存储"""
    try:
        # 创建保存目录
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        os.makedirs(data_dir, exist_ok=True)

        # 保存令牌到本地文件
        auth_data = {
            "token": token,
            "user_id": user_id,
            "custom_id": self.custom_id,  # 添加custom_id字段
            "refresh_token": self.refresh_token_str,
            "expires_at": self.expires_at,
            "timestamp": time.time()
        }

        # 保存到文件
        token_file = os.path.join(data_dir, 'cloud_auth.json')
        with open(token_file, 'w', encoding='utf-8') as f:
            json.dump(auth_data, f, ensure_ascii=False)

        # 更新实例变量
        self.token = token
        self.user_id = user_id

        logger.info(f"已保存认证信息到本地存储，用户ID: {user_id}, custom_id: {self.custom_id}")
    except Exception as e:
        logger.error(f"保存认证信息失败: {str(e)}")
```

### 2. 修改 CloudAPI.load_auth_info 方法

```python
def load_auth_info(self):
    """从文件加载认证信息"""
    try:
        # 使用统一的文件名 cloud_auth.json
        auth_file = os.path.join(self.data_dir, 'cloud_auth.json')
        if os.path.exists(auth_file):
            with open(auth_file, 'r', encoding='utf-8') as f:
                auth_info = json.load(f)

            self.token = auth_info.get("token")
            self.refresh_token_str = auth_info.get("refresh_token")
            self.user_id = auth_info.get("user_id")
            self.custom_id = auth_info.get("custom_id")  # 加载custom_id字段
            self.expires_at = auth_info.get("expires_at")

            # 检查token是否过期
            if self.expires_at and time.time() > self.expires_at:
                logger.info("加载的token已过期，尝试刷新")
                if not self.refresh_token():
                    logger.warning("刷新token失败，清除已保存的认证信息")
                    self.token = None
                    self.refresh_token_str = None
                    self.user_id = None
                    self.custom_id = None  # 清除custom_id
                    self.expires_at = None
            else:
                logger.info(f"已加载认证信息，用户ID: {self.user_id}, custom_id: {self.custom_id}")
    except Exception as e:
        logger.error(f"加载认证信息失败: {str(e)}")
```

### 3. 修改 UserManager.get_current_user_id 方法

```python
def get_current_user_id(self):
    """获取当前用户ID

    Returns:
        str: 当前用户ID，优先返回custom_id，如果没有则返回user_id，如果没有当前用户则返回None
    """
    if self.current_user:
        # 优先返回custom_id
        if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
            print(f"返回当前用户custom_id: {self.current_user.custom_id}")
            return self.current_user.custom_id
        # 如果没有custom_id，返回user_id
        print(f"返回当前用户user_id: {self.current_user.user_id}")
        return self.current_user.user_id
    return None
```

### 4. 修改 APIClient.upload_document 方法

```python
# 获取当前用户ID
# 1. 先尝试从云API获取custom_id
user_id = None
if hasattr(self.cloud_api, 'custom_id') and self.cloud_api.custom_id:
    user_id = self.cloud_api.custom_id
    logger.info(f"使用cloud_api.custom_id: {self.cloud_api.custom_id}")
# 2. 如果没有custom_id，尝试使用user_id
elif self.cloud_api.user_id:
    user_id = self.cloud_api.user_id
    logger.info(f"使用cloud_api.user_id: {self.cloud_api.user_id}")
# 3. 如果仍然不可用，从用户管理器获取
if not user_id:
    from utils.user_manager import get_user_manager
    user_manager = get_user_manager()
    user_id = user_manager.get_current_user_id()
    logger.info(f"从user_manager获取用户ID: {user_id}")
```

## 修复效果

修复后，移动端将正确使用云端返回的用户ID（如P_00000009）进行文件上传，而不是使用临时生成的ID（如TEMP_1746432023）。这样可以确保用户在登录状态下正常上传文件，不会出现"用户未登录"的错误。
