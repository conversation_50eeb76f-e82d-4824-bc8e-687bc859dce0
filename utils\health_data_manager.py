"""
健康数据管理模块 - 处理健康数据的本地存储和云端同步
"""
import os
import json
import logging
import threading
import time
import uuid
from pathlib import Path
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

class HealthDataManager:
    """
    健康数据管理器 - 负责健康数据的本地存储和云端同步
    """
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls):
        """单例模式获取实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
            return cls._instance
    
    def __init__(self):
        """初始化健康数据管理器"""
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 数据存储路径
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.health_data_dir = os.path.join(self.data_dir, 'health_data')
        self.documents_dir = os.path.join(self.data_dir, 'documents')
        self.sync_queue_dir = os.path.join(self.data_dir, 'sync_queue', 'health_data')
        
        # 确保目录存在
        os.makedirs(self.health_data_dir, exist_ok=True)
        os.makedirs(self.documents_dir, exist_ok=True)
        os.makedirs(self.sync_queue_dir, exist_ok=True)
        
        # 本地数据库文件
        self.health_db_file = os.path.join(self.data_dir, 'health_data_db.json')
        self.documents_db_file = os.path.join(self.data_dir, 'documents_db.json')
        
        # 初始化本地数据库
        self._init_db()
        
        # 启动同步队列处理线程
        threading.Thread(target=self._process_sync_queue_background, daemon=True).start()
    
    def _init_db(self):
        """初始化本地数据库"""
        # 健康数据数据库
        if not os.path.exists(self.health_db_file):
            with open(self.health_db_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'records': [],
                    'last_updated': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
        
        # 文档数据库
        if not os.path.exists(self.documents_db_file):
            with open(self.documents_db_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'documents': [],
                    'last_updated': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
    
    def save_health_data(self, health_data):
        """保存健康数据
        
        Args:
            health_data (dict): 健康数据
            
        Returns:
            str: 数据ID，失败返回None
        """
        try:
            # 生成本地数据ID
            if 'data_id' not in health_data:
                health_data['data_id'] = str(uuid.uuid4())
            
            # 添加创建和更新时间
            if 'created_at' not in health_data:
                health_data['created_at'] = datetime.now().isoformat()
            health_data['updated_at'] = datetime.now().isoformat()
            
            # 添加同步状态
            health_data['sync_status'] = 'pending'
            
            # 保存到文件
            data_id = health_data['data_id']
            file_path = os.path.join(self.health_data_dir, f"{data_id}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(health_data, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_health_db(data_id, health_data)
            
            logger.info(f"健康数据已保存，ID: {data_id}")
            return data_id
            
        except Exception as e:
            logger.error(f"保存健康数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def _update_health_db(self, data_id, health_data):
        """更新健康数据数据库
        
        Args:
            data_id (str): 数据ID
            health_data (dict): 健康数据
        """
        with self._lock:
            try:
                # 读取当前数据库
                with open(self.health_db_file, 'r', encoding='utf-8') as f:
                    db = json.load(f)
                
                # 检查是否已存在
                existing_index = None
                for i, record in enumerate(db['records']):
                    if record['data_id'] == data_id:
                        existing_index = i
                        break
                
                # 准备基本记录信息
                record_info = {
                    'data_id': data_id,
                    'user_id': health_data.get('user_id', ''),
                    'document_type': health_data.get('document_type', 'unknown'),
                    'file_id': health_data.get('file_id', ''),
                    'created_at': health_data.get('created_at', ''),
                    'updated_at': health_data.get('updated_at', ''),
                    'sync_status': health_data.get('sync_status', 'pending'),
                    'cloud_data_id': health_data.get('cloud_data_id', '')
                }
                
                # 更新或添加记录
                if existing_index is not None:
                    db['records'][existing_index] = record_info
                else:
                    db['records'].append(record_info)
                
                # 更新最后更新时间
                db['last_updated'] = datetime.now().isoformat()
                
                # 保存数据库
                with open(self.health_db_file, 'w', encoding='utf-8') as f:
                    json.dump(db, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                logger.error(f"更新健康数据数据库失败: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
    
    def get_health_data(self, data_id):
        """获取健康数据
        
        Args:
            data_id (str): 数据ID
            
        Returns:
            dict: 健康数据，不存在返回None
        """
        try:
            # 检查文件是否存在
            file_path = os.path.join(self.health_data_dir, f"{data_id}.json")
            if not os.path.exists(file_path):
                logger.warning(f"健康数据不存在: {data_id}")
                return None
            
            # 读取数据
            with open(file_path, 'r', encoding='utf-8') as f:
                health_data = json.load(f)
            
            return health_data
            
        except Exception as e:
            logger.error(f"获取健康数据失败: {str(e)}")
            return None
    
    def get_health_data_list(self, user_id=None, document_type=None):
        """获取健康数据列表
        
        Args:
            user_id (str, optional): 用户ID过滤
            document_type (str, optional): 文档类型过滤
            
        Returns:
            list: 健康数据列表
        """
        try:
            # 读取数据库
            with open(self.health_db_file, 'r', encoding='utf-8') as f:
                db = json.load(f)
            
            # 筛选记录
            records = db['records']
            
            if user_id:
                records = [r for r in records if r['user_id'] == user_id]
            
            if document_type:
                records = [r for r in records if r['document_type'] == document_type]
            
            # 按创建时间倒序排序
            records.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            
            return records
            
        except Exception as e:
            logger.error(f"获取健康数据列表失败: {str(e)}")
            return []
    
    def mark_health_data_synced(self, data_id, cloud_data_id):
        """标记健康数据已同步
        
        Args:
            data_id (str): 本地数据ID
            cloud_data_id (str): 云端数据ID
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取数据
            health_data = self.get_health_data(data_id)
            if not health_data:
                logger.warning(f"标记同步状态失败，健康数据不存在: {data_id}")
                return False
            
            # 更新同步状态
            health_data['sync_status'] = 'synced'
            health_data['cloud_data_id'] = cloud_data_id
            health_data['updated_at'] = datetime.now().isoformat()
            
            # 保存回文件
            file_path = os.path.join(self.health_data_dir, f"{data_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(health_data, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_health_db(data_id, health_data)
            
            logger.info(f"健康数据已标记为已同步，本地ID: {data_id}，云端ID: {cloud_data_id}")
            return True
            
        except Exception as e:
            logger.error(f"标记健康数据同步状态失败: {str(e)}")
            return False
    
    def add_to_sync_queue(self, health_data):
        """添加健康数据到同步队列
        
        Args:
            health_data (dict): 健康数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保有数据ID
            if 'data_id' not in health_data:
                health_data['data_id'] = str(uuid.uuid4())
            
            data_id = health_data['data_id']
            
            # 保存到队列
            queue_file = os.path.join(self.sync_queue_dir, f"{data_id}.json")
            
            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'health_data': health_data,
                    'created_at': datetime.now().isoformat(),
                    'retries': 0
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"健康数据已添加到同步队列，ID: {data_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加健康数据到同步队列失败: {str(e)}")
            return False
    
    def _process_sync_queue_background(self):
        """后台处理同步队列"""
        try:
            # 等待应用完全初始化
            time.sleep(5)
            
            while True:
                try:
                    # 检查云API是否已认证
                    from utils.cloud_api import get_cloud_api
                    cloud_api = get_cloud_api()
                    
                    if cloud_api.is_authenticated():
                        # 处理队列
                        self._process_sync_queue(cloud_api)
                    
                    # 每10分钟检查一次
                    time.sleep(10 * 60)
                    
                except Exception as e:
                    logger.error(f"处理同步队列线程异常: {str(e)}")
                    time.sleep(60)  # 出错后等待1分钟
                    
        except Exception as e:
            logger.error(f"同步队列处理线程初始化失败: {str(e)}")
    
    def _process_sync_queue(self, cloud_api, max_items=3):
        """处理同步队列
        
        Args:
            cloud_api: 云API实例
            max_items (int): 最大处理数量
            
        Returns:
            tuple: (成功数, 失败数)
        """
        if not os.path.exists(self.sync_queue_dir):
            return 0, 0
        
        # 获取队列文件
        queue_files = [f for f in os.listdir(self.sync_queue_dir) if f.endswith('.json')]
        
        if not queue_files:
            return 0, 0
        
        # 按创建时间排序
        queue_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.sync_queue_dir, x)))
        
        # 限制处理数量
        queue_files = queue_files[:max_items]
        
        success_count = 0
        fail_count = 0
        
        for file_name in queue_files:
            file_path = os.path.join(self.sync_queue_dir, file_name)
            
            try:
                # 读取队列项
                with open(file_path, 'r', encoding='utf-8') as f:
                    queue_item = json.load(f)
                
                health_data = queue_item.get('health_data', {})
                data_id = health_data.get('data_id')
                retries = queue_item.get('retries', 0)
                
                # 尝试同步
                result = cloud_api.save_health_data(health_data)
                
                if result and result.get('success'):
                    # 同步成功
                    cloud_data_id = result.get('data_id')
                    
                    # 标记为已同步
                    self.mark_health_data_synced(data_id, cloud_data_id)
                    
                    # 删除队列文件
                    os.remove(file_path)
                    
                    success_count += 1
                    logger.info(f"健康数据同步成功，ID: {data_id}，云端ID: {cloud_data_id}")
                    
                else:
                    # 同步失败
                    fail_count += 1
                    
                    # 增加重试次数
                    queue_item['retries'] = retries + 1
                    
                    # 保存回队列
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(queue_item, f, ensure_ascii=False, indent=2)
                    
                    logger.warning(f"健康数据同步失败，ID: {data_id}，重试次数: {retries + 1}")
            
            except Exception as e:
                logger.error(f"处理健康数据同步队列项失败: {str(e)}")
                fail_count += 1
        
        return success_count, fail_count
    
    def save_document(self, document_data):
        """保存文档记录
        
        Args:
            document_data (dict): 文档数据
            
        Returns:
            str: 文档ID，失败返回None
        """
        try:
            # 确保有文档ID
            if 'document_id' not in document_data:
                document_data['document_id'] = str(uuid.uuid4())
            
            document_id = document_data['document_id']
            
            # 添加时间戳
            if 'created_at' not in document_data:
                document_data['created_at'] = datetime.now().isoformat()
            document_data['updated_at'] = datetime.now().isoformat()
            
            # 保存到文件
            file_path = os.path.join(self.documents_dir, f"{document_id}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(document_data, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_documents_db(document_id, document_data)
            
            logger.info(f"文档记录已保存，ID: {document_id}")
            return document_id
            
        except Exception as e:
            logger.error(f"保存文档记录失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def _update_documents_db(self, document_id, document_data):
        """更新文档数据库
        
        Args:
            document_id (str): 文档ID
            document_data (dict): 文档数据
        """
        with self._lock:
            try:
                # 读取当前数据库
                with open(self.documents_db_file, 'r', encoding='utf-8') as f:
                    db = json.load(f)
                
                # 检查是否已存在
                existing_index = None
                for i, doc in enumerate(db['documents']):
                    if doc['document_id'] == document_id:
                        existing_index = i
                        break
                
                # 准备基本记录信息
                doc_info = {
                    'document_id': document_id,
                    'file_id': document_data.get('file_id', ''),
                    'user_id': document_data.get('user_id', ''),
                    'document_type': document_data.get('document_type', 'unknown'),
                    'description': document_data.get('description', ''),
                    'file_path': document_data.get('file_path', ''),
                    'upload_status': document_data.get('upload_status', ''),
                    'ocr_status': document_data.get('ocr_status', ''),
                    'created_at': document_data.get('created_at', ''),
                    'updated_at': document_data.get('updated_at', '')
                }
                
                # 更新或添加记录
                if existing_index is not None:
                    db['documents'][existing_index] = doc_info
                else:
                    db['documents'].append(doc_info)
                
                # 更新最后更新时间
                db['last_updated'] = datetime.now().isoformat()
                
                # 保存数据库
                with open(self.documents_db_file, 'w', encoding='utf-8') as f:
                    json.dump(db, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                logger.error(f"更新文档数据库失败: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
    
    def update_document_ocr_status(self, file_id, ocr_status, task_id=None, ocr_data=None, verification=None, error=None):
        """更新文档OCR状态
        
        Args:
            file_id (str): 文件ID
            ocr_status (str): OCR状态 ('pending', 'processing', 'completed', 'failed')
            task_id (str, optional): OCR任务ID
            ocr_data (dict, optional): OCR提取的数据
            verification (dict, optional): 验证结果
            error (str, optional): 错误信息
            
        Returns:
            bool: 是否成功
        """
        try:
            # 查找文档
            document = self.get_document_by_file_id(file_id)
            if not document:
                logger.warning(f"更新OCR状态失败，文档不存在，文件ID: {file_id}")
                return False
            
            document_id = document['document_id']
            
            # 更新状态
            document['ocr_status'] = ocr_status
            document['updated_at'] = datetime.now().isoformat()
            
            if task_id:
                document['ocr_task_id'] = task_id
            
            if ocr_data:
                document['ocr_data'] = ocr_data
            
            if verification:
                document['verification'] = verification
            
            if error:
                document['ocr_error'] = error
            
            # 保存回文件
            file_path = os.path.join(self.documents_dir, f"{document_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(document, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self._update_documents_db(document_id, document)
            
            logger.info(f"文档OCR状态已更新，文件ID: {file_id}，状态: {ocr_status}")
            return True
            
        except Exception as e:
            logger.error(f"更新文档OCR状态失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def get_document_by_file_id(self, file_id):
        """根据文件ID获取文档
        
        Args:
            file_id (str): 文件ID
            
        Returns:
            dict: 文档数据，不存在返回None
        """
        try:
            # 读取数据库
            with open(self.documents_db_file, 'r', encoding='utf-8') as f:
                db = json.load(f)
            
            # 查找文档
            document_id = None
            for doc in db['documents']:
                if doc['file_id'] == file_id:
                    document_id = doc['document_id']
                    break
            
            if not document_id:
                return None
            
            # 读取文档文件
            file_path = os.path.join(self.documents_dir, f"{document_id}.json")
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                document = json.load(f)
            
            return document
            
        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            return None

# 全局获取函数
def get_health_data_manager():
    """获取健康数据管理器实例"""
    return HealthDataManager.get_instance() 