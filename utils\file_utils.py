# mobile/utils/file_utils.py
import os
import logging
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Flowable
from reportlab.lib.styles import getSampleStyleSheet
from typing import List, Sequence

logger = logging.getLogger(__name__)

def convert_text_to_pdf(text_file_path):
    """
    将文本文件转换为PDF文件
    
    Args:
        text_file_path: 文本文件路径
        
    Returns:
        str: 生成的PDF文件路径，失败则返回None
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(text_file_path):
            logger.error(f"文件不存在: {text_file_path}")
            return None
            
        # 读取文本内容
        with open(text_file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # 生成PDF文件路径
        pdf_path = os.path.splitext(text_file_path)[0] + '.pdf'
        
        # 创建PDF文档
        doc = SimpleDocTemplate(pdf_path, pagesize=letter)
        styles = getSampleStyleSheet()
        style = styles['Normal']
        
        # 将文本内容分行处理
        lines = text_content.split('\n')
        story = [Paragraph(line.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;'), style) 
                for line in lines if line.strip()]
        
        # 构建PDF
        doc.build(story)  # type: ignore
        
        logger.info(f"成功将文本文件转换为PDF: {pdf_path}")
        return pdf_path
        
    except Exception as e:
        logger.error(f"转换文本到PDF时出错: {str(e)}")
        return None