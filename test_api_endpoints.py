#!/usr/bin/env python3
"""
测试API端点修复
"""

import requests
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://8.138.188.26:80/api"

    # 测试端点列表 - 使用正确的API端点
    endpoints = [
        ("评估量表列表", "assessments"),
        ("调查问卷列表", "questionnaires"),
        ("健康文档列表", "documents"),
        ("健康记录", "user-health-records/U_0002"),  # 使用示例custom_id
        ("登录", "login"),
    ]

    logger.info(f"测试服务器: {base_url}")

    for name, endpoint in endpoints:
        url = f"{base_url}/{endpoint}"
        logger.info(f"\n测试 {name}: {url}")

        try:
            # 设置代理禁用
            proxies = {
                'http': None,
                'https': None
            }

            response = requests.get(url, timeout=10, proxies=proxies)
            logger.info(f"状态码: {response.status_code}")

            if response.status_code == 200:
                logger.info(f"✓ {name} 端点可访问")
            elif response.status_code == 401:
                logger.info(f"✓ {name} 端点存在（需要认证）")
            elif response.status_code == 404:
                logger.error(f"✗ {name} 端点不存在 (404)")
            elif response.status_code == 502:
                logger.error(f"✗ {name} 网关错误 (502)")
            else:
                logger.warning(f"? {name} 返回状态码: {response.status_code}")

        except requests.exceptions.ConnectTimeout:
            logger.error(f"✗ {name} 连接超时")
        except requests.exceptions.ConnectionError:
            logger.error(f"✗ {name} 连接错误")
        except Exception as e:
            logger.error(f"✗ {name} 请求失败: {str(e)}")

def test_backup_server():
    """测试备用服务器"""
    backup_url = "http://localhost:8006/api"

    logger.info(f"\n测试备用服务器: {backup_url}")

    try:
        proxies = {
            'http': None,
            'https': None
        }

        response = requests.get(f"{backup_url}/assessment-scales", timeout=5, proxies=proxies)
        logger.info(f"备用服务器状态码: {response.status_code}")

        if response.status_code == 200:
            logger.info("✓ 备用服务器可访问")
        else:
            logger.warning(f"? 备用服务器返回状态码: {response.status_code}")

    except requests.exceptions.ConnectionError:
        logger.warning("✗ 备用服务器连接被拒绝（可能未启动）")
    except Exception as e:
        logger.error(f"✗ 备用服务器测试失败: {str(e)}")

def main():
    """主测试函数"""
    logger.info("开始测试API端点修复...")

    test_api_endpoints()
    test_backup_server()

    logger.info("\n=== 测试总结 ===")
    logger.info("1. 如果主服务器端点返回401，说明端点存在但需要认证")
    logger.info("2. 如果返回404，说明端点路径不正确")
    logger.info("3. 如果返回502，说明网关错误")
    logger.info("4. 备用服务器连接被拒绝是正常的（本地服务器未启动）")

if __name__ == "__main__":
    main()
