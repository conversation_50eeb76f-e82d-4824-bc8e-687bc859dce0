import os
import sqlite3
import json
import logging
import time
from datetime import datetime
from contextlib import contextmanager

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('database')

class DatabaseManager:
    """数据库管理器，负责管理所有数据库连接和操作"""
    
    # 数据库版本
    DB_VERSION = "1.0"
    
    # 数据库目录
    DB_DIR = os.path.join(os.path.expanduser('~'), 'health-trea', 'db')
    
    def __init__(self, db_dir=None):
        """初始化数据库管理器"""
        # 如果提供了自定义目录，使用它
        if db_dir:
            self.DB_DIR = db_dir
            
        # 确保数据目录存在
        if not os.path.exists(self.DB_DIR):
            os.makedirs(self.DB_DIR)
            logger.info(f"创建数据库目录: {self.DB_DIR}")
            
        # 当前连接的用户ID和数据库连接
        self.current_user_id = None
        self.connection = None
        
        # 初始化用户数据映射
        self.user_db_map = self._load_user_db_map()
    
    def _load_user_db_map(self):
        """加载用户数据库映射文件"""
        map_file = os.path.join(self.DB_DIR, 'user_db_map.json')
        if os.path.exists(map_file):
            try:
                with open(map_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载用户数据库映射失败: {str(e)}")
                return {}
        else:
            # 创建一个新映射文件
            empty_map = {}
            self._save_user_db_map(empty_map)
            return empty_map
    
    def _save_user_db_map(self, map_data):
        """保存用户数据库映射文件"""
        map_file = os.path.join(self.DB_DIR, 'user_db_map.json')
        try:
            with open(map_file, 'w', encoding='utf-8') as f:
                json.dump(map_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存用户数据库映射失败: {str(e)}")
            return False
    
    def get_db_path(self, user_id):
        """获取用户数据库文件路径"""
        if not user_id:
            logger.error("用户ID不能为空")
            return None
            
        # 检查用户ID是否已有数据库
        if user_id not in self.user_db_map:
            # 注册新用户数据库
            db_filename = f"user_{user_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.db"
            self.user_db_map[user_id] = {
                "db_filename": db_filename,
                "created_at": datetime.now().isoformat(),
                "version": self.DB_VERSION
            }
            self._save_user_db_map(self.user_db_map)
            logger.info(f"为用户 {user_id} 创建新数据库: {db_filename}")
        
        # 返回数据库文件路径
        db_filename = self.user_db_map[user_id]["db_filename"]
        return os.path.join(self.DB_DIR, db_filename)
    
    def connect(self, user_id):
        """连接到用户数据库"""
        if not user_id:
            logger.error("连接数据库失败: 用户ID不能为空")
            return False
        
        # 如果已连接到其他用户，先断开连接
        if self.connection and self.current_user_id != user_id:
            self.disconnect()
        
        # 如果已经连接到当前用户，直接返回
        if self.connection and self.current_user_id == user_id:
            # 验证连接是否有效
            try:
                self.connection.execute("SELECT 1")
                return True
            except sqlite3.Error as e:
                logger.warning(f"当前数据库连接已失效，将重新连接: {str(e)}")
                self.connection = None
                self.current_user_id = None
                # 继续执行连接逻辑
        
        # 获取数据库文件路径
        db_path = self.get_db_path(user_id)
        if not db_path:
            logger.error(f"无法获取用户 {user_id} 的数据库路径")
            return False
        
        # 检查数据库目录是否存在
        db_dir = os.path.dirname(db_path)
        if not os.path.exists(db_dir):
            try:
                os.makedirs(db_dir, exist_ok=True)
                logger.info(f"创建数据库目录: {db_dir}")
            except Exception as e:
                logger.error(f"创建数据库目录失败: {str(e)}")
                return False
        
        # 检查数据库文件是否可访问
        if os.path.exists(db_path):
            if not os.access(db_path, os.R_OK | os.W_OK):
                logger.error(f"数据库文件权限不足: {db_path}")
                return False
        
        try:
            # 连接数据库，设置超时
            self.connection = sqlite3.connect(db_path, timeout=30.0)
            # 启用外键约束
            self.connection.execute("PRAGMA foreign_keys = ON")
            # 设置返回行为字典而不是元组
            self.connection.row_factory = sqlite3.Row
            # 设置当前用户ID
            self.current_user_id = user_id
            logger.info(f"已连接到用户 {user_id} 的数据库: {db_path}")
            return True
        except sqlite3.Error as e:
            logger.error(f"SQLite错误: 连接用户 {user_id} 数据库失败: {str(e)}")
            self.connection = None
            self.current_user_id = None
            return False
        except Exception as e:
            logger.error(f"连接用户 {user_id} 数据库失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            self.connection = None
            self.current_user_id = None
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                logger.info(f"已断开与用户 {self.current_user_id} 数据库的连接")
            except Exception as e:
                logger.error(f"断开数据库连接时出错: {str(e)}")
            finally:
                self.connection = None
                self.current_user_id = None
    
    @contextmanager
    def get_connection(self, user_id=None):
        """获取数据库连接的上下文管理器"""
        # 如果提供了用户ID，确保连接到该用户
        temp_connect = False
        max_retries = 3
        retry_count = 0
        
        if user_id and self.current_user_id != user_id:
            while retry_count < max_retries:
                try:
                    if self.connect(user_id):
                        temp_connect = True
                        break
                    else:
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"连接到用户 {user_id} 的数据库失败，尝试重试 ({retry_count}/{max_retries})")
                            time.sleep(1)  # 等待1秒后重试
                        else:
                            logger.error(f"连接到用户 {user_id} 的数据库失败，已达到最大重试次数")
                            raise Exception(f"无法连接到用户 {user_id} 的数据库，已重试 {max_retries} 次")
                except Exception as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"连接到用户 {user_id} 的数据库时出错，尝试重试 ({retry_count}/{max_retries}): {str(e)}")
                        time.sleep(1)  # 等待1秒后重试
                    else:
                        logger.error(f"连接到用户 {user_id} 的数据库时出错，已达到最大重试次数: {str(e)}")
                        import traceback
                        logger.error(traceback.format_exc())
                        raise
        
        # 如果当前没有连接，尝试重新连接
        if not self.connection:
            if user_id:
                logger.warning(f"数据库未连接，尝试连接到用户 {user_id} 的数据库")
                if not self.connect(user_id):
                    logger.error(f"无法连接到用户 {user_id} 的数据库")
                    raise Exception(f"数据库未连接且无法连接到用户 {user_id} 的数据库")
                temp_connect = True
            else:
                logger.error("数据库未连接且未提供用户ID")
                raise Exception("数据库未连接且未提供用户ID")
        
        # 验证连接是否有效
        try:
            self.connection.execute("SELECT 1")
        except sqlite3.Error as e:
            logger.warning(f"数据库连接已失效，尝试重新连接: {str(e)}")
            if user_id or self.current_user_id:
                user_to_connect = user_id if user_id else self.current_user_id
                if not self.connect(user_to_connect):
                    logger.error(f"重新连接到用户 {user_to_connect} 的数据库失败")
                    raise Exception(f"数据库连接已失效且无法重新连接到用户 {user_to_connect} 的数据库")
                if user_id and user_id != self.current_user_id:
                    temp_connect = True
            else:
                logger.error("数据库连接已失效且无法重新连接（未提供用户ID且无当前用户）")
                raise Exception("数据库连接已失效且无法重新连接")
        
        try:
            yield self.connection
        except sqlite3.Error as e:
            logger.error(f"SQLite错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"使用数据库连接时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise
        finally:
            # 如果是临时连接，断开连接
            if temp_connect:
                self.disconnect()
    
    def execute_query(self, query, params=(), user_id=None):
        """执行查询语句并返回结果集"""
        try:
            with self.get_connection(user_id) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                result = cursor.fetchall()
                return [dict(row) for row in result]
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}\n查询: {query}\n参数: {params}")
            return []
    
    def execute_update(self, query, params=(), user_id=None):
        """执行更新语句并返回受影响的行数"""
        try:
            with self.get_connection(user_id) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"执行更新失败: {str(e)}\n查询: {query}\n参数: {params}")
            return 0
    
    def execute_insert(self, query, params=(), user_id=None):
        """执行插入语句并返回最后插入的ID"""
        try:
            with self.get_connection(user_id) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"执行插入失败: {str(e)}\n查询: {query}\n参数: {params}")
            return None
    
    def execute_script(self, script, user_id=None):
        """执行SQL脚本"""
        try:
            with self.get_connection(user_id) as conn:
                conn.executescript(script)
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"执行脚本失败: {str(e)}")
            return False
    
    def table_exists(self, table_name, user_id=None):
        """检查表是否存在"""
        query = """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name=?
        """
        result = self.execute_query(query, (table_name,), user_id)
        return len(result) > 0
    
    def create_table(self, table_name, columns, user_id=None):
        """创建表"""
        # 检查表是否已存在
        if self.table_exists(table_name, user_id):
            logger.info(f"表 {table_name} 已存在")
            return True
        
        # 构建创建表的SQL
        columns_sql = ", ".join(columns)
        query = f"CREATE TABLE {table_name} ({columns_sql})"
        
        try:
            with self.get_connection(user_id) as conn:
                conn.execute(query)
                conn.commit()
                logger.info(f"创建表 {table_name} 成功")
                return True
        except Exception as e:
            logger.error(f"创建表 {table_name} 失败: {str(e)}")
            return False
    
    def create_index(self, table_name, column_names, index_name=None, unique=False, user_id=None):
        """在表上创建索引"""
        if not index_name:
            # 自动生成索引名
            index_name = f"idx_{table_name}_{'_'.join(column_names)}"
        
        unique_str = "UNIQUE" if unique else ""
        columns_str = ", ".join(column_names)
        query = f"CREATE {unique_str} INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"
        
        try:
            with self.get_connection(user_id) as conn:
                conn.execute(query)
                conn.commit()
                logger.info(f"在表 {table_name} 上创建索引 {index_name} 成功")
                return True
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")
            return False
    
    def get_table_info(self, table_name, user_id=None):
        """获取表的列信息"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query, user_id=user_id)
    
    def backup_database(self, user_id, backup_dir=None):
        """备份用户数据库"""
        if not user_id:
            logger.error("备份数据库失败: 用户ID不能为空")
            return False
        
        # 默认备份到数据目录下的backups子目录
        if not backup_dir:
            backup_dir = os.path.join(self.DB_DIR, 'backups')
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
        
        # 获取源数据库路径
        source_path = self.get_db_path(user_id)
        if not source_path or not os.path.exists(source_path):
            logger.error(f"备份数据库失败: 用户 {user_id} 的数据库不存在")
            return False
        
        # 生成备份文件名
        backup_filename = f"backup_{os.path.basename(source_path)}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        try:
            # 连接到源数据库
            source_conn = sqlite3.connect(source_path)
            # 创建备份数据库
            backup_conn = sqlite3.connect(backup_path)
            
            # 执行备份
            source_conn.backup(backup_conn)
            
            # 关闭连接
            backup_conn.close()
            source_conn.close()
            
            logger.info(f"备份用户 {user_id} 的数据库到 {backup_path} 成功")
            return True
        except Exception as e:
            logger.error(f"备份用户 {user_id} 的数据库失败: {str(e)}")
            return False

# 单例模式
_db_manager_instance = None

def get_db_manager():
    """获取数据库管理器实例（单例模式）"""
    global _db_manager_instance
    if _db_manager_instance is None:
        _db_manager_instance = DatabaseManager()
    return _db_manager_instance