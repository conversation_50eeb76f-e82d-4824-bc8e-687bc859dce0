# 移动端 API 测试工具

本目录包含用于测试健康管理系统移动端 API 的工具。

## 测试脚本

`test_cloud_api.py` 是一个用于测试移动端 API 兼容性的 Python 脚本。它会自动对以下功能进行测试：

1. 登录认证
2. 用户资料管理
3. 健康记录管理

## 使用方法

### 前提条件

确保已启动健康管理系统的前端和后端服务：

```bash
# 在项目根目录执行
start_all.bat
```

或者分别启动：

```bash
# 后端
cd backend
start.bat

# 前端 (需要管理员权限，因为使用了80端口)
cd frontend
start.bat
```

### 运行测试

在本目录执行以下命令：

```bash
python test_cloud_api.py
```

### 配置说明

测试脚本的配置参数位于文件开头：

```python
# 配置参数
BASE_URL = "http://localhost"  # 前端服务地址(端口80)
TEST_USERNAME = "admin"  # 测试用户名
TEST_PASSWORD = "admin123"  # 测试密码
```

如需测试其他环境，请修改 `BASE_URL` 为实际的服务器地址。

### 测试结果

测试结果会在命令行中显示，同时会保存到 `api_test_responses` 目录中，便于后续分析。

## 故障排除

### 连接被拒绝

如果看到类似以下错误：

```
请求出错: HTTPConnectionPool(...): Max retries exceeded with url: ... (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at ...>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
```

请检查：

1. 前端服务是否正在运行 (http://localhost)
2. 服务是否能在浏览器中访问
3. 是否存在防火墙阻止
4. 端口80是否需要管理员权限运行

### 认证失败

如果登录认证失败，请检查：

1. 测试账户名和密码是否正确
2. 后端服务是否已正确运行并且可以访问
3. 数据库中是否存在您提供的测试用户 