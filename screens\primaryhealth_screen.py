# screens/upload_screen.py
import os
from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.lang import Builder
from kivy.metrics import dp
from api.api_client import APIClient
from kivy.uix.modalview import ModalView
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.spinner import Spinner, SpinnerOption
#from widgets.camera_view import CameraView  # 从widgets导入CameraView
from kivy.properties import BooleanProperty
from theme import AppTheme, AppMetrics
from datetime import datetime
from kivy.factory import Factory
#from utils.platform_utils import PlatformUtils  # 导入平台工具类
from kivy.clock import Clock  # 添加Clock模块的导入
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件

# 自定义SpinnerOption样式
class CustomSpinnerOption(SpinnerOption):
    def __init__(self, **kwargs):
        super(CustomSpinnerOption, self).__init__(**kwargs)
        # 确保触摸事件能够正确传递
        self.bind(on_release=self._on_release)

    def _on_release(self, *args):
        # 确保选项被点击时能够正确关闭下拉菜单并更新选中值
        spinner = self.parent.parent
        if spinner:
            spinner.text = self.text
            spinner.is_open = False
            # 手动触发on_text事件
            if hasattr(spinner, 'on_text_callback'):
                spinner.on_text_callback(spinner, self.text)
        # 打印调试信息
        print(f"CustomSpinnerOption selected: {self.text}")

# 确保CustomSpinnerOption在kv字符串中可用
Factory.register('CustomSpinnerOption', cls=CustomSpinnerOption)

Builder.load_string("""
#:import os os
<CustomSpinnerOption>:
    size_hint_y: None
    height: dp(40)
    background_normal: ''
    background_color: app.theme.PRIMARY_LIGHT if self.state == 'down' else app.theme.CARD_BACKGROUND
    color: app.theme.TEXT_PRIMARY
    font_size: app.metrics.FONT_SIZE_NORMAL
    # 移除可能冲突的on_release绑定，让类中的_on_release方法处理
    # 添加触摸反馈
    on_release: print(f"Option {self.text} pressed")

<UploadScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    BoxLayout:
        orientation: "vertical"
        padding: dp(app.metrics.PADDING_LARGE)
        spacing: dp(app.metrics.PADDING_MEDIUM) / 2  # 减小间距使界面更紧凑

        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [dp(app.metrics.CORNER_RADIUS)]

            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo

        # 健康信息录入标签
        BoxLayout:
            size_hint_y: None
            height: dp(40)
            padding: [dp(10), dp(5), dp(10), dp(5)]

            Label:
                text: "健康信息录入"
                font_size: dp(app.metrics.FONT_SIZE_LARGE)
                color: app.theme.TEXT_PRIMARY
                bold: True
                halign: 'left'
                text_size: self.size

        # 健康信息表格部分
        ScrollView:
            BoxLayout:
                orientation: "vertical"
                size_hint_y: None
                height: self.minimum_height
                spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距使界面更紧凑

                # 基本健康信息
                BoxLayout:
                    orientation: "vertical"
                    spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距
                    size_hint_y: None
                    height: self.minimum_height
                    canvas.before:
                        Color:
                            rgba: app.theme.CARD_BACKGROUND
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(app.metrics.CORNER_RADIUS)]
                    padding: [dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2, dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2]  # 减小垂直间距

                    Label:
                        text: "基本健康信息"
                        size_hint_y: None
                        height: dp(25)  # 减小高度
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 基本健康信息输入框
                    TextInput:
                        id: upload_basic_info
                        hint_text: "请输入基本健康信息"
                        multiline: True
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT * 1.5)  # 略微减小高度
                        font_size: app.metrics.FONT_SIZE_NORMAL

                # 重要病史
                BoxLayout:
                    orientation: "vertical"
                    spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距
                    size_hint_y: None
                    height: self.minimum_height
                    canvas.before:
                        Color:
                            rgba: app.theme.CARD_BACKGROUND
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(app.metrics.CORNER_RADIUS)]
                    padding: [dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2, dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2]  # 减小垂直间距

                    Label:
                        text: "重要病史（可逐条添加）"
                        size_hint_y: None
                        height: dp(25)  # 减小高度
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 疾病名称
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        spacing: dp(5)
                        TextInput:
                            id: disease_name
                            hint_text: "疾病名称"
                            multiline: False
                            size_hint_x: 0.7

                    # 患病时长
                    TextInput:
                        id: disease_duration
                        hint_text: "患病时长（如：2年）"
                        multiline: False
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)

                    # 用药情况
                    TextInput:
                        id: medication
                        hint_text: "用药情况"
                        multiline: False
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)

                    # 控制效果
                    Spinner:
                        id: control_effect
                        text: "控制效果"
                        values: ["良好", "一般", "较差", "无效果"]
                        option_cls: 'CustomSpinnerOption'
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        background_normal: ''
                        background_color: app.theme.PRIMARY_MEDIUM

                    # 添加按钮
                    Button:
                        text: "添加病史"
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        background_color: app.theme.PRIMARY_COLOR
                        color: app.theme.TEXT_LIGHT
                        on_release: root.add_disease(disease_name.text, disease_duration.text, medication.text, control_effect.text)

                    # 已添加疾病列表
                    BoxLayout:
                        id: disease_list_container
                        orientation: "vertical"
                        size_hint_y: None
                        height: self.minimum_height
                        padding: [dp(10), dp(5), dp(10), dp(5)]  # 减小垂直间距

                # 药物过敏
                BoxLayout:
                    orientation: "vertical"
                    spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距
                    size_hint_y: None
                    height: self.minimum_height
                    canvas.before:
                        Color:
                            rgba: app.theme.CARD_BACKGROUND
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(app.metrics.CORNER_RADIUS)]
                    padding: [dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2, dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2]  # 减小垂直间距

                    Label:
                        text: "药物过敏史（可逐条添加）"
                        size_hint_y: None
                        height: dp(25)  # 减小高度
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 药物过敏输入
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        spacing: dp(5)
                        TextInput:
                            id: drug_allergy
                            hint_text: "药物过敏史"
                            multiline: False
                            size_hint_x: 0.7
                        Button:
                            text: "添加"
                            size_hint_x: 0.3
                            background_color: app.theme.PRIMARY_COLOR
                            color: app.theme.TEXT_LIGHT
                            on_release: root.add_drug_allergy(drug_allergy.text)

                    # 已添加药物过敏列表
                    BoxLayout:
                        id: allergy_list_container
                        orientation: "vertical"
                        size_hint_y: None
                        height: self.minimum_height
                        padding: [dp(10), dp(5), dp(10), dp(5)]  # 减小垂直间距

                # 基因信息
                BoxLayout:
                    orientation: "vertical"
                    spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距
                    size_hint_y: None
                    height: self.minimum_height
                    canvas.before:
                        Color:
                            rgba: app.theme.CARD_BACKGROUND
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(app.metrics.CORNER_RADIUS)]
                    padding: [dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2, dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2]  # 减小垂直间距

                    Label:
                        text: "基因信息（可逐条添加）"
                        size_hint_y: None
                        height: dp(25)  # 减小高度
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 基因信息输入
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        spacing: dp(5)
                        TextInput:
                            id: gene_info
                            hint_text: "基因信息"
                            multiline: False
                            size_hint_x: 0.7
                        Button:
                            text: "添加"
                            size_hint_x: 0.3
                            background_color: app.theme.PRIMARY_COLOR
                            color: app.theme.TEXT_LIGHT
                            on_release: root.add_gene_info(gene_info.text)

                    # 已添加基因信息列表
                    BoxLayout:
                        id: gene_list_container
                        orientation: "vertical"
                        size_hint_y: None
                        height: self.minimum_height
                        padding: [dp(10), dp(5), dp(10), dp(5)]  # 减小垂直间距

                # 血型
                BoxLayout:
                    orientation: "vertical"
                    spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距
                    size_hint_y: None
                    height: self.minimum_height
                    canvas.before:
                        Color:
                            rgba: app.theme.CARD_BACKGROUND
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(app.metrics.CORNER_RADIUS)]
                    padding: [dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2, dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2]  # 减小垂直间距

                    # 血型选择
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        Label:
                            text: "血型："
                            size_hint_x: 0.3
                            color: app.theme.TEXT_PRIMARY
                        Spinner:
                            id: blood_type
                            text: "A型"
                            values: ["A型", "B型", "AB型", "O型", "未知"]
                            option_cls: 'CustomSpinnerOption'
                            size_hint_x: 0.7
                            background_color: app.theme.PRIMARY_MEDIUM

                # 家族疾病史
                BoxLayout:
                    orientation: "vertical"
                    spacing: dp(app.metrics.PADDING_NORMAL) / 2  # 减小间距
                    size_hint_y: None
                    height: self.minimum_height
                    canvas.before:
                        Color:
                            rgba: app.theme.CARD_BACKGROUND
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(app.metrics.CORNER_RADIUS)]
                    padding: [dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2, dp(app.metrics.PADDING_MEDIUM), dp(app.metrics.PADDING_MEDIUM) / 2]  # 减小垂直间距

                    Label:
                        text: "家族疾病史（可逐条添加）"
                        size_hint_y: None
                        height: dp(25)  # 减小高度
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 疾病名称
                    TextInput:
                        id: family_disease_name
                        hint_text: "疾病名称"
                        multiline: False
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)

                    # 与患者关系
                    TextInput:
                        id: family_relation
                        hint_text: "家族关系（如：父子）"
                        multiline: False
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)

                    # 添加按钮
                    Button:
                        text: "添加家族病史"
                        size_hint_y: None
                        height: dp(app.metrics.INPUT_HEIGHT)
                        background_color: app.theme.PRIMARY_COLOR
                        color: app.theme.TEXT_LIGHT
                        on_release: root.add_family_disease(family_disease_name.text, family_relation.text)

                    # 已添加家族疾病列表
                    BoxLayout:
                        id: family_disease_container
                        orientation: "vertical"
                        size_hint_y: None
                        height: self.minimum_height
                        padding: [dp(10), dp(5), dp(10), dp(5)]  # 减小垂直间距

                # 按钮区域
                BoxLayout:
                    orientation: "horizontal"
                    spacing: dp(app.metrics.PADDING_NORMAL)
                    size_hint_y: None
                    height: dp(app.metrics.BUTTON_HEIGHT_PRIMARY)
                    padding: [0, dp(app.metrics.PADDING_MEDIUM), 0, 0]

                    Button:
                        text: "提交信息"
                        background_color: app.theme.SUCCESS_COLOR
                        color: app.theme.TEXT_LIGHT
                        on_release: root.submit_health_info()

                    Button:
                        text: "返回上一级"
                        background_color: app.theme.PRIMARY_COLOR
                        color: app.theme.TEXT_LIGHT
                        on_release: app.root.current = 'homepage_screen'
""")


class UploadScreen(Screen):
    """健康信息录入屏幕"""
    api_client = APIClient()
    diseases = []  # 存储疾病信息的列表
    drug_allergies = []  # 存储药物过敏信息
    gene_list = []  # 存储添加的基因记录
    family_diseases = []  # 存储家族疾病史

    def __init__(self, **kwargs):
        super(UploadScreen, self).__init__(**kwargs)
        self.app_metrics = AppMetrics()
        # 初始化空列表
        self.diseases = []
        self.drug_allergies = []
        self.gene_list = []
        self.family_diseases = []
        # 获取主题和字体
        self.theme = AppTheme()
        # 延迟初始化，确保界面完全加载后再绑定事件
        Clock.schedule_once(self._init_ui, 0.1)

    def _init_ui(self, dt):
        """初始化UI组件"""
        # 清空所有列表显示容器
        if hasattr(self.ids, 'disease_list_container'):
            self.ids.disease_list_container.clear_widgets()
        if hasattr(self.ids, 'allergy_list_container'):
            self.ids.allergy_list_container.clear_widgets()
        if hasattr(self.ids, 'gene_list_container'):
            self.ids.gene_list_container.clear_widgets()
        if hasattr(self.ids, 'family_disease_container'):
            self.ids.family_disease_container.clear_widgets()

    def show_error_dialog(self, message):
        """显示错误对话框"""
        modal = ModalView(size_hint=(0.6, 0.3), auto_dismiss=True)
        content = BoxLayout(orientation='vertical', padding=dp(10))
        content.add_widget(Label(text=message))
        modal.add_widget(content)
        modal.open()

    def simulate_upload(self, filename):
        """模拟上传文件"""
        result = self.api_client.upload_health_data(filename)
        modal = ModalView(size_hint=(0.6, 0.4), auto_dismiss=True)
        modal.add_widget(Label(text=result["message"]))
        modal.open()

    def add_disease(self, name, duration, medication, effect):
        """添加疾病信息"""
        if not name:
            self.show_error_dialog("请输入疾病名称")
            return

        # 创建疾病记录
        disease_info = {
            'name': name,
            'duration': duration,
            'medication': medication,
            'effect': effect
        }
        self.diseases.append(disease_info)

        # 清空输入框
        self.ids.disease_name.text = ''
        self.ids.disease_duration.text = ''
        self.ids.medication.text = ''
        self.ids.control_effect.text = '控制效果'

        # 更新疾病列表显示
        self._update_disease_list()

        # 显示添加成功提示
        self.show_error_dialog(f"已添加疾病: {name}")

    def _update_disease_list(self):
        """更新疾病列表显示"""
        if not hasattr(self.ids, 'disease_list_container'):
            return

        # 清空当前列表
        self.ids.disease_list_container.clear_widgets()

        # 没有数据时显示提示
        if not self.diseases:
            label = Label(
                text="暂无添加的疾病记录",
                size_hint_y=None,
                height=dp(30),
                color=self.theme.TEXT_SECONDARY
            )
            self.ids.disease_list_container.add_widget(label)
            return

        # 添加标题
        title = Label(
            text="已添加的疾病记录:",
            size_hint_y=None,
            height=dp(30),
            color=self.theme.TEXT_PRIMARY,
            halign='left',
            valign='middle',
            text_size=(self.width, None)
        )
        self.ids.disease_list_container.add_widget(title)

        # 添加每条疾病记录
        for i, disease in enumerate(self.diseases):
            box = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(5),
                padding=[dp(15), 0, 0, 0]  # 左边添加更大的间距，使内容向右移动
            )

            # 添加序号和疾病信息
            info_text = f"{i+1}. {disease['name']}"
            if disease['duration']:
                info_text += f" | 患病时长: {disease['duration']}"
            if disease['medication']:
                info_text += f" | 用药: {disease['medication']}"
            if disease['effect'] and disease['effect'] != "控制效果":
                info_text += f" | 控制效果: {disease['effect']}"

            info = Label(
                text=info_text,
                size_hint_x=0.8,
                color=self.theme.TEXT_PRIMARY,
                halign='left',
                valign='middle',
                text_size=(self.width * 0.75, None)  # 限制文本宽度，确保不会超出可见区域
            )

            # 添加删除按钮
            btn = Button(
                text="删除",
                size_hint_x=0.2,
                background_color=self.theme.ERROR_COLOR
            )
            btn.index = i  # 存储索引以便删除
            btn.bind(on_release=lambda btn: self._delete_disease(btn.index))

            box.add_widget(info)
            box.add_widget(btn)
            self.ids.disease_list_container.add_widget(box)

    def _delete_disease(self, index):
        """删除指定索引的疾病记录"""
        if 0 <= index < len(self.diseases):
            del self.diseases[index]
            self._update_disease_list()

    def add_drug_allergy(self, drug_allergy):
        """添加药物过敏记录"""
        if not drug_allergy:
            self.show_error_dialog("请输入药物过敏信息")
            return

        # 添加到过敏列表
        self.drug_allergies.append(drug_allergy)

        # 清空输入框
        self.ids.drug_allergy.text = ""

        # 更新过敏列表显示
        self._update_allergy_list()

        # 显示添加成功提示
        self.show_error_dialog(f"已添加药物过敏记录：{drug_allergy}")

    def _update_allergy_list(self):
        """更新药物过敏列表显示"""
        if not hasattr(self.ids, 'allergy_list_container'):
            return

        # 清空当前列表
        self.ids.allergy_list_container.clear_widgets()

        # 没有数据时显示提示
        if not self.drug_allergies:
            label = Label(
                text="暂无添加的药物过敏记录",
                size_hint_y=None,
                height=dp(30),
                color=self.theme.TEXT_SECONDARY
            )
            self.ids.allergy_list_container.add_widget(label)
            return

        # 添加标题
        title = Label(
            text="已添加的药物过敏记录:",
            size_hint_y=None,
            height=dp(30),
            color=self.theme.TEXT_PRIMARY,
            halign='left',
            valign='middle',
            text_size=(self.width, None)
        )
        self.ids.allergy_list_container.add_widget(title)

        # 添加每条过敏记录
        for i, allergy in enumerate(self.drug_allergies):
            box = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(5),
                padding=[dp(15), 0, 0, 0]  # 左边添加更大的间距，使内容向右移动
            )

            # 添加序号和过敏信息
            info = Label(
                text=f"{i+1}. {allergy}",
                size_hint_x=0.8,
                color=self.theme.TEXT_PRIMARY,
                halign='left',
                valign='middle',
                text_size=(self.width * 0.75, None)  # 限制文本宽度，确保不会超出可见区域
            )

            # 添加删除按钮
            btn = Button(
                text="删除",
                size_hint_x=0.2,
                background_color=self.theme.ERROR_COLOR
            )
            btn.index = i  # 存储索引以便删除
            btn.bind(on_release=lambda btn: self._delete_allergy(btn.index))

            box.add_widget(info)
            box.add_widget(btn)
            self.ids.allergy_list_container.add_widget(box)

    def _delete_allergy(self, index):
        """删除指定索引的药物过敏记录"""
        if 0 <= index < len(self.drug_allergies):
            del self.drug_allergies[index]
            self._update_allergy_list()

    def add_gene_info(self, gene_info):
        """添加基因信息记录"""
        if not gene_info:
            self.show_error_dialog("请输入基因信息")
            return

        # 添加到基因列表
        self.gene_list.append(gene_info)

        # 清空输入框
        self.ids.gene_info.text = ""

        # 更新基因列表显示
        self._update_gene_list()

        # 显示添加成功提示
        self.show_error_dialog(f"已添加基因信息：{gene_info}")

    def _update_gene_list(self):
        """更新基因信息列表显示"""
        if not hasattr(self.ids, 'gene_list_container'):
            return

        # 清空当前列表
        self.ids.gene_list_container.clear_widgets()

        # 没有数据时显示提示
        if not self.gene_list:
            label = Label(
                text="暂无添加的基因信息记录",
                size_hint_y=None,
                height=dp(30),
                color=self.theme.TEXT_SECONDARY
            )
            self.ids.gene_list_container.add_widget(label)
            return

        # 添加标题
        title = Label(
            text="已添加的基因信息记录:",
            size_hint_y=None,
            height=dp(30),
            color=self.theme.TEXT_PRIMARY,
            halign='left',
            valign='middle',
            text_size=(self.width, None)
        )
        self.ids.gene_list_container.add_widget(title)

        # 添加每条基因信息
        for i, gene in enumerate(self.gene_list):
            box = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(5),
                padding=[dp(15), 0, 0, 0]  # 左边添加更大的间距，使内容向右移动
            )

            # 添加序号和基因信息
            info = Label(
                text=f"{i+1}. {gene}",
                size_hint_x=0.8,
                color=self.theme.TEXT_PRIMARY,
                halign='left',
                valign='middle',
                text_size=(self.width * 0.75, None)  # 限制文本宽度，确保不会超出可见区域
            )

            # 添加删除按钮
            btn = Button(
                text="删除",
                size_hint_x=0.2,
                background_color=self.theme.ERROR_COLOR
            )
            btn.index = i  # 存储索引以便删除
            btn.bind(on_release=lambda btn: self._delete_gene(btn.index))

            box.add_widget(info)
            box.add_widget(btn)
            self.ids.gene_list_container.add_widget(box)

    def _delete_gene(self, index):
        """删除指定索引的基因信息"""
        if 0 <= index < len(self.gene_list):
            del self.gene_list[index]
            self._update_gene_list()

    def add_family_disease(self, disease_name, relation):
        """添加家族疾病史记录"""
        if not disease_name:
            self.show_error_dialog("请输入疾病名称")
            return

        # 创建家族疾病记录
        disease_info = {
            'name': disease_name,
            'relation': relation
        }
        self.family_diseases.append(disease_info)

        # 清空输入框
        self.ids.family_disease_name.text = ''
        self.ids.family_relation.text = ''

        # 更新家族疾病列表显示
        self._update_family_disease_list()

        # 显示添加成功提示
        self.show_error_dialog(f"已添加家族疾病史: {disease_name}")

    def _update_family_disease_list(self):
        """更新家族疾病列表显示"""
        if not hasattr(self.ids, 'family_disease_container'):
            return

        # 清空当前列表
        self.ids.family_disease_container.clear_widgets()

        # 没有数据时显示提示
        if not self.family_diseases:
            label = Label(
                text="暂无添加的家族疾病记录",
                size_hint_y=None,
                height=dp(30),
                color=self.theme.TEXT_SECONDARY
            )
            self.ids.family_disease_container.add_widget(label)
            return

        # 添加标题
        title = Label(
            text="已添加的家族疾病记录:",
            size_hint_y=None,
            height=dp(30),
            color=self.theme.TEXT_PRIMARY,
            halign='left',
            valign='middle',
            text_size=(self.width, None)
        )
        self.ids.family_disease_container.add_widget(title)

        # 添加每条家族疾病记录
        for i, disease in enumerate(self.family_diseases):
            box = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(5),
                padding=[dp(15), 0, 0, 0]  # 左边添加更大的间距，使内容向右移动
            )

            # 添加序号和疾病信息
            info_text = f"{i+1}. {disease['name']}"
            if disease['relation']:
                info_text += f" | 家族关系: {disease['relation']}"

            info = Label(
                text=info_text,
                size_hint_x=0.8,
                color=self.theme.TEXT_PRIMARY,
                halign='left',
                valign='middle',
                text_size=(self.width * 0.75, None)  # 限制文本宽度，确保不会超出可见区域
            )

            # 添加删除按钮
            btn = Button(
                text="删除",
                size_hint_x=0.2,
                background_color=self.theme.ERROR_COLOR
            )
            btn.index = i  # 存储索引以便删除
            btn.bind(on_release=lambda btn: self._delete_family_disease(btn.index))

            box.add_widget(info)
            box.add_widget(btn)
            self.ids.family_disease_container.add_widget(box)

    def _delete_family_disease(self, index):
        """删除指定索引的家族疾病记录"""
        if 0 <= index < len(self.family_diseases):
            del self.family_diseases[index]
            self._update_family_disease_list()

    def submit_health_info(self):
        """提交健康信息"""
        try:
            # 首先尝试从用户管理器获取当前用户信息（推荐的方式）
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            # 测试用户ID - 如果无法获取真实用户ID，使用默认ID
            user_id = "default_user_id"

            # 优先从用户管理器获取用户ID，使用custom_id
            if current_user:
                # 优先使用custom_id
                if hasattr(current_user, 'custom_id') and current_user.custom_id:
                    user_id = current_user.custom_id
                    print(f"从用户管理器获取到custom_id: {user_id}")
                else:
                    user_id = current_user.user_id
                    print(f"从用户管理器获取到user_id: {user_id}")
            else:
                # 备用方案：从应用实例获取
                app = App.get_running_app()
                try:
                    # 使用更安全的方式访问user_data
                    user_data = getattr(app, 'user_data', {})
                    if isinstance(user_data, dict):
                        # 尝试各种可能的键，优先使用custom_id
                        if 'custom_id' in user_data:
                            user_id = user_data['custom_id']
                        elif 'id' in user_data:
                            user_id = user_data['id']
                        elif 'user_id' in user_data:
                            user_id = user_data['user_id']
                        elif 'username' in user_data:
                            user_id = user_data['username']

                    print(f"备用方案 - 用户数据: {user_data}")
                except Exception as e:
                    print(f"获取用户ID时出错: {str(e)}")

            print(f"最终使用的用户ID: {user_id}")

            # 获取健康信息
            health_info = {
                "user_id": user_id,
                "basic_info": self.ids.upload_basic_info.text,
                "diseases": self.diseases,
                "drug_allergies": self.drug_allergies,
                "gene_info": self.gene_list,
                "family_diseases": self.family_diseases,
                "blood_type": self.ids.blood_type.text,
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 保存到本地数据库
            from utils.storage import get_storage
            storage = get_storage()
            local_result = storage.save_health_info(health_info)

            # 同时尝试保存到后端服务器
            try:
                # 获取API客户端
                from api.api_client import APIClient
                api_client = APIClient()

                # 发送到后端服务器
                cloud_result = api_client.cloud_api.create_health_record(health_info)

                if cloud_result:
                    print(f"健康信息已成功同步到后端服务器: {cloud_result}")
                    # 显示上传成功提示
                    self.show_error_dialog("健康信息提交成功并已同步到云端！")
                else:
                    # 本地保存成功但云端同步失败
                    print(f"健康信息本地保存成功，但云端同步失败: {api_client.cloud_api.last_error}")
                    self.show_error_dialog("健康信息已保存到本地，但云端同步失败，将在下次联网时自动同步")
                    return
            except Exception as e:
                # 捕获云端同步异常
                print(f"云端同步异常: {str(e)}")
                if local_result.get('success', False):
                    # 本地保存成功
                    self.show_error_dialog("健康信息已保存到本地，但云端同步失败，将在下次联网时自动同步")
                    return

            # 如果本地保存成功
            if local_result.get('success', False):
                # 显示上传成功提示
                self.show_error_dialog("健康信息提交成功！")

                # 清空所有数据
                self.diseases = []
                self.drug_allergies = []
                self.gene_list = []
                self.family_diseases = []
                self.ids.upload_basic_info.text = ""
                self.ids.drug_allergy.text = ""
                self.ids.gene_info.text = ""
                self.ids.blood_type.text = "A型"

                # 清空显示列表
                self._update_disease_list()
                self._update_allergy_list()
                self._update_gene_list()
                self._update_family_disease_list()

                # 延迟返回上一级页面
                Clock.schedule_once(lambda dt: setattr(self.manager, 'current', 'homepage_screen'), 1)
            else:
                self.show_error_dialog(f"保存失败: {result.get('message', '未知错误')}")

        except Exception as e:
            print(f"提交健康信息时出错: {str(e)}")
            self.show_error_dialog(f"提交健康信息时出错: {str(e)}")