# screens/unit_screen.py
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, NumericProperty
from kivy.factory import Factory
from theme import AppTheme, AppMetrics, FontStyles, FontManager
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.graphics import RoundedRectangle, Color
# 导入KivyMD组件
from kivymd.uix.button import MDIconButton
from kivymd.uix.label import MDLabel
from kivy.uix.image import Image
from kivy.animation import Animation
import json
import os
import sys
import os.path as osp
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件

# 添加资源路径解析函数
def resource_path(relative_path):
    """解析相对资源路径为绝对路径"""
    base_path = osp.dirname(osp.dirname(osp.abspath(__file__)))  # 获取mobile目录路径
    return osp.join(base_path, relative_path)

# 确保图形元素在kv字符串中可用
Factory.register('RoundedRectangle', cls=RoundedRectangle)
Factory.register('Color', cls=Color)
Factory.register('MDIconButton', cls=MDIconButton)

# 定义单位管理者界面KV字符串
class UnitScreen(Screen):
    # 属性定义
    user_name = StringProperty("张三同志")  # 管理者真实姓名
    department = StringProperty("技术部")  # 管理者所在部门
    position = StringProperty("部门主管")  # 管理者职位
    
    # 当前查询的人员列表
    staff_list = ListProperty([
        {"name": "王五", "avatar": resource_path("assets/avatars/avatar1.png")},
        {"name": "李四", "avatar": resource_path("assets/avatars/avatar2.png")},
        {"name": "张三", "avatar": resource_path("assets/avatars/avatar3.png")}
    ])
    
    # 组织部门信息
    departments = ListProperty([
        {"name": "技术部", "count": 24},
        {"name": "市场部", "count": 18},
        {"name": "人事部", "count": 12}
    ])
    
    def __init__(self, **kwargs):
        super(UnitScreen, self).__init__(**kwargs)
        # 加载用户数据
        self.load_user_data()
    
    def load_user_data(self):
        """从本地存储加载用户数据"""
        try:
            # 尝试从data目录读取用户数据
            data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../data")
            user_data_file = os.path.join(data_dir, "user_data.json")
            
            if os.path.exists(user_data_file):
                with open(user_data_file, "r") as f:
                    data = json.load(f)
                    user_info = data.get("user_info", {})
                    
                    # 设置用户名称
                    if "name" in user_info:
                        self.user_name = user_info["name"]
        except Exception as e:
            print(f"加载用户数据失败: {str(e)}")
    
    def on_enter(self):
        """屏幕进入时调用"""
        # 在实际应用中，这里应该从API获取最新的组织和人员数据
        pass
    
    def navigate_to_data_collection(self):
        """导航到健康资料收集页面"""
        self.manager.current = "upload"
    
    def navigate_to_query(self):
        """导航到健康资料查询页面"""
        # 在实际应用中，这里应该跳转到查询页面
        pass
    
    def navigate_to_analysis(self):
        """导航到健康资料分析页面"""
        # 在实际应用中，这里应该跳转到分析页面
        pass
    
    def navigate_to_member_management(self):
        """导航到成员管理页面"""
        # 在实际应用中，这里应该跳转到成员管理页面
        pass
    
    def add_department(self):
        """添加新部门"""
        # 在实际应用中，这里应该打开添加部门的对话框
        pass
        
    def add_member(self):
        """添加新成员"""
        # 在实际应用中，这里应该打开添加成员的对话框
        pass

Builder.load_string("""
<UnitScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    BoxLayout:
        orientation: "vertical"
        padding: dp(app.metrics.PADDING_MEDIUM)
        spacing: dp(app.metrics.PADDING_NORMAL)
        
        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [dp(app.metrics.CORNER_RADIUS)]
            
            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo
                
            # 用户信息 - 放在Logo下方
            BoxLayout:
                orientation: "vertical"
                size_hint_x: None
                width: dp(200)
                padding: dp(app.metrics.PADDING_SMALL)
                pos_hint: {"right": 1}
                
                Label:
                    text: root.user_name
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_LIGHT
                    halign: "right"
                    text_size: self.size
        
        # 当前管理人员信息
        BoxLayout:
            size_hint_y: None
            height: dp(30)
            
            Label:
                text: "当前管理人员: " + root.user_name
                font_size: dp(app.metrics.FONT_SIZE_SMALL)
                color: app.theme.TEXT_SECONDARY
                halign: "left"
                valign: "middle"
                text_size: self.size
            
            Label:
                text: "正在查询: 王五 同志"
                font_size: dp(app.metrics.FONT_SIZE_SMALL)
                color: app.theme.TEXT_SECONDARY
                halign: "right"
                valign: "middle"
                text_size: self.size
        
        # 人员列表
        BoxLayout:
            size_hint_y: None
            height: dp(80)
            spacing: dp(10)
            padding: dp(5)
            
            # 人员头像和名称
            BoxLayout:
                orientation: "vertical"
                size_hint_x: None
                width: dp(60)
                
                MDIconButton:
                    icon: "account-circle"
                    theme_icon_color: "Custom"
                    icon_color: app.theme.PRIMARY_COLOR
                    icon_size: dp(50)
                    pos_hint: {"center_x": 0.5}
                
                Label:
                    text: "张三"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    size_hint_y: None
                    height: dp(20)
                    halign: "center"
            
            # 人员头像和名称
            BoxLayout:
                orientation: "vertical"
                size_hint_x: None
                width: dp(60)
                
                MDIconButton:
                    icon: "account-circle"
                    user_font_size: dp(50)
                    size_hint: None, None
                    size: dp(50), dp(50)
                    pos_hint: {"center_x": 0.5}
                
                Label:
                    text: "李四"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    size_hint_y: None
                    height: dp(20)
                    halign: "center"
            
            # 人员头像和名称
            BoxLayout:
                orientation: "vertical"
                size_hint_x: None
                width: dp(60)
                
                Image:
                    source: "mobile/assets/avatars/avatar3.png"  # 使用从项目根目录的路径
                    size_hint: None, None
                    size: dp(50), dp(50)
                    pos_hint: {"center_x": 0.5}
                
                Label:
                    text: "张三"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    size_hint_y: None
                    height: dp(20)
                    halign: "center"
            
            # 添加成员按钮
            BoxLayout:
                orientation: "vertical"
                size_hint_x: None
                width: dp(60)
                
                Button:
                    background_color: 0, 0, 0, 0
                    size_hint: None, None
                    size: dp(50), dp(50)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.add_member()
                    
                    canvas.before:
                        Color:
                            rgba: app.theme.PRIMARY_LIGHT
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(25)]
                    
                    Label:
                        text: "+"
                        font_size: dp(30)
                        color: app.theme.TEXT_SECONDARY
                        center: self.parent.center
                
                Label:
                    text: "添加成员"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    size_hint_y: None
                    height: dp(20)
                    halign: "center"
        
        # 二维码区域
        BoxLayout:
            size_hint_y: None
            height: dp(120)
            orientation: "vertical"
            padding: dp(10)
            
            MDIconButton:
                icon: "qrcode"
                user_font_size: dp(100)
                pos_hint: {"center_x": 0.5}
                size_hint: None, None
                size: dp(100), dp(100)
            
            Label:
                text: "扫描二维码添加成员"
                font_size: dp(app.metrics.FONT_SIZE_SMALL)
                color: app.theme.TEXT_SECONDARY
                size_hint_y: None
                height: dp(20)
                halign: "center"
        
        # 功能按钮区域
        GridLayout:
            cols: 2
            spacing: dp(10)
            size_hint_y: None
            height: dp(200)
            padding: dp(5)
            
            # 健康资料收集
            Button:
                background_color: 0, 0, 0, 0
                on_release: root.navigate_to_data_collection()
                
                canvas.before:
                    Color:
                        rgba: app.theme.PRIMARY_LIGHT
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(10)]
                
                BoxLayout:
                    orientation: "vertical"
                    pos: self.parent.pos
                    size: self.parent.size
                    padding: dp(10)
                    
                    MDIconButton:
                        icon: "folder"
                        user_font_size: dp(40)
                        pos_hint: {"center_x": 0.5}
                        size_hint: None, None
                        size: dp(40), dp(40)
                    
                    Label:
                        text: "健康资料收集"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: app.theme.TEXT_PRIMARY
                        halign: "center"
            
            # 健康资料查询
            Button:
                background_color: 0, 0, 0, 0
                on_release: root.navigate_to_query()
                
                canvas.before:
                    Color:
                        rgba: app.theme.PRIMARY_LIGHT
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(10)]
                
                BoxLayout:
                    orientation: "vertical"
                    pos: self.parent.pos
                    size: self.parent.size
                    padding: dp(10)
                    
                    MDIconButton:
                        icon: "magnify"
                        user_font_size: dp(40)
                        pos_hint: {"center_x": 0.5}
                        size_hint: None, None
                        size: dp(40), dp(40)
                    
                    Label:
                        text: "健康资料查询"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: app.theme.TEXT_PRIMARY
                        halign: "center"
            
            # 健康资料分析
            Button:
                background_color: 0, 0, 0, 0
                on_release: root.navigate_to_analysis()
                
                canvas.before:
                    Color:
                        rgba: app.theme.PRIMARY_LIGHT
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(10)]
                
                BoxLayout:
                    orientation: "vertical"
                    pos: self.parent.pos
                    size: self.parent.size
                    padding: dp(10)
                    
                    MDIconButton:
                        icon: "chart-bar"
                        user_font_size: dp(40)
                        pos_hint: {"center_x": 0.5}
                        size_hint: None, None
                        size: dp(40), dp(40)
                    
                    Label:
                        text: "健康资料分析"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: app.theme.TEXT_PRIMARY
                        halign: "center"
            
            # 成员管理
            Button:
                background_color: 0, 0, 0, 0
                on_release: root.navigate_to_member_management()
                
                canvas.before:
                    Color:
                        rgba: app.theme.PRIMARY_LIGHT
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(10)]
                
                BoxLayout:
                    orientation: "vertical"
                    pos: self.parent.pos
                    size: self.parent.size
                    padding: dp(10)
                    
                    MDIconButton:
                        icon: "account-group"
                        user_font_size: dp(40)
                        pos_hint: {"center_x": 0.5}
                        size_hint: None, None
                        size: dp(40), dp(40)
                    
                    Label:
                        text: "成员管理"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: app.theme.TEXT_PRIMARY
                        halign: "center"
        
        # 组织管理部分
        BoxLayout:
            orientation: "vertical"
            size_hint_y: None
            height: dp(200)
            spacing: dp(10)
            padding: dp(5)
            
            # 标题和添加部门按钮
            BoxLayout:
                size_hint_y: None
                height: dp(30)
                
                Label:
                    text: "组织管理"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    halign: "left"
                    valign: "middle"
                    text_size: self.size
                
                Button:
                    text: "+ 添加部门"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.PRIMARY_COLOR
                    background_color: 0, 0, 0, 0
                    size_hint_x: None
                    width: dp(100)
                    halign: "right"
                    on_release: root.add_department()
            
            # 部门列表
            ScrollView:
                do_scroll_x: False
                do_scroll_y: True
                
                GridLayout:
                    cols: 1
                    spacing: dp(5)
                    size_hint_y: None
                    height: self.minimum_height
                    
                    # 技术部
                    BoxLayout:
                        size_hint_y: None
                        height: dp(40)
                        padding: dp(10), dp(5)
                        canvas.before:
                            Color:
                                rgba: app.theme.PRIMARY_LIGHT
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [dp(5)]
                        
                        Label:
                            text: "技术部"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_PRIMARY
                            halign: "left"
                            valign: "middle"
                            text_size: self.size
                        
                        Label:
                            text: "24人"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "right"
                            valign: "middle"
                            text_size: self.size
                            size_hint_x: None
                            width: dp(50)
                    
                    # 市场部
                    BoxLayout:
                        size_hint_y: None
                        height: dp(40)
                        padding: dp(10), dp(5)
                        canvas.before:
                            Color:
                                rgba: app.theme.PRIMARY_LIGHT
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [dp(5)]
                        
                        Label:
                            text: "市场部"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_PRIMARY
                            halign: "left"
                            valign: "middle"
                            text_size: self.size
                        
                        Label:
                            text: "18人"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "right"
                            valign: "middle"
                            text_size: self.size
                            size_hint_x: None
                            width: dp(50)
                    
                    # 人事部
                    BoxLayout:
                        size_hint_y: None
                        height: dp(40)
                        padding: dp(10), dp(5)
                        canvas.before:
                            Color:
                                rgba: app.theme.PRIMARY_LIGHT
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [dp(5)]
                        
                        Label:
                            text: "人事部"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_PRIMARY
                            halign: "left"
                            valign: "middle"
                            text_size: self.size
                        
                        Label:
                            text: "12人"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "right"
                            valign: "middle"
                            text_size: self.size
                            size_hint_x: None
                            width: dp(50)
""")