import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())
print("Files in current directory:")
for f in os.listdir('.'):
    if f.endswith('.py'):
        print(f"  {f}")

try:
    import utils.screen_loader
    print("Screen loader import: SUCCESS")
except Exception as e:
    print(f"Screen loader import: FAILED - {e}")

try:
    import utils.memory_utils
    print("Memory utils import: SUCCESS")
except Exception as e:
    print(f"Memory utils import: FAILED - {e}")

print("Test completed.")
