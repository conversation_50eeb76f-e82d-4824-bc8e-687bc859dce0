#!/usr/bin/env python3
"""
调试评估量表数据格式
"""

import requests
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_assessment_detail():
    """调试评估量表详情数据格式"""
    base_url = "http://8.138.188.26:80/api"
    assessment_id = 14  # 使用日志中的ID
    url = f"{base_url}/assessments/{assessment_id}"
    
    logger.info(f"调试API: {url}")
    
    try:
        # 设置代理禁用
        proxies = {
            'http': None,
            'https': None
        }
        
        # 发送请求
        response = requests.get(url, timeout=10, proxies=proxies)
        logger.info(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"响应数据类型: {type(data)}")
                
                # 打印完整的响应数据结构
                print("\n=== 完整响应数据 ===")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                # 分析数据结构
                print("\n=== 数据结构分析 ===")
                if isinstance(data, dict):
                    print(f"顶级字段: {list(data.keys())}")
                    
                    # 检查questions字段
                    if 'questions' in data:
                        questions = data['questions']
                        print(f"questions类型: {type(questions)}")
                        print(f"questions数量: {len(questions) if questions else 0}")
                        
                        if questions and len(questions) > 0:
                            first_question = questions[0]
                            print(f"第一个题目字段: {list(first_question.keys()) if isinstance(first_question, dict) else '非字典类型'}")
                            print(f"第一个题目内容: {json.dumps(first_question, ensure_ascii=False, indent=2)}")
                    
                    # 检查其他可能的字段
                    for key in ['items', 'scale_items', 'question_items', 'content']:
                        if key in data:
                            print(f"发现字段 '{key}': {type(data[key])}")
                            if isinstance(data[key], list) and data[key]:
                                print(f"  第一个元素: {json.dumps(data[key][0], ensure_ascii=False, indent=2)}")
                
                return True
                
            except json.JSONDecodeError:
                logger.error("✗ 响应不是有效的JSON格式")
                logger.error(f"响应内容: {response.text[:500]}...")
                return False
                
        elif response.status_code == 403:
            logger.info("✓ 端点存在但需要认证 (403)")
            return True
        elif response.status_code == 401:
            logger.info("✓ 端点存在但需要认证 (401)")
            return True
        elif response.status_code == 404:
            logger.error("✗ 端点不存在 (404)")
            return False
        else:
            logger.warning(f"? 其他状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        logger.error("✗ 连接超时")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("✗ 连接错误")
        return False
    except Exception as e:
        logger.error(f"✗ 请求失败: {str(e)}")
        return False

def debug_assessment_list():
    """调试评估量表列表数据格式"""
    base_url = "http://8.138.188.26:80/api"
    url = f"{base_url}/assessments"
    
    logger.info(f"调试列表API: {url}")
    
    try:
        proxies = {
            'http': None,
            'https': None
        }
        
        response = requests.get(url, timeout=10, proxies=proxies)
        logger.info(f"列表状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"\n=== 量表列表数据 ===")
                print(f"数据类型: {type(data)}")
                
                if isinstance(data, list):
                    print(f"列表长度: {len(data)}")
                    if data:
                        print(f"第一个量表字段: {list(data[0].keys()) if isinstance(data[0], dict) else '非字典类型'}")
                        print(f"第一个量表: {json.dumps(data[0], ensure_ascii=False, indent=2)}")
                
                return True
                
            except json.JSONDecodeError:
                logger.error("列表响应不是有效的JSON")
                return False
        else:
            logger.info(f"列表请求状态码: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"列表请求失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始调试评估量表数据格式...")
    
    # 调试量表列表
    debug_assessment_list()
    
    # 调试量表详情
    debug_assessment_detail()
    
    print("\n=== 调试总结 ===")
    print("请检查上面的数据结构，确定:")
    print("1. 题目数据在哪个字段中（questions, items, scale_items等）")
    print("2. 选项数据在哪个字段中（options, choices, answers等）")
    print("3. 数据的具体格式和字段名称")

if __name__ == "__main__":
    main()
