# theme.py
from kivy.utils import get_color_from_hex
from kivy.metrics import dp, sp

# 定义应用的主题颜色基类
class BaseTheme:
    # 主色调
    PRIMARY_COLOR = get_color_from_hex('#3F51B5')  # 靛蓝色
    PRIMARY_DARK = get_color_from_hex('#303F9F')   # 深靛蓝色
    PRIMARY_LIGHT = get_color_from_hex('#e5f2ff')  # 浅蓝色
    PRIMARY_MEDIUM = get_color_from_hex('#9FA8DA') # 中靛蓝色，用于复选框和输入框
    
    # 强调色
    ACCENT_COLOR = get_color_from_hex('#FF4081')   # 粉色
    ACCENT_DARK = get_color_from_hex('#C2185B')    # 深粉色
    ACCENT_LIGHT = get_color_from_hex('#F8BBD0')   # 浅粉色
    
    # 背景色
    BACKGROUND_COLOR = get_color_from_hex('#E6E6FA')  # 浅紫色
    CARD_BACKGROUND = get_color_from_hex('#FFFFFF')   # 白色
    INPUT_BACKGROUND = get_color_from_hex('#E8EAF6')  # 输入框背景色
    SURFACE_COLOR = get_color_from_hex('#FFFFFF')     # 表面颜色
    DIVIDER_COLOR = get_color_from_hex('#BDBDBD')     # 分隔线颜色
    SHADOW_COLOR = get_color_from_hex('#000000')      # 阴影颜色，带透明度
    
    # 文本颜色
    TEXT_PRIMARY = get_color_from_hex('#212121')      # 深灰色
    TEXT_SECONDARY = get_color_from_hex('#757575')    # 中灰色
    TEXT_HINT = get_color_from_hex('#BDBDBD')         # 浅灰色
    TEXT_LIGHT = get_color_from_hex('#FFFFFF')        # 白色
    TEXT_LINK = get_color_from_hex('#1976D2')         # 链接文本颜色
    TEXT_EMPHASIS = get_color_from_hex('#673AB7')     # 强调文本颜色
    TEXT_DISABLED = get_color_from_hex('#9E9E9E')     # 禁用文本颜色
    
    # 状态颜色
    SUCCESS_COLOR = get_color_from_hex('#4CAF50')    # 绿色
    WARNING_COLOR = get_color_from_hex('#FFC107')    # 黄色
    ERROR_COLOR = get_color_from_hex('#F44336')      # 红色
    DANGER_COLOR = get_color_from_hex('#F44336')     # 红色（与ERROR_COLOR相同）
    INFO_COLOR = get_color_from_hex('#2196F3')       # 蓝色

# 日间模式主题（默认主题）
class LightTheme(BaseTheme):
    # 背景色 - 日间模式特有
    BACKGROUND_COLOR = get_color_from_hex('#F5F5F5')  # 浅灰色
    CARD_BACKGROUND = get_color_from_hex('#FFFFFF')   # 白色
    SURFACE_COLOR = get_color_from_hex('#FFFFFF')     # 表面颜色
    ELEVATED_SURFACE = get_color_from_hex('#FAFAFA')  # 浮起表面颜色
    BACKGROUND_LIGHT = get_color_from_hex('#F8F8F8')  # 超浅灰色，用于输入框背景
    
    # 文本颜色 - 日间模式特有
    TEXT_PRIMARY = get_color_from_hex('#212121')      # 深灰色
    TEXT_SECONDARY = get_color_from_hex('#757575')    # 中灰色
    TEXT_HINT = get_color_from_hex('#9E9E9E')         # 灰色

# 夜间模式主题
class DarkTheme(BaseTheme):
    # 背景色 - 夜间模式特有
    BACKGROUND_COLOR = get_color_from_hex('#121212')  # 深黑色
    CARD_BACKGROUND = get_color_from_hex('#1E1E1E')   # 深灰色
    SURFACE_COLOR = get_color_from_hex('#242424')     # 表面颜色
    ELEVATED_SURFACE = get_color_from_hex('#2C2C2C')  # 浮起表面颜色
    INPUT_BACKGROUND = get_color_from_hex('#333333')  # 输入框背景色
    DIVIDER_COLOR = get_color_from_hex('#424242')     # 分隔线颜色
    
    # 文本颜色 - 夜间模式特有
    TEXT_PRIMARY = get_color_from_hex('#FFFFFF')      # 白色
    TEXT_SECONDARY = get_color_from_hex('#B0B0B0')    # 浅灰色
    TEXT_HINT = get_color_from_hex('#707070')         # 中灰色
    TEXT_LIGHT = get_color_from_hex('#FFFFFF')        # 白色
    TEXT_LINK = get_color_from_hex('#90CAF9')         # 浅蓝色链接
    TEXT_EMPHASIS = get_color_from_hex('#BB86FC')     # 浅紫色强调

# 高对比度主题
class HighContrastTheme(BaseTheme):
    # 主色调 - 高对比度
    PRIMARY_COLOR = get_color_from_hex('#000000')     # 黑色
    PRIMARY_DARK = get_color_from_hex('#000000')      # 黑色
    PRIMARY_LIGHT = get_color_from_hex('#FFFFFF')     # 白色
    
    # 强调色 - 高对比度
    ACCENT_COLOR = get_color_from_hex('#FFFF00')      # 亮黄色
    
    # 背景色 - 高对比度
    BACKGROUND_COLOR = get_color_from_hex('#FFFFFF')  # 白色
    CARD_BACKGROUND = get_color_from_hex('#FFFFFF')   # 白色
    
    # 文本颜色 - 高对比度
    TEXT_PRIMARY = get_color_from_hex('#000000')      # 黑色
    TEXT_SECONDARY = get_color_from_hex('#000000')    # 黑色
    TEXT_HINT = get_color_from_hex('#000000')         # 黑色
    TEXT_LINK = get_color_from_hex('#0000FF')         # 蓝色
    
    # 状态颜色 - 高对比度
    SUCCESS_COLOR = get_color_from_hex('#008000')     # 绿色
    WARNING_COLOR = get_color_from_hex('#FF8000')     # 橙色
    ERROR_COLOR = get_color_from_hex('#FF0000')       # 红色

# 默认主题（向后兼容）
class AppTheme(LightTheme):
    pass

# 应用指标类 - 增强版
class AppMetrics:
    """应用程序指标定义，提供统一的尺寸、间距和布局规则"""
    
    # 字体大小 - 使用dp单位使其在不同设备上保持一致
    FONT_SIZE_XL = dp(24)       # 特大号字体，如主标题
    FONT_SIZE_LARGE = dp(20)    # 大号字体，如副标题
    FONT_SIZE_MEDIUM = dp(18)   # 中号字体，如普通文本
    FONT_SIZE_NORMAL = dp(16)   # 正常字体，如按钮文本
    FONT_SIZE_SMALL = dp(14)    # 小号字体，如辅助文本
    FONT_SIZE_XS = dp(12)       # 超小号字体，如脚注
    
    # 字体
    FONT_FAMILY = "NotoSansSC"
    
    # 基础间距 - 用于组件内部和组件之间的间距
    SPACING_XL = dp(32)         # 特大间距，用于屏幕主要部分之间
    SPACING_LARGE = dp(24)      # 大间距，用于相关组件组之间
    SPACING_MEDIUM = dp(16)     # 中等间距，用于卡片之间
    SPACING_NORMAL = dp(12)     # 正常间距，用于相关元素之间
    SPACING_SMALL = dp(8)       # 小间距，用于列表项内部元素
    SPACING_XS = dp(4)          # 超小间距，用于紧凑布局
    
    # 内边距 - 用于组件内部填充
    PADDING_XL = dp(24)         # 特大内边距，用于屏幕边缘
    PADDING_LARGE = dp(20)      # 大内边距，用于卡片和面板
    PADDING_MEDIUM = dp(16)     # 中等内边距，用于组件块
    PADDING_NORMAL = dp(12)     # 正常内边距，用于组件内部
    PADDING_SMALL = dp(8)       # 小内边距，用于紧凑组件
    PADDING_XS = dp(4)          # 超小内边距，用于最小组件
    
    # 组件高度预设
    HEIGHT_XL = dp(64)          # 特大高度，用于顶部栏
    HEIGHT_LARGE = dp(56)       # 大高度，用于主要按钮
    HEIGHT_MEDIUM = dp(48)      # 中等高度，用于输入框和普通按钮
    HEIGHT_NORMAL = dp(40)      # 正常高度，用于次要按钮
    HEIGHT_SMALL = dp(32)       # 小高度，用于辅助按钮
    HEIGHT_XS = dp(24)          # 超小高度，用于紧凑型按钮和标签
    
    # 控件尺寸
    BUTTON_HEIGHT_PRIMARY = dp(48)     # 主要按钮高度
    BUTTON_HEIGHT_SECONDARY = dp(40)   # 次要按钮高度
    BUTTON_WIDTH = dp(200)             # 标准按钮宽度
    INPUT_HEIGHT = dp(48)              # 输入框高度
    ICON_SIZE_LARGE = dp(32)           # 大图标尺寸
    ICON_SIZE_MEDIUM = dp(24)          # 中图标尺寸
    ICON_SIZE_SMALL = dp(18)           # 小图标尺寸
    
    # 卡片和容器尺寸
    CARD_ELEVATION = dp(1)             # 卡片阴影高度
    CARD_CORNER_RADIUS = dp(16)        # 卡片圆角半径
    BUTTON_CORNER_RADIUS = dp(8)       # 按钮圆角半径
    CORNER_RADIUS = dp(8)             # 兼容旧代码中的圆角半径 (为向后兼容)
    
    # 导航栏和顶部栏
    NAVBAR_HEIGHT = dp(56)             # 底部导航栏高度
    STATUSBAR_HEIGHT = dp(24)          # 状态栏高度（安卓）
    APPBAR_HEIGHT = dp(56)             # 应用栏高度
    
    # 列表和网格
    LIST_ITEM_HEIGHT = dp(56)          # 列表项高度
    GRID_SPACING = dp(16)              # 网格间距
    
    # 屏幕尺寸 - 5.7英寸屏幕
    SCREEN_WIDTH = 360  # 典型的5.7英寸手机宽度（dp）
    SCREEN_HEIGHT = 640  # 典型的5.7英寸手机高度（dp）
    
    # 响应式断点
    BREAKPOINT_SMALL = dp(600)         # 小屏幕断点（手机）
    BREAKPOINT_MEDIUM = dp(905)        # 中屏幕断点（平板）
    BREAKPOINT_LARGE = dp(1240)        # 大屏幕断点（桌面）
    
    # 表单元素
    FORM_FIELD_SPACING = dp(16)        # 表单字段之间的间距
    FORM_GROUP_SPACING = dp(24)        # 表单组之间的间距
    CHECKBOX_SIZE = dp(20)             # 复选框大小
    
    # 动画时间
    ANIMATION_DURATION_SHORT = 0.2     # 短动画时长（秒）
    ANIMATION_DURATION_MEDIUM = 0.3    # 中等动画时长（秒）
    ANIMATION_DURATION_LONG = 0.5      # 长动画时长（秒）

    @staticmethod
    def get_responsive_width(screen_width, percent=0.9):
        """返回响应式宽度，基于屏幕宽度的百分比"""
        return screen_width * percent
    
    @staticmethod
    def get_responsive_padding(screen_width):
        """基于屏幕宽度返回合适的内边距"""
        if screen_width <= AppMetrics.BREAKPOINT_SMALL:
            return dp(16)  # 小屏幕使用较小内边距
        elif screen_width <= AppMetrics.BREAKPOINT_MEDIUM:
            return dp(24)  # 中等屏幕使用中等内边距
        else:
            return dp(32)  # 大屏幕使用较大内边距

# 字体管理器
class FontManager:
    """字体管理器，集中管理应用中使用的所有字体"""
    
    # 字体文件映射
    FONT_FILES = {
        "regular": "NotoSansSC-Regular.ttf",
        "medium": "NotoSansSC-Medium.ttf",
        "light": "NotoSansSC-Light.ttf",
        "semibold": "NotoSansSC-SemiBold.ttf",
        "bold": "NotoSansSC-Bold.ttf",  # 可能不存在，会有备选方案
        "black": "NotoSansSC-Black.ttf",
        "extrabold": "NotoSansSC-ExtraBold.ttf",
        "extralight": "NotoSansSC-ExtraLight.ttf",
        "thin": "NotoSansSC-Thin.ttf"
    }
    
    # 字体名称映射
    FONT_NAMES = {
        "regular": "NotoSans",
        "medium": "NotoSansMedium",
        "light": "NotoSansLight",
        "semibold": "NotoSansSemiBold",
        "bold": "NotoSansBold",
        "black": "NotoSansBlack",
        "extrabold": "NotoSansExtraBold",
        "extralight": "NotoSansExtraLight",
        "thin": "NotoSansThin"
    }
    
    # 默认字体
    DEFAULT_FONT = "NotoSans"
    DEFAULT_FONT_FILE = "NotoSansSC-Regular.ttf"
    
    # 备选字体
    FALLBACK_FONT = "msyh.ttf"
    
    # 字体路径
    @staticmethod
    def get_font_path(weight="regular"):
        """获取指定字重的字体路径
        
        Args:
            weight: 字体字重，可选值：regular, medium, light, semibold, bold, black, extrabold, extralight, thin
            
        Returns:
            字体路径
        """
        from os.path import join
        from kivy.app import App
        app = App.get_running_app()
        if not app:
            # 在没有运行应用程序的情况下，返回相对路径
            return join("assets", "fonts", FontManager.FONT_FILES.get(weight, FontManager.DEFAULT_FONT_FILE))
        
        # 根据应用程序的资源路径获取字体路径
        return join(app.resource_path, "assets", "fonts", FontManager.FONT_FILES.get(weight, FontManager.DEFAULT_FONT_FILE))
    
    @staticmethod
    def get_font_name(weight="regular"):
        """获取指定字重的字体名称
        
        Args:
            weight: 字体字重，可选值：regular, medium, light, semibold, bold, black, extrabold, extralight, thin
            
        Returns:
            字体名称
        """
        return FontManager.FONT_NAMES.get(weight, FontManager.DEFAULT_FONT)

# 基础字体配置类 - 新增
class BaseFontConfig:
    """基础字体配置类
    
    这个类定义了应用中所有文本元素的基础字体配置，包括字体大小、行高和字重。
    作为FontStyles和KivyMDFontStyles的基础，确保整个应用的字体配置一致。
    """
    
    # 标题字体大小和行高
    TITLE_LARGE_SIZE = dp(22)
    TITLE_LARGE_LINE_HEIGHT = 1.6
    
    TITLE_MEDIUM_SIZE = dp(16)
    TITLE_MEDIUM_LINE_HEIGHT = 1.5
    
    TITLE_SMALL_SIZE = dp(14)
    TITLE_SMALL_LINE_HEIGHT = 1.4
    
    # 副标题字体大小和行高
    SUBTITLE_LARGE_SIZE = dp(16)
    SUBTITLE_LARGE_LINE_HEIGHT = 1.5
    
    SUBTITLE_MEDIUM_SIZE = dp(14)
    SUBTITLE_MEDIUM_LINE_HEIGHT = 1.4
    
    SUBTITLE_SMALL_SIZE = dp(12)
    SUBTITLE_SMALL_LINE_HEIGHT = 1.3
    
    # 正文字体大小和行高
    BODY_LARGE_SIZE = dp(16)
    BODY_LARGE_LINE_HEIGHT = 1.5
    
    BODY_MEDIUM_SIZE = dp(14)
    BODY_MEDIUM_LINE_HEIGHT = 1.4
    
    BODY_SMALL_SIZE = dp(12)
    BODY_SMALL_LINE_HEIGHT = 1.3
    
    # 按钮字体大小和行高
    BUTTON_LARGE_SIZE = dp(16)
    BUTTON_LARGE_LINE_HEIGHT = 1.5
    
    BUTTON_MEDIUM_SIZE = dp(14)
    BUTTON_MEDIUM_LINE_HEIGHT = 1.4
    
    BUTTON_SMALL_SIZE = dp(12)
    BUTTON_SMALL_LINE_HEIGHT = 1.3
    
    # 标签字体大小和行高
    LABEL_LARGE_SIZE = dp(14)
    LABEL_LARGE_LINE_HEIGHT = 1.4
    
    LABEL_MEDIUM_SIZE = dp(12)
    LABEL_MEDIUM_LINE_HEIGHT = 1.3
    
    LABEL_SMALL_SIZE = dp(10)
    LABEL_SMALL_LINE_HEIGHT = 1.2
    
    # 提示文本字体大小和行高
    HINT_TEXT_SIZE = dp(12)
    HINT_TEXT_LINE_HEIGHT = 1.3
    
    # 错误文本字体大小和行高
    ERROR_TEXT_SIZE = dp(12)
    ERROR_TEXT_LINE_HEIGHT = 1.3
    
    # 链接文本字体大小和行高
    LINK_TEXT_SIZE = dp(14)
    LINK_TEXT_LINE_HEIGHT = 1.4
    
    # 标题字体大小和行高
    HEADLINE_LARGE_SIZE = dp(32)
    HEADLINE_LARGE_LINE_HEIGHT = 1.7
    
    HEADLINE_MEDIUM_SIZE = dp(28)
    HEADLINE_MEDIUM_LINE_HEIGHT = 1.6
    
    HEADLINE_SMALL_SIZE = dp(24)
    HEADLINE_SMALL_LINE_HEIGHT = 1.5
    
    # 大标题字体大小和行高
    DISPLAY_LARGE_SIZE = dp(57)
    DISPLAY_LARGE_LINE_HEIGHT = 1.8
    
    DISPLAY_MEDIUM_SIZE = dp(45)
    DISPLAY_MEDIUM_LINE_HEIGHT = 1.7
    
    DISPLAY_SMALL_SIZE = dp(36)
    DISPLAY_SMALL_LINE_HEIGHT = 1.6

# Kivy字体样式类 - 修改为使用BaseFontConfig
class FontStyles:
    """Kivy字体样式定义，提供统一的Kivy文本元素样式设置
    
    这个类定义了应用中所有常规Kivy文本元素的标准字体样式，如Label, Button等。
    样式格式适用于标准Kivy组件，通过普通字典格式提供样式配置。
    主要用于非KivyMD组件的样式配置。
    """
    
    # 标题样式
    TITLE_LARGE = {
        'font_size': BaseFontConfig.TITLE_LARGE_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': True
    }
    
    TITLE_MEDIUM = {
        'font_size': BaseFontConfig.TITLE_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': True
    }
    
    TITLE_SMALL = {
        'font_size': BaseFontConfig.TITLE_SMALL_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': True
    }
    
    # 副标题样式
    SUBTITLE_LARGE = {
        'font_size': BaseFontConfig.SUBTITLE_LARGE_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (1, 1, 1, 0.9),  # 白色，轻微透明
        'bold': False
    }
    
    SUBTITLE_MEDIUM = {
        'font_size': BaseFontConfig.SUBTITLE_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (1, 1, 1, 0.9),  # 白色，轻微透明
        'bold': False
    }
    
    SUBTITLE_SMALL = {
        'font_size': BaseFontConfig.SUBTITLE_SMALL_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (1, 1, 1, 0.9),  # 白色，轻微透明
        'bold': False
    }
    
    # 正文样式
    BODY_LARGE = {
        'font_size': BaseFontConfig.BODY_LARGE_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': False
    }
    
    BODY_MEDIUM = {
        'font_size': BaseFontConfig.BODY_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': False
    }
    
    BODY_SMALL = {
        'font_size': BaseFontConfig.BODY_SMALL_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': False
    }
    
    # 按钮样式
    BUTTON_LARGE = {
        'font_size': BaseFontConfig.BUTTON_LARGE_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0.7, 0.5, 0.6, 1),  # 紫色
        'bold': False
    }
    
    BUTTON_MEDIUM = {
        'font_size': BaseFontConfig.BUTTON_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (1, 1, 1, 1),  # 白色
        'bold': False
    }
    
    BUTTON_SMALL = {
        'font_size': BaseFontConfig.BUTTON_SMALL_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (1, 1, 1, 1),  # 白色
        'bold': False
    }
    
    # 标签样式
    LABEL_LARGE = {
        'font_size': BaseFontConfig.LABEL_LARGE_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 0.87),  # 黑色，轻微透明
        'bold': False
    }
    
    LABEL_MEDIUM = {
        'font_size': BaseFontConfig.LABEL_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 0.87),  # 黑色，轻微透明
        'bold': False
    }
    
    LABEL_SMALL = {
        'font_size': BaseFontConfig.LABEL_SMALL_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 0.87),  # 黑色，轻微透明
        'bold': False
    }
    
    # 提示文本样式
    HINT_TEXT = {
        'font_size': BaseFontConfig.HINT_TEXT_SIZE,
        'font_name': FontManager.get_font_name('light'),
        'color': (0, 0, 0, 0.6),  # 黑色，更透明
        'bold': False
    }
    
    # 错误文本样式
    ERROR_TEXT = {
        'font_size': BaseFontConfig.ERROR_TEXT_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0.96, 0.26, 0.21, 1),  # 红色
        'bold': False
    }
    
    # 链接文本样式
    LINK_TEXT = {
        'font_size': BaseFontConfig.LINK_TEXT_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0.1, 0.46, 0.82, 1),  # 蓝色
        'bold': False,
        'underline': True
    }
    
    # Logo标题样式
    LOGO_TITLE = {
        'font_size': sp(32),  # 使用 sp 单位
        'font_name': FontManager.get_font_name('bold'),
        'bold': True,
        'color': AppTheme.PRIMARY_COLOR
    }
    
    # Logo副标题样式
    LOGO_SUBTITLE = {
        'font_size': sp(14),  # 使用 sp 单位
        'font_name': FontManager.get_font_name('regular'),
        'bold': False,
        'color': AppTheme.TEXT_SECONDARY
    }
    
    @staticmethod
    def get_style_for_theme(style_dict, theme):
        """根据当前主题调整样式
        
        Args:
            style_dict: 样式字典
            theme: 当前主题
            
        Returns:
            调整后的样式字典
        """
        # 创建样式字典的副本，避免修改原始样式
        adjusted_style = style_dict.copy()
        
        # 根据主题调整文本颜色
        if isinstance(theme, DarkTheme):
            # 如果原始颜色接近黑色，则在暗色主题中改为白色
            if style_dict.get('color', (0, 0, 0, 1))[0:3] == (0, 0, 0):
                adjusted_style['color'] = (1, 1, 1, style_dict.get('color', (0, 0, 0, 1))[3])
        elif isinstance(theme, LightTheme):
            # 如果原始颜色接近白色，则在亮色主题中改为黑色
            if style_dict.get('color', (0, 0, 0, 1))[0:3] == (1, 1, 1):
                adjusted_style['color'] = (0, 0, 0, style_dict.get('color', (1, 1, 1, 1))[3])
        
        return adjusted_style

# KivyMD字体样式类 - 修改为使用BaseFontConfig
class KivyMDFontStyles:
    """KivyMD字体样式定义，提供统一的KivyMD字体样式设置
    
    这个类定义了应用中所有KivyMD文本元素的标准字体样式，包括Body、Label、Headline、Display、Title等。
    样式格式特别适配KivyMD的要求，通过特定的键名和格式提供样式配置。
    主要用于KivyMD组件的样式配置，如MDLabel、MDButton等。
    """
    
    @staticmethod
    def get_font_styles(sp_func):
        """获取KivyMD字体样式配置
        
        Args:
            sp_func: sp函数，用于设置字体大小
            
        Returns:
            KivyMD字体样式配置字典
        """
        return {
            "Icon": {
                "large": {
                    "font-name": "Icons",
                    "font-size": sp_func(24),
                    "line-height": 1.2
                },
                "medium": {
                    "font-name": "Icons",
                    "font-size": sp_func(20),
                    "line-height": 1.2
                },
                "small": {
                    "font-name": "Icons",
                    "font-size": sp_func(16),
                    "line-height": 1.2
                }
            },
            "Body": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.BODY_LARGE_SIZE),
                    "line-height": BaseFontConfig.BODY_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.BODY_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.BODY_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.BODY_SMALL_SIZE),
                    "line-height": BaseFontConfig.BODY_SMALL_LINE_HEIGHT
                },
            },
            
            "Label": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.LABEL_LARGE_SIZE),
                    "line-height": BaseFontConfig.LABEL_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.LABEL_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.LABEL_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.LABEL_SMALL_SIZE),
                    "line-height": BaseFontConfig.LABEL_SMALL_LINE_HEIGHT
                },
            },
            
            "Headline": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.HEADLINE_LARGE_SIZE),
                    "line-height": BaseFontConfig.HEADLINE_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.HEADLINE_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.HEADLINE_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.HEADLINE_SMALL_SIZE),
                    "line-height": BaseFontConfig.HEADLINE_SMALL_LINE_HEIGHT
                },
            },
            
            "Display": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.DISPLAY_LARGE_SIZE),
                    "line-height": BaseFontConfig.DISPLAY_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.DISPLAY_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.DISPLAY_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.DISPLAY_SMALL_SIZE),
                    "line-height": BaseFontConfig.DISPLAY_SMALL_LINE_HEIGHT
                },
            },
            
            "Title": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.TITLE_LARGE_SIZE),
                    "line-height": BaseFontConfig.TITLE_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.TITLE_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.TITLE_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.TITLE_SMALL_SIZE),
                    "line-height": BaseFontConfig.TITLE_SMALL_LINE_HEIGHT
                },
            }
        }

# 主题管理器
class ThemeManager:
    @staticmethod
    def get_theme(theme_name='light'):
        """获取指定的主题
        
        Args:
            theme_name: 主题名称，可选值：'light', 'dark', 'high_contrast'
            
        Returns:
            对应的主题类
        """
        themes = {
            'light': LightTheme,
            'dark': DarkTheme,
            'high_contrast': HighContrastTheme
        }
        return themes.get(theme_name, LightTheme)