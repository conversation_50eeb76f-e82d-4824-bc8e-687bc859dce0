"""家庭成员管理模块
该模块提供家庭成员的管理功能，包括添加、删除、查询家庭成员等。
"""

import os
import json
from datetime import datetime

# 应用程序路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)

# 家庭成员数据文件路径
FAMILY_DATA_FILE = os.path.join(APP_DIR, "user_family_members.json")

# 家庭成员关系类型
RELATIONSHIP_TYPES = [
    "配偶", "父亲", "母亲", "儿子", "女儿", "兄弟", "姐妹", 
    "祖父", "祖母", "外祖父", "外祖母", "孙子", "孙女", 
    "外孙", "外孙女", "叔叔", "阿姨", "其他"
]

class FamilyMember:
    """家庭成员类"""
    def __init__(self, member_id, name, relationship, user_code=None, added_time=None):
        self.member_id = member_id  # 成员ID
        self.name = name            # 成员姓名
        self.relationship = relationship  # 与用户的关系
        self.user_code = user_code  # 成员的用户编码（如果有）
        self.added_time = added_time or datetime.now().isoformat()  # 添加时间
    
    def to_dict(self):
        """转换为字典"""
        return {
            "member_id": self.member_id,
            "name": self.name,
            "relationship": self.relationship,
            "user_code": self.user_code,
            "added_time": self.added_time
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建成员对象"""
        return cls(
            member_id=data.get("member_id"),
            name=data.get("name"),
            relationship=data.get("relationship"),
            user_code=data.get("user_code"),
            added_time=data.get("added_time")
        )

class FamilyManager:
    """家庭成员管理器"""
    def __init__(self, user_id):
        self.user_id = user_id  # 当前用户ID
        self.members = []       # 家庭成员列表
        self.load_members()     # 加载成员数据
    
    def load_members(self):
        """加载家庭成员数据"""
        try:
            if os.path.exists(FAMILY_DATA_FILE):
                with open(FAMILY_DATA_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 获取当前用户的家庭成员
                user_data = data.get(self.user_id, {})
                members_data = user_data.get("members", [])
                
                # 转换为成员对象
                self.members = [FamilyMember.from_dict(m) for m in members_data]
            else:
                self.members = []
        except Exception as e:
            print(f"加载家庭成员数据失败: {e}")
            self.members = []
    
    def save_members(self):
        """保存家庭成员数据"""
        try:
            # 读取现有数据
            all_data = {}
            if os.path.exists(FAMILY_DATA_FILE):
                try:
                    with open(FAMILY_DATA_FILE, 'r', encoding='utf-8') as f:
                        all_data = json.load(f)
                except:
                    all_data = {}
            
            # 更新当前用户的数据
            members_data = [m.to_dict() for m in self.members]
            all_data[self.user_id] = {"members": members_data}
            
            # 保存数据
            with open(FAMILY_DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存家庭成员数据失败: {e}")
            return False
    
    def add_member(self, name, relationship, user_code=None):
        """添加家庭成员"""
        # 生成成员ID
        member_id = f"FM{len(self.members) + 1:03d}"
        
        # 创建成员对象
        member = FamilyMember(
            member_id=member_id,
            name=name,
            relationship=relationship,
            user_code=user_code
        )
        
        # 添加到列表
        self.members.append(member)
        
        # 保存数据
        self.save_members()
        
        return member
    
    def remove_member(self, member_id):
        """删除家庭成员"""
        for i, member in enumerate(self.members):
            if member.member_id == member_id:
                del self.members[i]
                self.save_members()
                return True
        
        return False
    
    def get_member(self, member_id):
        """获取指定成员"""
        for member in self.members:
            if member.member_id == member_id:
                return member
        
        return None
    
    def get_all_members(self):
        """获取所有成员"""
        return self.members
    
    def update_member(self, member_id, name=None, relationship=None):
        """更新成员信息"""
        member = self.get_member(member_id)
        if not member:
            return False
        
        if name:
            member.name = name
        if relationship:
            member.relationship = relationship
        
        self.save_members()
        return True

# 测试代码
if __name__ == "__main__":
    # 创建家庭管理器
    manager = FamilyManager("user123")
    
    # 添加成员
    manager.add_member("李四", "父亲")
    manager.add_member("王五", "母亲")
    
    # 获取所有成员
    members = manager.get_all_members()
    for member in members:
        print(f"成员ID: {member.member_id}, 姓名: {member.name}, 关系: {member.relationship}")