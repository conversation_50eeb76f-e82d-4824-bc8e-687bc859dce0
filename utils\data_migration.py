import os
import json
import logging
import shutil
import sqlite3
import zipfile
from datetime import datetime
from .database import DatabaseManager

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('data_migration')

class DataMigrationTool:
    """数据迁移工具类，用于备份和恢复数据库"""
    
    def __init__(self, db_manager=None):
        """初始化迁移工具
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
        self.backup_dir = os.path.join(os.path.dirname(self.db_manager.db_dir), 'backups')
        
        # 确保备份目录存在
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def backup_user_data(self, user_id):
        """备份用户数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 备份文件路径
        """
        try:
            # 检查用户数据库是否存在
            user_db_path = self.db_manager.get_user_db_path(user_id)
            if not os.path.exists(user_db_path):
                logger.error(f"用户数据库不存在: {user_db_path}")
                return None
            
            # 创建备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = os.path.join(self.backup_dir, f"user_{user_id}_backup_{timestamp}.zip")
            
            # 创建临时目录
            temp_dir = os.path.join(self.backup_dir, 'temp')
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            
            # 复制数据库文件到临时目录
            db_copy = os.path.join(temp_dir, 'user.db')
            shutil.copy2(user_db_path, db_copy)
            
            # 导出用户元数据
            metadata = {
                'user_id': user_id,
                'backup_time': datetime.now().isoformat(),
                'app_version': '1.0.0',
                'schema_version': '1'
            }
            
            with open(os.path.join(temp_dir, 'metadata.json'), 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # 创建ZIP文件
            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arcname)
            
            # 清理临时目录
            shutil.rmtree(temp_dir)
            
            logger.info(f"已成功备份用户 {user_id} 的数据到 {backup_file}")
            return backup_file
            
        except Exception as e:
            logger.error(f"备份用户 {user_id} 数据时出错: {str(e)}")
            return None
    
    def restore_user_data(self, backup_file, target_user_id=None):
        """从备份文件恢复用户数据
        
        Args:
            backup_file: 备份文件路径
            target_user_id: 目标用户ID，默认从备份文件中读取
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建临时目录
            temp_dir = os.path.join(self.backup_dir, 'temp_restore')
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            
            # 解压备份文件
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(temp_dir)
            
            # 读取元数据
            metadata_file = os.path.join(temp_dir, 'metadata.json')
            if not os.path.exists(metadata_file):
                logger.error(f"备份文件不包含元数据: {backup_file}")
                shutil.rmtree(temp_dir)
                return False
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 获取用户ID
            user_id = target_user_id or metadata.get('user_id')
            if not user_id:
                logger.error("无法确定目标用户ID")
                shutil.rmtree(temp_dir)
                return False
            
            # 验证备份文件中的数据库文件存在
            db_file = os.path.join(temp_dir, 'user.db')
            if not os.path.exists(db_file):
                logger.error(f"备份文件不包含数据库: {backup_file}")
                shutil.rmtree(temp_dir)
                return False
            
            # 检查数据库文件是否有效
            try:
                conn = sqlite3.connect(db_file)
                cur = conn.cursor()
                cur.execute("PRAGMA integrity_check")
                result = cur.fetchone()
                conn.close()
                
                if result[0] != 'ok':
                    logger.error(f"备份数据库文件已损坏: {db_file}")
                    shutil.rmtree(temp_dir)
                    return False
            except Exception as e:
                logger.error(f"验证备份数据库时出错: {str(e)}")
                shutil.rmtree(temp_dir)
                return False
            
            # 获取目标用户数据库路径
            target_db_path = self.db_manager.get_user_db_path(user_id)
            
            # 如果用户数据库已存在，先备份
            if os.path.exists(target_db_path):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_before_restore = os.path.join(
                    self.backup_dir, 
                    f"user_{user_id}_before_restore_{timestamp}.db"
                )
                shutil.copy2(target_db_path, backup_before_restore)
                logger.info(f"在恢复前已备份原有数据库到 {backup_before_restore}")
            
            # 确保目标目录存在
            target_dir = os.path.dirname(target_db_path)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
            
            # 复制数据库文件到目标位置
            shutil.copy2(db_file, target_db_path)
            
            # 更新用户数据库映射
            self.db_manager.load_user_db_map()
            
            # 清理临时目录
            shutil.rmtree(temp_dir)
            
            logger.info(f"已成功从 {backup_file} 恢复用户 {user_id} 的数据")
            return True
            
        except Exception as e:
            logger.error(f"恢复用户数据时出错: {str(e)}")
            return False
    
    def export_data_as_json(self, user_id, output_file=None):
        """将用户数据导出为JSON格式
        
        Args:
            user_id: 用户ID
            output_file: 输出文件路径，默认自动生成
            
        Returns:
            str: 输出文件路径
        """
        try:
            # 检查用户数据库是否存在
            user_db_path = self.db_manager.get_user_db_path(user_id)
            if not os.path.exists(user_db_path):
                logger.error(f"用户数据库不存在: {user_db_path}")
                return None
            
            # 创建输出文件路径
            if not output_file:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = os.path.join(self.backup_dir, f"user_{user_id}_export_{timestamp}.json")
            
            # 连接数据库
            conn = sqlite3.connect(user_db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
            
            # 导出数据
            data = {
                'metadata': {
                    'user_id': user_id,
                    'export_time': datetime.now().isoformat(),
                    'app_version': '1.0.0',
                    'schema_version': '1'
                },
                'tables': {}
            }
            
            for table in tables:
                cursor.execute(f"SELECT * FROM {table}")
                rows = cursor.fetchall()
                
                # 获取列名
                columns = [column[0] for column in cursor.description]
                
                # 构建数据
                table_data = []
                for row in rows:
                    row_dict = {columns[i]: row[i] for i in range(len(columns))}
                    table_data.append(row_dict)
                
                data['tables'][table] = table_data
            
            # 关闭连接
            conn.close()
            
            # 写入JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已成功导出用户 {user_id} 的数据到 {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"导出用户 {user_id} 数据为JSON时出错: {str(e)}")
            return None
    
    def import_data_from_json(self, json_file, target_user_id=None):
        """从JSON文件导入用户数据
        
        Args:
            json_file: JSON文件路径
            target_user_id: 目标用户ID，默认从JSON文件中读取
            
        Returns:
            bool: 是否成功
        """
        try:
            # 读取JSON文件
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证JSON格式
            if 'metadata' not in data or 'tables' not in data:
                logger.error(f"JSON文件格式无效: {json_file}")
                return False
            
            # 获取用户ID
            user_id = target_user_id or data['metadata'].get('user_id')
            if not user_id:
                logger.error("无法确定目标用户ID")
                return False
            
            # 获取用户数据库路径
            user_db_path = self.db_manager.get_user_db_path(user_id)
            
            # 如果用户数据库已存在，先备份
            if os.path.exists(user_db_path):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_before_import = os.path.join(
                    self.backup_dir, 
                    f"user_{user_id}_before_import_{timestamp}.db"
                )
                shutil.copy2(user_db_path, backup_before_import)
                logger.info(f"在导入前已备份原有数据库到 {backup_before_import}")
            
            # 确保目标目录存在
            target_dir = os.path.dirname(user_db_path)
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
            
            # 创建新数据库
            conn = sqlite3.connect(user_db_path)
            cursor = conn.cursor()
            
            # 导入数据
            for table_name, table_data in data['tables'].items():
                if not table_data:
                    continue
                
                # 获取列名
                columns = list(table_data[0].keys())
                
                # 创建表
                columns_def = ', '.join([f"{col} TEXT" for col in columns])
                cursor.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({columns_def})")
                
                # 插入数据
                placeholders = ', '.join(['?'] * len(columns))
                insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                
                for row in table_data:
                    values = [row.get(col) for col in columns]
                    cursor.execute(insert_sql, values)
            
            # 提交并关闭
            conn.commit()
            conn.close()
            
            # 更新用户数据库映射
            self.db_manager.load_user_db_map()
            
            logger.info(f"已成功从 {json_file} 导入数据到用户 {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"从JSON导入用户数据时出错: {str(e)}")
            return False
    
    def list_backups(self, user_id=None):
        """列出可用的备份文件
        
        Args:
            user_id: 用户ID，如果提供则仅列出该用户的备份
            
        Returns:
            list: 备份文件信息列表
        """
        try:
            backups = []
            
            # 遍历备份目录
            for filename in os.listdir(self.backup_dir):
                file_path = os.path.join(self.backup_dir, filename)
                
                # 只处理ZIP文件
                if not filename.endswith('.zip') or not os.path.isfile(file_path):
                    continue
                
                # 如果指定了用户ID，检查文件名是否匹配
                if user_id and f"user_{user_id}_backup_" not in filename:
                    continue
                
                # 提取文件信息
                file_info = {
                    'filename': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'creation_time': datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                }
                
                # 尝试从文件名中提取用户ID和备份时间
                parts = filename.split('_')
                if len(parts) >= 4 and parts[0] == 'user' and parts[2] == 'backup':
                    file_info['user_id'] = parts[1]
                    try:
                        # 从文件名中提取时间戳
                        time_str = parts[3].split('.')[0]
                        file_info['backup_time'] = datetime.strptime(
                            time_str, '%Y%m%d_%H%M%S'
                        ).isoformat()
                    except:
                        pass
                
                backups.append(file_info)
            
            # 按创建时间排序
            backups.sort(key=lambda x: x['creation_time'], reverse=True)
            return backups
            
        except Exception as e:
            logger.error(f"列出备份文件时出错: {str(e)}")
            return []
    
    def delete_backup(self, backup_file):
        """删除备份文件
        
        Args:
            backup_file: 备份文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(backup_file):
                logger.error(f"备份文件不存在: {backup_file}")
                return False
            
            # 删除文件
            os.remove(backup_file)
            logger.info(f"已删除备份文件: {backup_file}")
            return True
            
        except Exception as e:
            logger.error(f"删除备份文件时出错: {str(e)}")
            return False
    
    def get_backup_info(self, backup_file):
        """获取备份文件的详细信息
        
        Args:
            backup_file: 备份文件路径
            
        Returns:
            dict: 备份文件信息
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(backup_file):
                logger.error(f"备份文件不存在: {backup_file}")
                return None
            
            # 创建临时目录
            temp_dir = os.path.join(self.backup_dir, 'temp_info')
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            
            # 解压元数据文件
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # 列出所有文件
                file_list = zipf.namelist()
                
                # 提取元数据
                if 'metadata.json' in file_list:
                    zipf.extract('metadata.json', temp_dir)
                    
                    with open(os.path.join(temp_dir, 'metadata.json'), 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                else:
                    metadata = {}
            
            # 获取文件信息
            file_info = {
                'filename': os.path.basename(backup_file),
                'path': backup_file,
                'size': os.path.getsize(backup_file),
                'creation_time': datetime.fromtimestamp(os.path.getctime(backup_file)).isoformat(),
                'metadata': metadata,
                'files': file_list
            }
            
            # 清理临时目录
            shutil.rmtree(temp_dir)
            
            return file_info
            
        except Exception as e:
            logger.error(f"获取备份文件信息时出错: {str(e)}")
            return None

# 单例模式
_migration_tool_instance = None

def get_migration_tool(db_manager=None):
    """获取数据迁移工具实例（单例模式）"""
    global _migration_tool_instance
    if _migration_tool_instance is None:
        _migration_tool_instance = DataMigrationTool(db_manager)
    return _migration_tool_instance 