import os
import shutil
import time
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional

# 配置日志
logger = logging.getLogger(__name__)

class FileManager:
    """文件管理器类，用于处理应用中的文件操作"""
    
    def __init__(self):
        """初始化文件管理器"""
        # 基础目录
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 临时文件目录
        self.temp_dir = os.path.join(self.base_dir, "temp")
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
            
        # 上传文件目录
        self.upload_dir = os.path.join(self.base_dir, "uploads")
        if not os.path.exists(self.upload_dir):
            os.makedirs(self.upload_dir)
            
        # 文档目录
        self.documents_dir = os.path.join(self.upload_dir, "documents")
        if not os.path.exists(self.documents_dir):
            os.makedirs(self.documents_dir)
            
        # 图片目录
        self.images_dir = os.path.join(self.upload_dir, "images")
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)
    
    def save_uploaded_file(self, file_path: str, user_id: str, file_type: Optional[str] = None) -> Dict[str, Any]:
        """保存上传的文件到用户目录
        
        Args:
            file_path: 原始文件路径
            user_id: 用户ID
            file_type: 文件类型（可选，如果为None则自动检测）
            
        Returns:
            Dict: 包含保存结果的字典
        """
        try:
            # 获取文件名和扩展名
            file_name = os.path.basename(file_path)
            _, ext = os.path.splitext(file_name)
            ext = ext.lower()
            
            # 如果未指定文件类型，根据扩展名判断
            if not file_type:
                if ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx']:
                    file_type = 'document'
                elif ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
                    file_type = 'image'
                else:
                    file_type = 'other'
            
            # 创建用户目录
            user_dir = os.path.join(self.upload_dir, user_id)
            if not os.path.exists(user_dir):
                os.makedirs(user_dir)
                
            # 根据文件类型选择目标目录
            if file_type == 'document':
                target_dir = os.path.join(user_dir, 'documents')
            elif file_type == 'image':
                target_dir = os.path.join(user_dir, 'images')
            else:
                target_dir = os.path.join(user_dir, 'other')
                
            # 确保目标目录存在
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)
                
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_name = f"{timestamp}_{file_name}"
            target_path = os.path.join(target_dir, unique_name)
            
            # 复制文件
            shutil.copy2(file_path, target_path)
            
            # 返回结果
            return {
                'success': True,
                'original_path': file_path,
                'saved_path': target_path,
                'file_name': unique_name,
                'file_type': file_type,
                'user_id': user_id,
                'timestamp': timestamp
            }
            
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'original_path': file_path
            }
    
    def list_user_files(self, user_id: str, file_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出用户的文件
        
        Args:
            user_id: 用户ID
            file_type: 文件类型筛选（可选）
            
        Returns:
            List[Dict]: 文件列表
        """
        try:
            # 用户目录
            user_dir = os.path.join(self.upload_dir, user_id)
            if not os.path.exists(user_dir):
                return []
                
            # 根据文件类型确定查找目录
            if file_type == 'document':
                search_dir = os.path.join(user_dir, 'documents')
            elif file_type == 'image':
                search_dir = os.path.join(user_dir, 'images')
            elif file_type == 'other':
                search_dir = os.path.join(user_dir, 'other')
            else:
                # 如果没有指定类型，则搜索所有目录
                return (
                    self.list_user_files(user_id, 'document') +
                    self.list_user_files(user_id, 'image') +
                    self.list_user_files(user_id, 'other')
                )
                
            # 检查目录是否存在
            if not os.path.exists(search_dir):
                return []
                
            # 获取文件列表
            files = []
            for filename in os.listdir(search_dir):
                file_path = os.path.join(search_dir, filename)
                if os.path.isfile(file_path):
                    # 获取文件信息
                    file_info = {
                        'name': filename,
                        'path': file_path,
                        'type': file_type,
                        'size': os.path.getsize(file_path),
                        'created': datetime.fromtimestamp(os.path.getctime(file_path)).strftime("%Y-%m-%d %H:%M:%S"),
                        'modified': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y-%m-%d %H:%M:%S")
                    }
                    files.append(file_info)
                    
            # 按修改时间降序排序
            files.sort(key=lambda x: x['modified'], reverse=True)
            return files
            
        except Exception as e:
            logger.error(f"列出用户文件失败: {str(e)}")
            return []
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否删除成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")
            return False
    
    def create_temp_file(self, content: str, file_name: Optional[str] = None, ext: str = '.txt') -> Optional[str]:
        """创建临时文件
        
        Args:
            content: 文件内容
            file_name: 自定义文件名（可选）
            ext: 文件扩展名
            
        Returns:
            Optional[str]: 临时文件路径，失败则返回None
        """
        try:
            # 生成临时文件名
            if not file_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"temp_{timestamp}{ext}"
                
            # 临时文件路径
            temp_path = os.path.join(self.temp_dir, file_name)
            
            # 写入内容
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
            return temp_path
            
        except Exception as e:
            logger.error(f"创建临时文件失败: {str(e)}")
            return None
    
    def clean_temp_files(self, max_age_hours: int = 24) -> int:
        """清理临时文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
            
        Returns:
            int: 清理的文件数量
        """
        try:
            count = 0
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            # 遍历临时目录
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                if os.path.isfile(file_path):
                    # 检查文件年龄
                    file_modified = os.path.getmtime(file_path)
                    if current_time - file_modified > max_age_seconds:
                        # 删除过期文件
                        os.remove(file_path)
                        count += 1
                        
            return count
            
        except Exception as e:
            logger.error(f"清理临时文件失败: {str(e)}")
            return 0

# 单例模式
_file_manager = None

def get_file_manager():
    """获取文件管理器实例"""
    global _file_manager
    if _file_manager is None:
        _file_manager = FileManager()
    return _file_manager 