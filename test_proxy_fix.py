#!/usr/bin/env python3
"""
测试代理修复是否有效
"""

import os
import sys
import requests
import logging

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_proxy_environment():
    """测试代理环境变量"""
    print("=== 代理环境变量检查 ===")
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy',
        'ALL_PROXY', 'all_proxy'
    ]
    
    found_proxies = {}
    for var in proxy_vars:
        if var in os.environ:
            found_proxies[var] = os.environ[var]
            print(f"发现代理变量: {var} = {os.environ[var]}")
    
    if not found_proxies:
        print("未发现代理环境变量")
    
    return found_proxies

def test_proxy_config_module():
    """测试代理配置模块"""
    print("\n=== 测试代理配置模块 ===")
    try:
        from utils.proxy_config import check_proxy_status, disable_proxy_globally
        
        # 检查当前状态
        status = check_proxy_status()
        print(f"代理状态: {status}")
        
        # 禁用代理
        disable_proxy_globally()
        
        # 再次检查
        status_after = check_proxy_status()
        print(f"禁用后状态: {status_after}")
        
        return status_after['has_proxy_vars']
        
    except Exception as e:
        print(f"代理配置模块测试失败: {e}")
        return True

def test_requests_with_proxy_disabled():
    """测试禁用代理的requests请求"""
    print("\n=== 测试禁用代理的requests请求 ===")
    
    # 测试URL
    test_url = "http://8.138.188.26:80/api/health"
    
    try:
        # 明确禁用代理的请求
        response = requests.get(
            test_url,
            timeout=10,
            proxies={
                'http': None,
                'https': None
            }
        )
        print(f"请求成功: {response.status_code}")
        return True
        
    except requests.exceptions.ProxyError as e:
        print(f"代理错误: {e}")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: {e}")
        return False
    except Exception as e:
        print(f"其他错误: {e}")
        return False

def test_local_api_client():
    """测试本地API客户端"""
    print("\n=== 测试本地API客户端 ===")
    
    try:
        from utils.local_api_client import LocalApiClient
        
        client = LocalApiClient()
        
        # 测试健康检查
        health_result = client.check_server_health()
        print(f"健康检查结果: {health_result}")
        
        return health_result.get('any_server_online', False)
        
    except Exception as e:
        print(f"本地API客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始代理修复测试...")
    
    # 1. 检查环境变量
    has_proxy_env = bool(test_proxy_environment())
    
    # 2. 测试代理配置模块
    has_proxy_after_disable = test_proxy_config_module()
    
    # 3. 测试requests请求
    requests_success = test_requests_with_proxy_disabled()
    
    # 4. 测试本地API客户端
    api_client_success = test_local_api_client()
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"初始代理环境变量: {'有' if has_proxy_env else '无'}")
    print(f"禁用后仍有代理变量: {'是' if has_proxy_after_disable else '否'}")
    print(f"requests请求成功: {'是' if requests_success else '否'}")
    print(f"API客户端连接成功: {'是' if api_client_success else '否'}")
    
    if not has_proxy_after_disable and (requests_success or api_client_success):
        print("✅ 代理修复成功！")
        return True
    else:
        print("❌ 代理修复失败，仍有连接问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
