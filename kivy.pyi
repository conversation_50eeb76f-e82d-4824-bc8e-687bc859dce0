"""Kivy类型存根文件，解决类型检查问题"""
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Kivy常用组件的类型定义

class Widget:
    """基础Widget类型存根"""
    parent: Any
    children: List[Any]
    
    def __init__(self, **kwargs: Any) -> None: ...
    def add_widget(self, widget: Any, index: int = 0, canvas: Any = None) -> None: ...
    def remove_widget(self, widget: Any) -> None: ...
    def bind(self, **kwargs: Callable) -> None: ...
    def unbind(self, **kwargs: Callable) -> None: ...
    
class Button(Widget):
    """Button类型存根"""
    text: str
    background_normal: str
    background_down: str
    background_color: List[float]
    color: List[float]
    
    def bind(self, **kwargs: Callable) -> None: ...
    
class Label(Widget):
    """Label类型存根"""
    text: str
    font_name: str
    font_size: Union[float, int]
    color: List[float]
    
class SpinnerOption(Button):
    """SpinnerOption类型存根"""
    def _on_release(self, *args: Any) -> None: ...
    
class BoxLayout(Widget):
    """BoxLayout类型存根"""
    orientation: str
    padding: Union[List[int], int]
    spacing: Union[int, float]
    
class ModalView(Widget):
    """ModalView类型存根"""
    size_hint: Tuple[float, float]
    auto_dismiss: bool
    
    def open(self) -> None: ...
    def dismiss(self) -> None: ...
    
class Image(Widget):
    """Image类型存根"""
    source: str
    texture: Any
    
    def save(self, filename: str) -> bool: ...
    
class Camera(Image):
    """Camera类型存根"""
    play: bool
    resolution: Tuple[int, int]
    
    def export_to_png(self, filename: str) -> None: ...

# KivyMD组件类型定义
class ThemeManager:
    """KivyMD主题管理器类型存根"""
    primaryColor: List[float]
    primary_palette: str
    theme_style: str
    
class MDApp:
    """MDApp类型存根"""
    theme_cls: ThemeManager
    
    @staticmethod
    def get_running_app() -> 'MDApp': ...

class MDDropdownMenu:
    """MDDropdownMenu类型存根"""
    def __init__(self, caller: Any, items: List[Dict[str, Any]], width: float) -> None: ...
    def open(self) -> None: ...
    def dismiss(self) -> None: ... 