"""
性能监控工具
提供应用性能监控和分析功能
"""
import time
import logging
import threading
from functools import wraps

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self._metrics = {}
        self._lock = threading.Lock()
        self._start_time = time.time()
    
    def record_metric(self, name, value, unit='ms'):
        """
        记录性能指标
        
        Args:
            name: 指标名称
            value: 指标值
            unit: 单位
        """
        with self._lock:
            if name not in self._metrics:
                self._metrics[name] = []
            
            self._metrics[name].append({
                'value': value,
                'timestamp': time.time(),
                'unit': unit
            })
    
    def get_metrics(self):
        """获取所有性能指标"""
        with self._lock:
            return self._metrics.copy()
    
    def get_average(self, name):
        """获取指标的平均值"""
        with self._lock:
            if name not in self._metrics or not self._metrics[name]:
                return None
            
            values = [m['value'] for m in self._metrics[name]]
            return sum(values) / len(values)
    
    def clear_metrics(self):
        """清除所有指标"""
        with self._lock:
            self._metrics.clear()
    
    def get_uptime(self):
        """获取应用运行时间"""
        return time.time() - self._start_time

def timing_decorator(metric_name):
    """
    性能计时装饰器
    
    Args:
        metric_name: 指标名称
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                duration = (end_time - start_time) * 1000  # 转换为毫秒
                get_performance_monitor().record_metric(metric_name, duration)
                
                # 如果执行时间超过100ms，记录警告
                if duration > 100:
                    logger.warning(f"{metric_name} 执行时间较长: {duration:.2f}ms")
        
        return wrapper
    return decorator

class StartupTimer:
    """启动时间计时器"""
    
    def __init__(self):
        self._phases = {}
        self._current_phase = None
        self._app_start_time = time.time()
    
    def start_phase(self, phase_name):
        """开始一个启动阶段"""
        current_time = time.time()
        
        # 结束当前阶段
        if self._current_phase:
            self._phases[self._current_phase]['end_time'] = current_time
            self._phases[self._current_phase]['duration'] = (
                current_time - self._phases[self._current_phase]['start_time']
            ) * 1000
        
        # 开始新阶段
        self._phases[phase_name] = {
            'start_time': current_time,
            'end_time': None,
            'duration': None
        }
        self._current_phase = phase_name
        
        logger.info(f"启动阶段开始: {phase_name}")
    
    def end_phase(self, phase_name=None):
        """结束启动阶段"""
        if phase_name is None:
            phase_name = self._current_phase
        
        if phase_name and phase_name in self._phases:
            current_time = time.time()
            self._phases[phase_name]['end_time'] = current_time
            self._phases[phase_name]['duration'] = (
                current_time - self._phases[phase_name]['start_time']
            ) * 1000
            
            logger.info(f"启动阶段完成: {phase_name} ({self._phases[phase_name]['duration']:.2f}ms)")
            
            if phase_name == self._current_phase:
                self._current_phase = None
    
    def get_total_startup_time(self):
        """获取总启动时间"""
        return (time.time() - self._app_start_time) * 1000
    
    def get_phase_summary(self):
        """获取启动阶段摘要"""
        summary = {
            'total_time': self.get_total_startup_time(),
            'phases': {}
        }
        
        for phase_name, phase_data in self._phases.items():
            summary['phases'][phase_name] = {
                'duration': phase_data['duration'],
                'percentage': (phase_data['duration'] / summary['total_time'] * 100) if phase_data['duration'] else 0
            }
        
        return summary

# 全局实例
_performance_monitor = None
_startup_timer = None

def get_performance_monitor():
    """获取全局性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def get_startup_timer():
    """获取全局启动计时器实例"""
    global _startup_timer
    if _startup_timer is None:
        _startup_timer = StartupTimer()
    return _startup_timer

def log_performance_summary():
    """记录性能摘要"""
    try:
        monitor = get_performance_monitor()
        timer = get_startup_timer()
        
        # 记录启动时间摘要
        startup_summary = timer.get_phase_summary()
        logger.info("=== 启动性能摘要 ===")
        logger.info(f"总启动时间: {startup_summary['total_time']:.2f}ms")
        
        for phase_name, phase_info in startup_summary['phases'].items():
            if phase_info['duration']:
                logger.info(f"  {phase_name}: {phase_info['duration']:.2f}ms ({phase_info['percentage']:.1f}%)")
        
        # 记录运行时性能指标
        metrics = monitor.get_metrics()
        if metrics:
            logger.info("=== 运行时性能指标 ===")
            for metric_name, metric_data in metrics.items():
                if metric_data:
                    avg_value = sum(m['value'] for m in metric_data) / len(metric_data)
                    logger.info(f"  {metric_name}: 平均 {avg_value:.2f}ms (共 {len(metric_data)} 次)")
        
        logger.info(f"应用运行时间: {monitor.get_uptime():.2f}秒")
        
    except Exception as e:
        logger.error(f"记录性能摘要时出错: {e}")

def schedule_performance_logging():
    """调度定期性能日志记录"""
    try:
        from kivy.clock import Clock
        
        def _log_performance(dt):
            """定期性能日志记录回调"""
            log_performance_summary()
        
        # 每10分钟记录一次性能摘要
        Clock.schedule_interval(_log_performance, 600)
        logger.info("已启动定期性能日志记录")
        
    except ImportError:
        logger.warning("无法导入Clock模块，跳过定期性能日志记录")
    except Exception as e:
        logger.error(f"调度性能日志记录时出错: {e}")
